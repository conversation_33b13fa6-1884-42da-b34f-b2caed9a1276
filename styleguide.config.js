const path = require('path');
const reactDocgenTypescript = require('react-docgen-typescript');

const options = {
  propFilter: (prop) => {
    if (prop.parent) {
      return !prop.parent.fileName.includes('node_modules');
    }
    return true;
  },
};

module.exports = {
  require: [path.resolve(__dirname, 'styleguide/setup.js')],
  styleguideDir: 'docs',
  template: {
    head: {
      links: [
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap',
        },
      ],
    },
  },
  theme: {
    color: {
      link: '#00008f',
      linkHover: '#00006D',
    },
    fontFamily: {
      base: 'Noto Sans JP',
    },
  },
  styles: {
    TableOfContents: {
      root: {
        'background-color': '#FFFFFF',
      },
    },
    StyleGuide: {
      '@global body': {
        fontFamily: 'Noto Sans JP',
      },
      logo: {
        'background-color': '#00008f',
        'border-bottom': '2px solid #FFFFFF',
      },
    },
    Logo: {
      logo: {
        color: '#FFFFFF',
      },
    },
    Link: {
      link: {
        '&, &:link, &:visited': {
          textDecoration: 'underline',
        },
      },
    },
  },
  sections: [
    {
      name: 'Introduction',
      content: 'styleguide/introduction.md',
    },
    {
      name: 'Component Request',
      content: 'styleguide/ComponentRequestPage/ComponentRequestPage.md',
    },
    {
      name: 'Styles / Tokens',
      sections: [
        {
          name: 'Breakpoints',
          content: 'src/Breakpoints/Breakpoints.md',
        },
        {
          name: 'Colors',
          content: 'styleguide/Colors.md',
        },
      ],
    },
    {
      name: 'Assets',
      href: '/#/Assets/Divider',
      components: ['src/Divider/Divider.tsx', 'src/Icons/Icon.tsx', 'src/Logo/Logo.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Buttons',
      href: '/#/Buttons/Button',
      components: ['src/Button/Button.tsx', 'src/IconButton/IconButton.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Containers',
      href: '/#/Containers/Accordion',
      components: ['src/Accordion/Accordion.tsx', 'src/FlowList/FlowList.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Data',
      href: '/#/Data/DataList',
      components: ['src/DataList/DataList.tsx', 'src/DataTable/DataTable.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Feedback',
      href: '/#/Feedback/Alert',
      components: ['src/Alert/Alert.tsx', 'src/Badge/Badge.tsx', 'src/Loader/Loader.tsx', 'src/Message/Message.tsx', 'src/Stepper/Stepper.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Forms',
      href: '/#/Forms/CheckboxGroupField',
      components: [
        'src/CheckboxGroupField/CheckboxGroupField.tsx',
        'src/CheckboxTileGroupField/CheckboxTileGroupField.tsx',
        'src/DatePickerField/DatePickerField.tsx',
        'src/RadioGroupField/RadioGroupField.tsx',
        'src/RadioTileGroupField/RadioTileGroupField.tsx',
        'src/PasswordField/PasswordField.tsx',
        'src/SelectField/SelectField.tsx',
        'src/TextField/TextField.tsx',
        'src/TextAreaField/TextAreaField.tsx',
        'src/UploadField/UploadField.tsx',
      ],
      sectionDepth: 1,
    },
    {
      name: 'Global Elements',
      href: '/#/Global Elements/Footer',
      components: ['src/Footer/Footer.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Navigation',
      href: '/#/Navigation/Breadcrumb',
      components: ['src/Breadcrumb/Breadcrumb.tsx', 'src/Link/Link.tsx', 'src/Pagination/Pagination.tsx', 'src/Tabs/Tabs.tsx'],
      sectionDepth: 1,
    },
    {
      name: 'Overlay',
      href: '/#/Overlay/Lightbox',
      components: [
        'src/Lightbox/Lightbox.tsx',
        'src/Modal/Modal.tsx',
        'src/Popover/Popover.tsx',
        'src/StatusModal/StatusModal.tsx',
        'src/Toast/Toast.tsx',
      ],
      sectionDepth: 1,
    },
    {
      name: 'Typography',
      href: '/#/Typography/Heading',
      components: ['src/Heading/Heading.tsx', 'src/Text/Text.tsx'],
      sectionDepth: 1,
    },
  ],
  pagePerSection: true,
  components: ['src/**/*.tsx', 'src/**/*.tsx', 'styleguide/**/*.tsx'],
  ignore: ['**/ColorSwatch.tsx'],
  skipComponentsWithoutExample: true,
  propsParser: reactDocgenTypescript.withCustomConfig(`${process.cwd()}/tsconfig.json`, options).parse,
  // Override Styleguidist components
  styleguideComponents: {
    Wrapper: path.join(__dirname, 'styleguide/components/Wrapper'),
    StyleGuideRenderer: path.join(__dirname, 'styleguide/components/StyleGuide'),
    SectionHeading: path.join(__dirname, 'styleguide/components/SectionHeading'),
  },
  webpackConfig: {
    devServer: {
      historyApiFallback: true,
    },
    resolve: {
      alias: {
        '@axa-japan/design-system-react': path.join(__dirname, './src'),
        styleguide: path.join(__dirname, './styleguide'),
      },
    },
    module: {
      rules: [
        {
          test: /\.[jt]sx?$/,
          exclude: /node_modules/,
          loader: 'esbuild-loader',
          include: [path.resolve(__dirname, './src'), path.resolve(__dirname, './styleguide')],
        },
        {
          test: /\.(png|jpg|jpeg|gif)$/,
          use: [
            {
              loader: 'file-loader',
              options: {
                outputPath: 'styleguide/images/',
              },
            },
          ],
        },
        {
          test: /\.css$/i,
          use: ['css-loader'],
        },
      ],
    },
  },
};

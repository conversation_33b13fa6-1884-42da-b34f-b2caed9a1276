import React from 'react';
import { SubComponent } from './Example.styles';

// Export the type of the props
export type ExampleProps = {
  // Make a short comment for each prop. Use /** */ comment style.
  /** This is someProp, and used for something */
  someProp?: boolean;
  // Add the default props for whatever outermost HTML element
  // Use React.ComponentPropsWithRef if you're forwarding the ref
} & React.ComponentPropsWithoutRef<'div'>;

const Example: React.FC<React.PropsWithChildren<ExampleProps>> = (props) => {
  // Destructure props on the first line of the component
  // Set default value for any prop that should have a default value (needed to document the default props, and makes ts happy)
  const { children, someProp = false, ...rest } = props;

  return <SubComponent {...rest}>{children}</SubComponent>;
};

export default Example;

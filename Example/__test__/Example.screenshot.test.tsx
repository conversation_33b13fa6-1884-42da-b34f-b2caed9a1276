import React from 'react';
import Example from '../Example';
import { takeScreenshot } from '../../src/utils/screenshotTestUtils';

describe('Example', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Example>Default Example</Example>, task);
  });

  test('WithSomeProp', async ({ task }) => {
    await takeScreenshot(<Example someProp={true}>with someProp True</Example>, task);
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Example > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

<div>
  <div
    class="c0"
  >
    example
  </div>
</div>
`;

exports[`Example > renders without crashing with someProp false 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

<div>
  <div
    class="c0"
  >
    example
  </div>
</div>
`;

exports[`Example > renders without crashing with someProp true 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

<div>
  <div
    class="c0"
  >
    example
  </div>
</div>
`;

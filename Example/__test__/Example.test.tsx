import React from 'react';
import { render } from '../../src/utils/testUtils';
import Example from '../Example';

describe('Example', () => {
  // Create a basic render test
  test('renders without crashing', () => {
    const { getByText, container } = render(<Example>example</Example>);
    const testElement = getByText(/example/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  // Create tests for each option of each prop
  [true, false].forEach((someProp) => {
    test(`renders without crashing with someProp ${someProp}`, () => {
      const { getByText, container } = render(<Example someProp={someProp}>example</Example>);
      const testElement = getByText(/example/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

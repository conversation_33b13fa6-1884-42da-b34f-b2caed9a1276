{"compilerOptions": {"baseUrl": "./", "lib": ["dom", "dom.iterable", "esnext"], "declaration": true, "declarationDir": "./build", "esModuleInterop": true, "jsx": "react", "module": "CommonJS", "moduleResolution": "node", "noEmit": false, "noEmitOnError": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./build", "skipLibCheck": true, "strict": true, "target": "ES2015", "types": ["vitest/globals", "@testing-library/jest-dom", "@vitest/browser/providers/webdriverio"], "typeRoots": ["./node_modules", "./node_modules/@types", "styleguide/types", "src/DatePicker", "./node_modules/@testing-library"]}, "include": ["src", "src/", "src/**/*.ts", "src/**/*.tsx", "styleguide/types"], "exclude": ["node_modules", "src/**/__screenshots__", "src/**/*.md"]}
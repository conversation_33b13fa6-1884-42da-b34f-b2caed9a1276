import type { UserConfig } from '@commitlint/types';
// eslint-disable-next-line import/no-extraneous-dependencies
import { RuleConfigSeverity } from '@commitlint/types';

// [type] <scope> - <description>
const Configuration: UserConfig = {
  /*
   * Resolve and load @commitlint/config-conventional from node_modules.
   * Referenced packages must be installed
   */
  extends: ['@commitlint/config-conventional'],
  /*
   * Resolve and load conventional-changelog-atom from node_modules.
   * Referenced packages must be installed
   */
  parserPreset: {
    parserOpts: {
      headerPattern: /^\[(\w+)\] (.+) - (.+)/,
      headerCorrespondence: ['type', 'scope', 'subject'],
    },
  },
  /*
   * Any rules defined here will override rules from @commitlint/config-conventional
   */
  rules: {
    'type-enum': [RuleConfigSeverity.Error, 'always', ['feature', 'fix', 'docs', 'refactor', 'ci', 'test', 'update', 'core']],
    'header-max-length': [RuleConfigSeverity.Error, 'always', 120],
    'subject-case': [RuleConfigSeverity.Disabled],
  },
  /*
   * Custom URL to show upon failure
   */
  helpUrl: 'https://github.com/conventional-changelog/commitlint/#what-is-commitlint',
};

export default Configuration;

# Contributing to the One AXA Design System

- [Stack](#stack)
- [Coding guidelines](#coding-guidelines)
- [Branching strategy](#branching-strategy)
- [Commit message syntax](#commit-message-syntax)
  - [Commit message examples](#commit-message-examples)
- [Folder structure](#folder-structure)
- [Library exports](#library-exports)
- [Documenting components](#documenting-components)
- [Testing strategy](#testing-strategy)
- [Visual regression testing](#visual-regression-testing)
  - [Setting up vitest browser](#setting-up-vitest-browser)
- [Tips for testing changes](#tips-for-testing-changes)
  - [Method 1](#method-1-directly-move-it-to-your-applications-existing-installation-in-node_modules)
  - [Method 2](#method-2-build-locally-and-creating-a-tarball-to-be-installed-it-as-a-package)
- [How to publish new version](#how-to-publish-new-version)
  - [Release script sequence](#release-script-sequence)
- [Troubleshooting](#troubleshooting)

## Stack

| Description                | Name                                                                                                                                                                                         |
| -------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Syntax                     | TypeScript                                                                                                                                                                                   |
| Styling                    | Styled Components                                                                                                                                                                            |
| Bundling                   | tsup                                                                                                                                                                                         |
| Documentation              | React Styleguidist                                                                                                                                                                           |
| Functional Testing         | React Testing Library                                                                                                                                                                        |
| Visual Regression Testing  | Vitest Browser                                                                                                                                                                               |
| Headless Component Library | [Chakra UI / Ark UI](https://github.com/chakra-ui/ark) <br> [React Aria](https://github.com/adobe/react-spectrum?tab=readme-ov-file) <br> [Radix UI](https://github.com/radix-ui/primitives) |

## Coding guidelines

This project uses [ALJ eslint config](https://github.axa.com/aljdevops/eslint-config) (including prettier) to enforce consistent coding style. Code is formatted with a pre-commit hook, so even if your code editor doesn't fix/format on save, your code should be compliant.

Never use eslint-disable comments.

## Branching strategy

If you want to fix a bug or add a new component or feature to the library, start by cloning the repo and making a new branch from `main`.

## Commit message syntax

Commitlint helps to follow commit conventions. Below is the structure to follow to commit code successfully.

`[type][optional ticket-no] <scope> - <subject>`

If you face below error that means you are not following the convention as expected.

```
✖   subject may not be empty [subject-empty]
✖   type may not be empty [type-empty]
```

Commitlint Doc Url: `https://github.com/conventional-changelog/commitlint/#what-is-commitlint`

### Commit message examples

| Commit message types (tags)                                                                       | Example                                                           |
| ------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| **feature**: To add a new feature                                                                 | `[feature] Landing Page - add table on landing page`              |
| **fix**: To fix a bug/error.                                                                      | `[fix] Landing Page - scroll overflows in mobile view`            |
| **docs**: Update documentation (README.md)                                                        | `[docs] Documentation - Updated new commit conventions to follow` |
| **refactor**: To refactor the code (breaking changes)                                             | `[refactor] Example Page - Break the component to the flow.`      |
| **ci** Changes to our CI configuration files and scripts (example scopes: Github workflow, husky) | `[ci] husky - Add husky pre-commits configs`                      |
| **test**: Adding missing or correcting existing tests                                             | `[test] Example Page - Add popup component test case`             |
| **update** To update the component from Beta/Candidate/Legacy                                     | `[update] Button - Update to Candidate`                           |
| **core**: General improvement which is not linked to specific component                           | `[core] Improvement - Add theme based changes`                    |

## Folder structure

Folder structure is as follows:

```
src/                                      -- All library code goes here
  ComponentName/                          -- All the code for a single component
    __test__/                             -- All the testing related stuff
      __screenshots__                     -- Screenshots for the visual regression tests
      __snapshots__                       -- Snapshots for the functional tests
      ComponentName.screenshot.test.tsx   -- Browser screenshot test code
      ComponentName.test.tsx              -- Functional test code
    ComponentName.md                      -- Component documentation
    ComponentName.ts                      -- Styled components for the component
    ComponentName.tsx                     -- The component
    index.ts                              -- File which exports components and TS types
```

You can copy/paste the `Example` directory to get started creating a new component.

Additionally, shared hooks should go in `src/hooks`, shared styles can go in `src/styles`, and shared utilities can go in `src/utils`.

## Library exports

The build is setup to manage exports to library consumers following these rules:

1. Anything exported in the "top-level" (src/\*.ts)
2. Anything exported in an index.ts file

Below is an example of which modules exports are private and public in the final build:

```
src/
  ComponentName/
    ComponentName.styles.ts               -- Private
    ComponentName.tsx                     -- Private
    index.ts                              -- Public
  colors.ts                               -- Public
  index.ts                                -- Public
```

_CAUTION_
Take care not to accidentally expose something to consumers that may not be
ready for public use.

```
src/
  MyComponent/
    MyComponent.tsx     -- some impl
    index.ts            -- exposes MyComponent to consumers
  hooks/
    internalHook1.ts    -- some hook used internally by MyComponent, etc.
    internalHook2.ts    -- some other hook used internally
    index.ts            -- *oops* these hooks are potentially consumed downstream
```

## Documenting components

This project uses [React Styleguidist](https://github.com/styleguidist/react-styleguidist) to document the components. Each component that is directly available to users should be documented.

Simply run `npm start` to run the styleguide, and you can see it at _localhost:6060_. Note, because we're using TypeScript, it takes a while for the styleguide to actually start up, you have to wait until you see the message "DONE Compiled successfully!".

You will need to add a newly added component to `styleguide.config.js`.

## Testing strategy

The testing strategy for this project consists of:

- Functional testing with [react-testing-library](https://github.com/testing-library/react-testing-library)
- Visual regression testing with [vitest browser](https://vitest.dev/guide/browser.html)

Each component that is directly available to users should have a functional tests (code test coverage is calculated with the functional tests), and a screenshot test. It is not necessary to have tests sub-components, as long as testing is covered by the main component tests.

## Visual regression testing

This project uses [vitest browser](https://vitest.dev/guide/browser.html) for visual regression testing to make sure none of the components ever breaks.

You need to run the screenshot tests before pushing any branch and opening a pull request. A diff in one of the screenshots means that something changed (which may or may not be expected).

### Setting up vitest browser

Run browser tests with `npm run test:screenshot` command, which creates a `__screenshots__` directory next to the component definition. Browser tests are defined in `*.screenshot.test.tsx` files and use the `takeScreenshot` utility function.

## Tips for testing changes

### Method 1: Directly move it to your application's existing installation in node_modules

Sometimes you might want to try to test changes in the context of some application. The easiest way is to build the components, and then replace them in your application code. You can run this in your terminal:

```
npm run build && npm run build:addPackageFiles &&
rm -rf ../<your project>/node_modules/@axa-japan/design-system-react && mv build ../<your project>/node_modules/@axa-japan/design-system-react
```

Make sure to replace _<your project>_ with the name of the project where you want to test the changes

### Method 2: Build locally and creating a tarball to be installed it as a package

This method more accurately replicates the behavior of installing the package in your application. This way you can test for problems during installation time as well:

```
npm run build && npm run build:addPackageFiles && npm pack ./build --pack-destination ~/<your destination>/

// in your project
npm i file:~/<your destination>/axa-japan-design-system-react-x.x.x.tgz
```

## How to publish new version

Here is the [pipeline](https://jenkins.axa-li-jp-dev-int.scarlet.ap-southeast-1.aws.openpaas.axa-cloud.com/job/projects/job/design-system/job/prod/job/design-system-react/).

Choose carefully the type of versioning you want (major, minor or patch) in the parameters of the pipeline.

The pipeline will:

- Run `npm version <type>` according to the parameter you chose. Check [the release script sequence](#Release-script-sequence) for more details.
- Publish the new version of the package to Artifactory.
- Push the new version and tag to GitHub.
- [The docs pipeline](https://jenkins.axa-li-jp-dev-int.scarlet.ap-southeast-1.aws.openpaas.axa-cloud.com/job/projects/job/design-system/job/test/job/design-system-docs/) is triggered automatically after the prod pipeline.

_More information about the `npm version` command: https://docs.npmjs.com/cli/version_

After that, add a release with release notes on GitHub:

- Go to the releases page on the repo _https://github.axa.com/aljdevops/design-system-react/releases_
- Click _Draft a new release_
- Enter the tag of the new release
- Add release notes
- Click _Publish release_

### Release script sequence

A lot of scripts run when the pipeline run `npm version`. Here are the exact steps:

- preversion
  - docs:build -- Make sure the docs build before creating a new version.
  - pretest:build -- Builds and runs a script to copy the test and snapshot files, modifying them to run on the built version of the components.
    - build -- Make sure the code builds before creating a new version.
      - prebuild
        - check-types -- Makes sure TypeScript types are valid.
        - rm -rf build -- Remove the previous build
      - build:ts -- Compiles the TypeScript code in src to JavaScript.
      - build:types -- Compiles the TypeScript type definition files.
    - node ./scripts/addBuildTests -- Script to copy test and snapshot files.
  - test:build -- Runs the React tests on the built version of the components.
- version -- Updates the package.json file with the new version number.
- postversion
  - build:addPackageFiles -- Copies types.d.ts, README.md and package.json into build directory and creates a package.json file for each component to ensure components are tree-shakeable.

## Troubleshooting

If you get this error
`TypeError: o.addEventListener is not a function`

you can fix by adding the following code to your file

Jest:

```
const mockMatchMedia = (query: any) => ({
  matches: query === '(max-width: 899px)',
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

jest.spyOn(window, 'matchMedia').mockImplementation(mockMatchMedia);
```

Vitest:

```
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: query === '(max-width: 899px)',
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
```

If you get this error:
`Error: Uncaught [ReferenceError: ResizeObserver is not defined]`

you can fix by adding the following code to your file

```
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
};
```

{"name": "@axa-japan/design-system-react", "version": "0.20.11", "description": "Shared React components for One AXA design system", "author": "<PERSON>", "license": "ISC", "repository": "https://github.axa.com/aljdevops/design-system-react", "publishConfig": {"registry": "https://artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/"}, "scripts": {"check-types": "tsc --noEmit --project tsconfig.build.json", "prebuild": "npm run check-types && rm -rf build", "build": "npm run build:ts && npm run build:types", "build:types": "tsc --declaration --emitDeclarationOnly --project tsconfig.build.json", "build:ts": "tsup --publicDir", "watch": "npm run build:ts -- --watch", "build:addPackageFiles": "ts-node --transpile-only ./scripts/addPackageFiles", "format": "prettier --write \".\"", "start": "npx styleguidist server", "docs:build": "npx styleguidist build", "lint": "eslint --ext .ts,.tsx ./src", "lint:styles": "stylelint './src/**/*.ts' './src/*.ts'", "lint:ci": "eslint --ext .ts,.tsx ./src --output-file ./eslint-error-report.json --format json", "test": "vitest --watch=false --project 'unit' --coverage", "test:screenshots": "npm run test:screenshots:mobile && npm run test:screenshots:desktop && npm run test:screenshots:tablet", "test:screenshots:mobile": "vitest --watch=false --project 'mobile'", "test:screenshots:tablet": "vitest --watch=false --project 'tablet'", "test:screenshots:desktop": "vitest --watch=false --project 'desktop'", "test:commit:screenshots": "npm run test:screenshots && git add -A && git commit -m'update screenshots' && git push origin HEAD", "pretest:build": "npm run build && node ./scripts/addBuildTests", "test:build": "vitest --watch=false --project 'build'", "publish": "npm run test:build && npm run build:addPackageFiles && npm publish ./build", "publish:pr": "npm run build:addPackageFiles && npm publish ./build", "prepare": "husky", "commitlint": "commitlint --config commitlint.config.ts --edit"}, "devDependencies": {"@axa-japan/eslint-config": "^6.0.0", "@axe-core/react": "^4.10.1", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/body-scroll-lock": "^3.1.2", "@types/fs-extra": "^11.0.4", "@types/react": "^18.3.11", "@types/react-input-mask": "^3.0.5", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/runner": "^3.2.4", "commitlint": "^19.5.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "esbuild-loader": "^4.2.2", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "file-loader": "^6.2.0", "fs-extra": "^11.3.0", "husky": "^9.1.7", "jest-styled-components": "^7.2.0", "jsdom": "^26.0.0", "lint-staged": "^15.2.10", "postcss-styled-syntax": "^0.6.4", "postcss-syntax": "^0.36.2", "prettier": "^3.3.3", "react": "^18.3.1", "react-docgen-typescript": "^2.2.2", "react-dom": "^18.3.1", "react-number-format": "^5.4.2", "react-router-dom": "^6.27.0", "react-styleguidist": "^13.1.3", "serverless": "^3.39.0", "serverless-lift": "^1.30.2", "styled-components": "^5.3.11", "stylelint": "^16.10.0", "stylelint-config-standard": "^36.0.1", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsup": "^8.3.0", "typescript": "^5.5.4", "vitest": "^3.2.4", "vitest-browser-react": "^1.0.0", "webdriverio": "^9.16.2", "webpack": "^5.98.0"}, "dependencies": {"@ark-ui/react": "^4.9.1", "@chakra-ui/toast": "^7.0.2", "@internationalized/date": "^3.7.0", "@mona-health/react-input-mask": "^3.0.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@react-aria/collections": "^3.0.0-beta.1", "@react-aria/interactions": "^3.24.1", "@react-aria/utils": "^3.28.1", "@react-types/shared": "^3.28.0", "@tanstack/react-table": "^8.21.2", "body-scroll-lock": "^4.0.0-beta.0", "core-js": "^3.40.0", "deepmerge": "^4.3.1", "focus-trap": "^7.6.4", "react-aria": "^3.38.1", "react-aria-components": "^1.7.1", "react-stately": "^3.36.1", "yet-another-react-lightbox": "^3.21.7"}, "peerDependencies": {"@commitlint/lint": ">=7.6.0", "@internationalized/date": "^3.7.0", "react": ">=18.0.0", "styled-components": ">=5.1.1"}, "overrides": {"eslint-config-react-app": {"@typescript-eslint/eslint-plugin": "^6.20.0"}, "jsdom": {"nwsapi": "2.2.13"}}, "lint-staged": {"*.{js,ts,tsx}": ["prettier --write", "eslint --fix"], "*.ts": ["stylelint"]}, "browserslist": ["last 2 versions and >.1% in JP", "ie 11", "safari >=12"]}
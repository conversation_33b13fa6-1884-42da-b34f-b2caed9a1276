import React, { forwardRef } from 'react';
import { useController } from 'yet-another-react-lightbox';
import { CloseIcon } from '../Icons';
import IconButton from '../IconButton';

const LightboxCloseButton = forwardRef<HTMLButtonElement, Required<Pick<React.ComponentPropsWithoutRef<'button'>, 'onClick'>>>(({ onClick }, ref) => {
  const { close } = useController();
  return (
    <IconButton
      ref={ref}
      aria-label="close-button"
      color="white"
      icon={<CloseIcon />}
      data-testid="close-lightbox-icon-button"
      onClick={(e) => {
        onClick(e);
        close();
      }}
    />
  );
});

LightboxCloseButton.displayName = 'LightboxCloseButton';

export default LightboxCloseButton;

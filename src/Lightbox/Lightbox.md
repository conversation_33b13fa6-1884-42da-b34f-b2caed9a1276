### Lightbox:

The _Lightbox_ is a controlled component that accepts a prop called _slides_ which is a list which currenly only accept one item, to display one image.

```js
import React, { useState } from 'react';
import Lightbox from '@axa-japan/design-system-react/Lightbox';
import Button from '@axa-japan/design-system-react/Button';
import Image from '@axa-japan/design-system-react/Lightbox/data/image_1920_1245.jpg';

function Render() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);
  return (
    <Lightbox
      open={open}
      onClose={() => setOpen(false)}
      slides={[
        {
          src: Image,
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Lightbox Trigger
      </Button>
    </Lightbox>
  );
}

<Render />;
```

### Lightbox slides options:

The _Lightbox_ _slides_ prop only has _src_ as required, but the other props are optional.

Unlike many other lightbox libraries, ours is not limited to just two images per slide ("thumbnail" and "original" / "full size"). Instead, we favor responsive images with automatic resolution switching and recommend you provide multiple files of different resolutions for each image slide with the _srcSet_ prop. _Lightbox_ automatically populates srcset / sizes attributes and lets your browser decide which image is more appropriate for its viewport size.

```js
import React, { useState } from 'react';
import Lightbox from '@axa-japan/design-system-react/Lightbox';
import Button from '@axa-japan/design-system-react/Button';
import Image from '@axa-japan/design-system-react/Lightbox/data/image_1920_1245.jpg';
import Image2 from '@axa-japan/design-system-react/Lightbox/data/image_2400_1556.jpg';
import Image3 from '@axa-japan/design-system-react/Lightbox/data/image_640_415.jpg';

function Render() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);
  return (
    <Lightbox
      open={open}
      onClose={() => setOpen(false)}
      slides={[
        {
          src: Image,
          alt: 'image 1',
          width: 1920,
          height: 1245,
          srcSet: [
            { src: Image, width: 1920, height: 1245 },
            { src: Image2, width: 2400, height: 1556 },
            { src: Image3, width: 640, height: 415 },
          ],
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Lightbox Trigger
      </Button>
    </Lightbox>
  );
}

<Render />;
```

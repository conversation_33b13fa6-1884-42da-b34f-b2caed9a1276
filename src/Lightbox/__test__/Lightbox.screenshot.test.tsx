import React from 'react';
import Lightbox from '../Lightbox';
import Image from '../data/image_1920_1245.jpg';
import Button from '../../Button';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Lightbox', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(
      <Lightbox
        open={true}
        slides={[
          {
            src: Image,
          },
        ]}
      >
        <Button variant="filled" color="blue">
          Lightbox Trigger
        </Button>
      </Lightbox>,
      task,
      {
        delay: true,
      },
    );
  });
});

import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import Button from '../../Button';
import { render } from '../../utils/testUtils';
import Lightbox from '../Lightbox';
import Image from '../data/image_1920_1245.jpg';

describe('Lightbox', () => {
  test('renders without crashing and test close button', async () => {
    const Component: React.FC = () => {
      const [open, setOpen] = useState(false);

      const onClick = () => setOpen((currentValue) => !currentValue);

      return (
        <Lightbox
          open={open}
          onClose={() => setOpen(false)}
          slides={[
            {
              src: Image,
            },
          ]}
        >
          <Button variant="filled" color="blue" onClick={onClick}>
            Lightbox Trigger
          </Button>
        </Lightbox>
      );
    };

    const { queryByRole, getByText, getByTestId, container } = render(<Component />);

    expect(queryByRole('dialog')).toBeNull();

    await userEvent.click(getByText('Lightbox Trigger'));

    await waitFor(() => {
      expect(queryByRole('dialog')).not.toBeNull();
    });

    expect(container).toMatchSnapshot();

    const closeButton = getByTestId('close-lightbox-icon-button');

    expect(closeButton).toHaveAttribute('aria-label', 'close-button');

    // test close button

    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(queryByRole('dialog')).toBeNull();
    });
  });
});

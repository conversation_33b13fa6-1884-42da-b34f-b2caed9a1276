import styled from 'styled-components';
import Lightbox from 'yet-another-react-lightbox';
import { getColorVar, primitiveColors } from '../colors';
import { getSpacingVar } from '../spacing';
import media from '../Breakpoints/Breakpoints';
import getRgba from '../utils/getRgba';

const LightboxBase = styled(Lightbox)`
  /* stylelint-disable */
  .yarl__toolbar {
    width: 100%;
    background-color: ${getColorVar('utilityBackgroundGrey')} !important;
    padding: ${getSpacingVar(1)} ${getSpacingVar(2)} !important;
  }

  .yarl__container {
    background-color: ${getRgba(primitiveColors.grey800, 90)} !important;
  }

  .yarl__slide {
    padding: 4.5rem ${getSpacingVar(6)} ${getSpacingVar(6)} ${getSpacingVar(6)} !important;

    ${media.extraSmallOnly} {
      padding: 3.75rem ${getSpacingVar(2)} ${getSpacingVar(2)} ${getSpacingVar(2)} !important;
    }
  }
  /* stylelint-enable */
`;

export default LightboxBase;

import React, { useEffect, useState } from 'react';
import { LightboxExternalProps, Slide } from 'yet-another-react-lightbox';
import Zoom from 'yet-another-react-lightbox/plugins/zoom';
import * as focusTrap from 'focus-trap';
import LightboxBase from './LightboxBase';
import LightboxCloseButton from './LightboxCloseButton';

declare global {
  interface Window {
    VITEST_ENV: boolean | undefined;
  }
}

type LightboxSlide = Pick<Slide, 'alt' | 'src' | 'height' | 'srcSet' | 'width'>;

export type LightboxProps = {
  /** Optional modal trigger */
  children?: React.ReactElement;
  /** slides to display in the lightbox, only 1 slide can be passed */
  slides: [LightboxSlide];
  /** a callback to close the lightbox */
  onClose?: () => void;
} & Pick<LightboxExternalProps, 'open'>;

const Lightbox: React.FC<LightboxProps> = ({ children, open, onClose, ...rest }) => {
  const [closeButton, setCloseButton] = useState<HTMLButtonElement | null>(null);
  const [lightboxFocusTrap, setLightboxFocusTrap] = useState<focusTrap.FocusTrap | null>(null);
  // Set up focus trap within the lightbox
  useEffect(() => {
    // Get element to trap focus in
    const lightboxElement = document.querySelector('div[class*="yarl__container"]') as HTMLElement;
    if (lightboxElement && open && !lightboxFocusTrap?.active) {
      const trap = focusTrap.createFocusTrap(lightboxElement, {
        tabbableOptions: {
          // we need to disable displayChecks as Vitest uses JSDom which doesn't support the full DOM APIs
          // https://github.com/focus-trap/tabbable?tab=readme-ov-file#testing-in-jsdom
          // https://github.com/focus-trap/focus-trap-react/issues/1002
          displayCheck: window.VITEST_ENV ? 'none' : undefined,
        },
        initialFocus: () => closeButton as HTMLButtonElement,
        returnFocusOnDeactivate: false,
      });
      trap.activate();
      setLightboxFocusTrap(trap);
    }
    return () => {
      if (lightboxFocusTrap?.active) {
        lightboxFocusTrap?.deactivate();
      }
    };
  }, [closeButton, open, lightboxFocusTrap]);
  return (
    <>
      {children}
      <LightboxBase
        {...rest}
        plugins={[Zoom]}
        carousel={{
          finite: true,
          padding: 0,
        }}
        open={open}
        close={onClose}
        controller={{ closeOnBackdropClick: true }}
        toolbar={{
          buttons: [
            <LightboxCloseButton
              onClick={() => lightboxFocusTrap?.deactivate()}
              ref={(el) => {
                setCloseButton(el);
              }}
              key="close-button"
            />,
          ],
        }}
        render={{
          buttonPrev: () => null,
          buttonNext: () => null,
          iconZoomIn: () => null,
          iconZoomOut: () => null,
        }}
      />
    </>
  );
};

export default Lightbox;

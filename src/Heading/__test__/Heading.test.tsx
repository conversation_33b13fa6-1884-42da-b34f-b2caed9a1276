import React from 'react';
import { render } from '../../utils/testUtils';
import Heading from '../Heading';

describe('Heading', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Heading>text-component</Heading>);
    const testElement = getByText('text-component');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
  (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] as const).forEach((tag) => {
    test(`renders with tag ${tag} without crashing`, () => {
      const { getByText, container } = render(<Heading as={tag}>HEADING</Heading>);
      const testElement = getByText(/HEADING/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  (['2xl', 'xl', 'lg', 'md'] as const).forEach((size) => {
    test(`renders with size ${size} without crashing`, () => {
      const { getByText, container } = render(<Heading size={size}>HEADING</Heading>);
      const testElement = getByText(/HEADING/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['primary', 'primary-white'].forEach((color) => {
    test(`renders with color ${color} without crashing`, () => {
      const { getByText, container } = render(<Heading color={color as 'primary' | 'primary-white'}>HEADING</Heading>);
      const testElement = getByText(/HEADING/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['start', 'end', 'center'].forEach((color) => {
    test(`renders with color ${color} without crashing`, () => {
      const { getByText, container } = render(<Heading align={color as 'start' | 'end' | 'center'}>HEADING</Heading>);
      const testElement = getByText(/HEADING/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['wrap', 'nowrap'].forEach((wrap) => {
    test(`renders with wrap ${wrap} without crashing`, () => {
      const { getByText, container } = render(<Heading wrap={wrap as 'wrap' | 'nowrap'}>HEADING</Heading>);
      const testElement = getByText(/HEADING/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

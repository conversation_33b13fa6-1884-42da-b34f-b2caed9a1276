// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Heading > renders with color center without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: center; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with color end without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: end; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with color primary without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with color primary-white without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with color start without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with size 2xl without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-2xl-mobile); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with size lg without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-lg); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with size md without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with size xl without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with tag h1 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h1
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-2xl-mobile); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h1>
</div>
`;

exports[`Heading > renders with tag h2 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with tag h3 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h3
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-lg); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h3>
</div>
`;

exports[`Heading > renders with tag h4 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h4
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h4>
</div>
`;

exports[`Heading > renders with tag h5 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h5
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h5>
</div>
`;

exports[`Heading > renders with tag h6 without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h6
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h6>
</div>
`;

exports[`Heading > renders with wrap nowrap without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: nowrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders with wrap wrap without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    HEADING
  </h2>
</div>
`;

exports[`Heading > renders without crashing 1`] = `
.c0 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

<div>
  <h2
    class="c0"
    style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
  >
    text-component
  </h2>
</div>
`;

import React from 'react';
import Heading from '../Heading';
import colors from '../../colors';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Heading', () => {
  test('defaultHeading', async ({ task }) => {
    await takeScreenshot(<Heading>Default Heading</Heading>, task);
  });

  test('headingVariants', async ({ task }) => {
    await takeScreenshot(
      <>
        <Heading as="h1">Heading H1</Heading>
        <Heading as="h2">Heading H2</Heading>
        <Heading as="h3">Heading H3</Heading>
        <Heading as="h4">Heading H4</Heading>
        <Heading as="h5">Heading H5</Heading>
        <Heading as="h6">Heading H6</Heading>
      </>,
      task,
    );
  });

  test('sizeVariants', async ({ task }) => {
    await takeScreenshot(
      <>
        <Heading size="2xl">This is 2xl Heading</Heading>
        <Heading size="xl">This is xl Heading</Heading>
        <Heading size="lg">This is lg Heading</Heading>
        <Heading size="md">This is md Heading</Heading>
      </>,
      task,
    );
  });

  test('alignmentVariants', async ({ task }) => {
    await takeScreenshot(
      <>
        <Heading align="start">This is left aligned Heading</Heading>
        <Heading align="center">This is center aligned Heading</Heading>
        <Heading align="end">This is right aligned Heading</Heading>
      </>,
      task,
    );
  });

  test('wrapVariants', async ({ task }) => {
    await takeScreenshot(
      <>
        <Heading wrap="wrap">This is wrap Heading</Heading>
        <Heading wrap="nowrap">This is nowrap Heading</Heading>
      </>,
      task,
    );
  });

  test('colorVariants', async ({ task }) => {
    await takeScreenshot(
      <>
        <Heading color="primary">This is primary variant color Heading</Heading>
        <div style={{ backgroundColor: colors.utilityBackgroundOcean, padding: '1rem' }}>
          <Heading color="primary-white">This is default variant color Heading</Heading>
        </div>
      </>,
      task,
    );
  });
});

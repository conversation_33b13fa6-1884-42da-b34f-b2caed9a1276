import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getTypographyVar } from '../typography';

const variants: VariantsType<'size' | 'color' | 'align' | 'wrap'> = {
  size: {
    '2xl': {
      'font-size': [getTypographyVar('2xlFontSizeMobile'), getTypographyVar('2xlFontSize')],
    },
    xl: {
      'font-size': getTypographyVar('xlFontSize'),
    },
    lg: {
      'font-size': getTypographyVar('lgFontSize'),
    },
    md: {
      'font-size': getTypographyVar('defaultFontSize'),
    },
  },
  color: {
    primary: {
      color: getColorVar('characterPrimary'),
    },
    'primary-white': {
      color: getColorVar('characterPrimaryWhite'),
    },
  },
  align: {
    start: {
      'text-align': 'start',
    },
    end: {
      'text-align': 'end',
    },
    center: {
      'text-align': 'center',
    },
  },
  wrap: {
    wrap: {
      'text-wrap': 'wrap',
    },
    nowrap: {
      'text-wrap': 'nowrap',
    },
  },
};

const { useSx, getSxStyleRules } = sx('Heading', ['margin'], variants);

export { useSx, variants };

// Japanese specific typography need to use correct font family
export const HeadingBase = styled.h2`
  line-height: ${getTypographyVar('smLineHeight')};
  font-weight: ${getTypographyVar('boldFontWeight')};

  ${getSxStyleRules()};
`;

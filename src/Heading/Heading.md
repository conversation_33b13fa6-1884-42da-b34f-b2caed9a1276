### Default Heading:

Default tag is H2.

```js
import Heading from '@axa-japan/design-system-react/Heading';

<Heading>This is the default Heading style</Heading>;
```

### With As:

You can change the tag of the Heading component using the `as` prop. It will also change the default size.

```js
import Heading from '@axa-japan/design-system-react/Heading';

<>
  <Heading as="h1">Heading 1</Heading>
  <Heading as="h2">Heading 2</Heading>
  <Heading as="h3">Heading 3</Heading>
  <Heading as="h4">Heading 4</Heading>
</>;
```

### With Size:

Sometimes we want to use a specific heading tag but with a different size.:

```js
import Heading from '@axa-japan/design-system-react/Heading';
<>
  <Heading as="h2" size="2xl">
    H2 with size 2xl
  </Heading>
  <Heading as="h2" size="xl">
    H2 with size xl
  </Heading>
  <Heading as="h2" size="lg">
    H2 with size lg
  </Heading>
  <Heading as="h2" size="md">
    H2 with size md
  </Heading>
</>;
```

### With Color:

```js
import Heading from '@axa-japan/design-system-react/Heading';
import Stack from '@axa-japan/design-system-react/Stack';

<Stack direction="column" sx={{ padding: '2', 'background-color': 'utilityBackgroundOcean' }}>
  <Heading color="primary">Heading with default color</Heading>
  <Heading color="primary-white">Heading with defaultVariant color</Heading>
</Stack>;
```

### With Align:

```js
import Heading from '@axa-japan/design-system-react/Heading';

<div style={{ width: '200px' }}>
  <Heading align="start">Start align</Heading>
  <Heading align="end">End align</Heading>
  <Heading align="center">Center align</Heading>
</div>;
```

### With Wrap:

```js
import Heading from '@axa-japan/design-system-react/Heading';

<div style={{ width: '500px' }}>
  <Heading wrap="wrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Heading>
  <Heading wrap="nowrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Heading>
</div>;
```

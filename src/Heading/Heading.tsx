import React from 'react';
import { MarginSxPropType } from '../sx';
import { HeadingBase, useSx } from './HeadingBase';

const sizeMap = {
  h1: '2xl',
  h2: 'xl',
  h3: 'lg',
  h4: 'md',
  h5: 'md',
  h6: 'md',
} as const;

export type HeadingProps = {
  /** Type of tag to use as the container */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  /** Overrides the style of "as" tag with another one if needed */
  size?: '2xl' | 'xl' | 'lg' | 'md';
  color?: 'primary' | 'primary-white';
  /** Sets the text-align property */
  align?: 'start' | 'end' | 'center';
  /** Sets the text-wrap property */
  wrap?: 'wrap' | 'nowrap';
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'h2'>;

const Heading: React.FC<HeadingProps> = ({ children, as = 'h2', size, color = 'primary', align = 'start', wrap = 'wrap', sx, ...rest }) => {
  const sxOverrides = useSx(sx, { size: size ?? sizeMap[as], color, align, wrap });

  return (
    <HeadingBase {...rest} as={as} style={sxOverrides}>
      {children}
    </HeadingBase>
  );
};

export default Heading;

import React, { useId } from 'react';
import FieldLabelBase from './FieldLabelBase';
import Badge from '../Badge';
import FieldLegendBase from './FieldLegendBase';

export type FieldLabelProps = {
  label: string;
  htmlFor?: string;
  required?: boolean;
  useLegend?: boolean;
  showRequiredIndicator?: boolean;
} & React.ComponentPropsWithRef<'label'> &
  React.ComponentPropsWithRef<'legend'>;

const FieldLabel: React.FC<FieldLabelProps> = ({
  htmlFor,
  label,
  required = false,
  useLegend = false,
  showRequiredIndicator = true,
  ...rest
}: FieldLabelProps) => {
  const id = useId();

  if (useLegend) {
    return (
      <FieldLegendBase id={`field-legend-${id}`} {...rest}>
        {label}
        {required && showRequiredIndicator && <Badge variant="important" text="必須" />}
      </FieldLegendBase>
    );
  }
  return (
    <FieldLabelBase id={`field-label-${id}`} htmlFor={htmlFor} {...rest}>
      {label}
      {required && showRequiredIndicator && <Badge variant="important" text="必須" />}
    </FieldLabelBase>
  );
};

export default FieldLabel;

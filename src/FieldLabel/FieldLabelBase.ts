import styled, { css } from 'styled-components';
import { getSpacingVar } from '../spacing';
import formStyles from '../styles/input';

export const FieldLabelStyles = css`
  ${formStyles.labelText}

  align-items: center;
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  column-gap: ${getSpacingVar(2)};

  /* Fix focus being triggered on 100% on container */
  align-self: self-start;
`;

const FieldLabelBase = styled.label`
  ${FieldLabelStyles}
`;

export default FieldLabelBase;

import React from 'react';
import { render } from '../../utils/testUtils';
import FieldLabel from '../FieldLabel';

describe('FieldLabel', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<FieldLabel label="Field Label" />);
    const testElement = getByText('Field Label');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

import styled from 'styled-components';
import input from '../styles/input';
import { getSpacingVar } from '../spacing';

const PasswordFieldInput = styled.input`
  ${input.base}

  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: ${getSpacingVar(3)} ${getSpacingVar(12)} ${getSpacingVar(3)} ${getSpacingVar(3.5)};

  &::-ms-reveal,
  &::-ms-clear {
    display: none;
  }
`;

export default PasswordFieldInput;

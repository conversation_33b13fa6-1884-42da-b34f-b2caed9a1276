// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TextField > renders without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:r1:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing and switches password visibility 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:re:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="true"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          data-testid="basic-password"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="text"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="true"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.1 13.3L14.65 11.85C14.8 11.0667 14.575 10.3333 13.975 9.65C13.375 8.96667 12.6 8.7 11.65 8.85L10.2 7.4C10.4833 7.26667 10.7708 7.16667 11.0625 7.1C11.3542 7.03334 11.6667 7 12 7C13.25 7 14.3125 7.4375 15.1875 8.3125C16.0625 9.1875 16.5 10.25 16.5 11.5C16.5 11.8333 16.4667 12.1458 16.4 12.4375C16.3333 12.7292 16.2333 13.0167 16.1 13.3ZM19.3 16.45L17.85 15.05C18.4833 14.5667 19.0458 14.0375 19.5375 13.4625C20.0292 12.8875 20.45 12.2333 20.8 11.5C19.9667 9.81667 18.7708 8.47917 17.2125 7.4875C15.6542 6.49584 13.9167 6 12 6C11.5167 6 11.0417 6.03334 10.575 6.1C10.1083 6.16667 9.65 6.26667 9.2 6.4L7.65 4.85C8.33333 4.56667 9.03333 4.35417 9.75 4.2125C10.4667 4.07084 11.2167 4 12 4C14.5167 4 16.7583 4.69584 18.725 6.0875C20.6917 7.47917 22.1167 9.28334 23 11.5C22.6167 12.4833 22.1125 13.3958 21.4875 14.2375C20.8625 15.0792 20.1333 15.8167 19.3 16.45ZM19.8 22.6L15.6 18.45C15.0167 18.6333 14.4292 18.7708 13.8375 18.8625C13.2458 18.9542 12.6333 19 12 19C9.48333 19 7.24167 18.3042 5.275 16.9125C3.30833 15.5208 1.88333 13.7167 1 11.5C1.35 10.6167 1.79167 9.79584 2.325 9.0375C2.85833 8.27917 3.46667 7.6 4.15 7L1.4 4.2L2.8 2.8L21.2 21.2L19.8 22.6ZM5.55 8.4C5.06667 8.83334 4.625 9.30834 4.225 9.825C3.825 10.3417 3.48333 10.9 3.2 11.5C4.03333 13.1833 5.22917 14.5208 6.7875 15.5125C8.34583 16.5042 10.0833 17 12 17C12.3333 17 12.6583 16.9792 12.975 16.9375C13.2917 16.8958 13.6167 16.85 13.95 16.8L13.05 15.85C12.8667 15.9 12.6917 15.9375 12.525 15.9625C12.3583 15.9875 12.1833 16 12 16C10.75 16 9.6875 15.5625 8.8125 14.6875C7.9375 13.8125 7.5 12.75 7.5 11.5C7.5 11.3167 7.5125 11.1417 7.5375 10.975C7.5625 10.8083 7.6 10.6333 7.65 10.45L5.55 8.4Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        >
          パスワードが表示されています
        </div>
      </div>
    </div>
    <div
      aria-live="polite"
      class="c13"
      role="alert"
    >
      <div
        class="c14"
      >
        <svg
          class="c11"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      Error
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing and switches password visibility 2`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:re:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="true"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          data-testid="basic-password"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        >
          パスワードが非表示になっています
        </div>
      </div>
    </div>
    <div
      aria-live="polite"
      class="c13"
      role="alert"
    >
      <div
        class="c14"
      >
        <svg
          class="c11"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      Error
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with disabled false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:r6:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with disabled true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:r4:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="true"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          disabled=""
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            disabled=""
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="true"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with error 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:rc:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="true"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        />
      </div>
    </div>
    <div
      aria-live="polite"
      class="c13"
      role="alert"
    >
      <div
        class="c14"
      >
        <svg
          class="c11"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      Error
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with required false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c4 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c4 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6::-ms-reveal,
.c6::-ms-clear {
  display: none;
}

.c12 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c7 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:ra:"
    >
      Password
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c3 c4"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c5 c6"
          id="basic-password"
          name="basic-password"
          spellcheck="false"
          type="password"
        />
        <div
          class="c7"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c8"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c9"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c10"
              >
                <svg
                  class="c11"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c12"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with required true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c13 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c10 svg {
  color: currentColor;
}

.c10:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c10:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c11 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c11 svg {
  color: currentColor;
}

.c11[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c12 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c6:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c6[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c6 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c6 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c6 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c8 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c8::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c7:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c8:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c8::-ms-reveal,
.c8::-ms-clear {
  display: none;
}

.c14 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c9 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c11:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c11:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-password"
      id="field-label-:r8:"
    >
      Password
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </label>
    <div
      class="c4 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-label="Password"
        aria-labelledby="basic-password"
        class="c5 c6"
        id="basic-password:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c7 c8"
          id="basic-password"
          name="basic-password"
          required=""
          spellcheck="false"
          type="password"
        />
        <div
          class="c9"
        >
          <button
            aria-controls="basic-password"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c10"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c11"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c12"
              >
                <svg
                  class="c13"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c14"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing without label 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c10 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c8 svg {
  color: currentColor;
}

.c8[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c9 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3:has(input[aria-invalid='true']) {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3 input[type='password'] {
  -webkit-letter-spacing: 0.16em;
  -moz-letter-spacing: 0.16em;
  -ms-letter-spacing: 0.16em;
  letter-spacing: 0.16em;
}

.c3 input[type='password']::-webkit-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c3 input[type='password']::-moz-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c3 input[type='password']:-ms-input-placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c3 input[type='password']::placeholder {
  -webkit-letter-spacing: initial;
  -moz-letter-spacing: initial;
  -ms-letter-spacing: initial;
  letter-spacing: initial;
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  text-overflow: ellipsis;
  background-color: transparent;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-12) var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c5::-ms-reveal,
.c5::-ms-clear {
  display: none;
}

.c11 {
  position: 'absolute';
  overflow: hidden;
  width: 1px;
  height: 1px;
}

.c6 {
  position: absolute;
  right: 0;
  top: 0;
}

@media (hover:hover) {
  .c8:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c8:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-labelledby="no-label"
        class="c2 c3"
        id="no-label:group"
        role="group"
      >
        <input
          aria-invalid="false"
          autocapitalize="none"
          autocorrect="off"
          class="c4 c5"
          id="no-label"
          name="no-label"
          spellcheck="false"
          type="password"
        />
        <div
          class="c6"
        >
          <button
            aria-controls="no-label"
            aria-label="パスワードを表示する"
            aria-pressed="false"
            class="c7"
            data-testid="reveal-password"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            type="button"
          >
            <div
              class="c8"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c9"
              >
                <svg
                  class="c10"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16C13.25 16 14.3125 15.5625 15.1875 14.6875C16.0625 13.8125 16.5 12.75 16.5 11.5C16.5 10.25 16.0625 9.1875 15.1875 8.3125C14.3125 7.4375 13.25 7 12 7C10.75 7 9.6875 7.4375 8.8125 8.3125C7.9375 9.1875 7.5 10.25 7.5 11.5C7.5 12.75 7.9375 13.8125 8.8125 14.6875C9.6875 15.5625 10.75 16 12 16ZM12 14.2C11.25 14.2 10.6125 13.9375 10.0875 13.4125C9.5625 12.8875 9.3 12.25 9.3 11.5C9.3 10.75 9.5625 10.1125 10.0875 9.5875C10.6125 9.0625 11.25 8.8 12 8.8C12.75 8.8 13.3875 9.0625 13.9125 9.5875C14.4375 10.1125 14.7 10.75 14.7 11.5C14.7 12.25 14.4375 12.8875 13.9125 13.4125C13.3875 13.9375 12.75 14.2 12 14.2ZM12 19C9.56667 19 7.35 18.3208 5.35 16.9625C3.35 15.6042 1.9 13.7833 1 11.5C1.9 9.21667 3.35 7.39583 5.35 6.0375C7.35 4.67917 9.56667 4 12 4C14.4333 4 16.65 4.67917 18.65 6.0375C20.65 7.39583 22.1 9.21667 23 11.5C22.1 13.7833 20.65 15.6042 18.65 16.9625C16.65 18.3208 14.4333 19 12 19ZM12 17C13.8833 17 15.6125 16.5042 17.1875 15.5125C18.7625 14.5208 19.9667 13.1833 20.8 11.5C19.9667 9.81667 18.7625 8.47917 17.1875 7.4875C15.6125 6.49583 13.8833 6 12 6C10.1167 6 8.3875 6.49583 6.8125 7.4875C5.2375 8.47917 4.03333 9.81667 3.2 11.5C4.03333 13.1833 5.2375 14.5208 6.8125 15.5125C8.3875 16.5042 10.1167 17 12 17Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
        <div
          aria-live="assertive"
          class="c11"
        />
      </div>
    </div>
  </div>
</div>
`;

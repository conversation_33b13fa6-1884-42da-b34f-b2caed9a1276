import React from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../PasswordField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('PasswordField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<PasswordField name="basic-password" label="Password" />, task);
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <div style={{ margin: '5px' }}>
        <PasswordField name="focus-password" label="Focus Password" />
      </div>,
      task,
      {
        interactionSelector: 'input[type="password"]',
        interactionType: 'focus',
      },
    );
  });

  test('Required', async ({ task }) => {
    await takeScreenshot(<PasswordField name="required-password" label="Required Password" required />, task);
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(<PasswordField name="no-label" />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<PasswordField name="password-disabled" label="Password Disabled" disabled />, task);
  });

  test('Placeholder', async ({ task }) => {
    await takeScreenshot(<PasswordField name="placeholder-password" label="Placeholder Password" placeholder="Some placeholder text" />, task);
  });

  test('DefaultValue', async ({ task }) => {
    await takeScreenshot(<PasswordField name="default-value-password" label="Default Value Password" defaultValue="Default value" />, task);
  });

  test('Error', async ({ task }) => {
    await takeScreenshot(<PasswordField name="password-with-error" label="Password With Error" errorMessage="Danger danger danger!!!" />, task);
  });

  test('RevealedPassword', async ({ task }) => {
    await takeScreenshot(<PasswordField name="revealed-password" label="Revealed Password" defaultValue="Some password" />, task, {
      interactionSelector: 'button[data-testid="reveal-password"]',
      interactionType: 'click',
    });
  });
});

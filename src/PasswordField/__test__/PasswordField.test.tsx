import React from 'react';
import { userEvent } from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import PasswordField from '../PasswordField';

describe('TextField', () => {
  test('renders without crashing', () => {
    const { container } = render(<PasswordField name="basic-password" label="Password" />);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { container } = render(<PasswordField name="no-label" />);
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with disabled ${condition}`, () => {
      const { container } = render(<PasswordField name="basic-password" label="Password" disabled={condition} />);
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with required ${condition}`, () => {
      const { container } = render(<PasswordField name="basic-password" label="Password" required={condition} />);
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders without crashing with error`, () => {
    const { getByText, container } = render(<PasswordField name="basic-password" label="Password" errorMessage="Error" />);
    const testElement = getByText('Error');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test(`renders without crashing and switches password visibility`, async () => {
    const { getByText, getByTestId, container } = render(
      <PasswordField data-testid="basic-password" name="basic-password" label="Password" errorMessage="Error" />,
    );
    const testElement = getByText('Error');
    expect(testElement).toBeInTheDocument();
    const revealButton = getByTestId('reveal-password');
    const input = getByTestId('basic-password');
    await userEvent.click(revealButton);
    await waitFor(() => expect(input).toHaveAttribute('type', 'text'));
    expect(container).toMatchSnapshot();
    await userEvent.click(revealButton);
    await waitFor(() => expect(input).toHaveAttribute('type', 'password'));
    expect(container).toMatchSnapshot();
  });
});

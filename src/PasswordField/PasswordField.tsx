import React, { forwardRef, useId, useState } from 'react';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import checkHasError from '../utils/checkHasError';
import VisibilityOffIcon from '../Icons/VisibilityOffIcon';
import VisibilityIcon from '../Icons/VisibilityIcon';
import IconButton from '../IconButton';
import PasswordFieldGroup from './PasswordFieldGroup';
import PasswordFieldInput from './PasswordFieldInput';
import PasswordFieldAssertiveText from './PasswordFieldAssertiveText';
import PasswordFieldActionContainer from './PasswordFieldActionContainer';

export type PasswordFieldProps = CommonFieldBaseProps &
  Omit<React.ComponentPropsWithoutRef<'input'>, 'type' | 'autoCapitalize' | 'autoCorrect' | 'spellCheck'>;

const PasswordField = forwardRef<HTMLInputElement, PasswordFieldProps>(
  ({ id, name, label, showError, errorMessage, required = false, disabled = false, sx, showRequiredIndicator, ...rest }, ref) => {
    const fallbackId = `password-field-id-${useId()}`;
    const hasError = checkHasError(showError, errorMessage);
    const [isPasswordRevealed, setPasswordRevealed] = useState(false);
    const [assertiveText, setAssertiveText] = useState('');
    const elementId = id || name || fallbackId;
    const togglePasswordReveal = () => {
      const newPasswordRevealed = !isPasswordRevealed;
      setPasswordRevealed(newPasswordRevealed);
      setAssertiveText(newPasswordRevealed ? 'パスワードが表示されています' : 'パスワードが非表示になっています');
    };
    return (
      <FieldBase
        id={elementId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <PasswordFieldGroup id={`${elementId}:group`} role="group" aria-disabled={disabled} aria-label={label} aria-labelledby={elementId}>
          <PasswordFieldInput
            ref={ref}
            id={elementId}
            type={isPasswordRevealed ? 'text' : 'password'}
            aria-invalid={hasError}
            disabled={disabled}
            name={name}
            required={required}
            autoCapitalize="none"
            autoCorrect="off"
            spellCheck="false"
            {...rest}
          />
          <PasswordFieldActionContainer>
            <IconButton
              aria-pressed={isPasswordRevealed}
              aria-controls={elementId}
              aria-label="パスワードを表示する"
              data-testid="reveal-password"
              icon={isPasswordRevealed ? <VisibilityOffIcon /> : <VisibilityIcon />}
              onClick={togglePasswordReveal}
              disabled={disabled}
              color="grey"
            />
          </PasswordFieldActionContainer>
          <PasswordFieldAssertiveText aria-live="assertive">{assertiveText}</PasswordFieldAssertiveText>
        </PasswordFieldGroup>
      </FieldBase>
    );
  },
);

PasswordField.displayName = 'PasswordField';

export default PasswordField;

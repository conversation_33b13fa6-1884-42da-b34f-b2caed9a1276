In addition to props above, &lt;PasswordField&gt; accepts any default props for _input_ HTML tags.

### Default PasswordField:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="basic-input" label="Basic PasswordField" />;
```

### Required:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="required-input" label="Required PasswordField" required={true} />;
```

### Without Label:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="no-label" />;
```

### Disabled:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="input-disabled" label="PasswordField Disabled" disabled={true} placeholder="Some placeholder text" />;
```

### With placeholder:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="placeholder-input" label="Placeholder PasswordField" placeholder="PasswordField" />;
```

### With Default value:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<PasswordField name="default-value-input" label="Default Value PasswordField" defaultValue="Some password" />;
```

### With error:

```js
import PasswordField from '@axa-japan/design-system-react/PasswordField';

<div style={{ width: '200px' }}>
  <PasswordField name="input-with-error" label="PasswordField With Error" errorMessage="エラーテキストが長い場合、数行に表示します" />
</div>;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the underlying &lt;input&gt; component.

```js
import React, { useRef } from 'react';
import PasswordField from '@axa-japan/design-system-react/PasswordField';

const ref = useRef(null);
<PasswordField ref={ref} name="Ref" label="Ref" />;
```

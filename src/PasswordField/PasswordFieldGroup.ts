import styled from 'styled-components';
import input from '../styles/input';
import { getColorVar } from '../colors';

const PasswordFieldGroup = styled.div`
  ${input.base}
  ${input.baseBorder}
  position: relative;

  &:focus-within:not([aria-disabled='true'], :has(button:focus)) {
    ${input.focusBoxShadow}
  }

  &[aria-disabled='true'] {
    ${input.disabledBorder}
    background-color: ${getColorVar('interactiveDisabledLight')};
    color: ${getColorVar('interactiveDisabledDark')};
    cursor: not-allowed;
  }

  &:has(input[aria-invalid='true']) {
    border: 2px solid ${getColorVar('statusDanger')};
  }

  input[type='password'] {
    letter-spacing: 0.16em;
  }

  input[type='password']::placeholder {
    letter-spacing: initial;
  }

  display: flex;
`;

export default PasswordFieldGroup;

import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';

const getHeaderTopNavLinkStyles = (color?: 'blue') => css`
  color: ${getColorVar('grey700')};
  display: block;
  font-size: 13px;
  font-weight: bold;
  text-decoration: none;
  padding: ${getSpacingVar(1)} 30px;

  &:visited {
    color: ${getColorVar('grey700')};
  }

  &:hover {
    color: ${getColorVar('interactiveHoverPrimary')};
    text-decoration: none;
  }

  ${color === 'blue' &&
  css`
    align-items: center;
    background-color: ${getColorVar('utilityBackgroundOcean')};
    color: ${getColorVar('white')};
    display: flex;
    height: 100%;
    justify-content: center;
    padding: 0 30px;

    &:visited {
      color: ${getColorVar('white')};
    }

    &:hover {
      background-color: ${getColorVar('interactiveHoverPrimary')};
      color: ${getColorVar('white')};
    }
  `}
`;

export const HeaderTopNavAnchorLink = styled('a')<{ color?: 'blue' }>`
  ${({ color }) => getHeaderTopNavLinkStyles(color)}
`;

export const HeaderTopNavRouterLink = styled(Link)<{ color?: 'blue' }>`
  ${({ color }) => getHeaderTopNavLinkStyles(color)}
`;

export const HeaderTopNavLinkBase = styled.div`
  display: inline-flex;
  height: 100%;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${getColorVar('grey800')};
    background-color: ${getColorVar('utilityBackgroundWhite')};
  }

  & + & ${HeaderTopNavAnchorLink}, & + & ${HeaderTopNavRouterLink} {
    border-left: ${getSpacingVar(0.5)} solid ${getColorVar('grey300')};
  }
`;

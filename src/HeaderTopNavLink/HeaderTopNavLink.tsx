import React from 'react';
import { LinkProps } from 'react-router-dom';
import { HeaderTopNavAnchorLink, HeaderTopNavLinkBase, HeaderTopNavRouterLink } from './HeaderTopNavLink.styles';
import LinkBase from '../OldLinkBase/OldLinkBase';

export type HeaderTopNavLinkProps = {
  /** URL for where you want the button to go to */
  to: string;
  /** Choose to use react router */
  useRouter?: boolean;
  /** Change the color to blue */
  color?: 'blue';
} & (React.ComponentPropsWithoutRef<'a'> | LinkProps);

const HeaderTopNavLink: React.FC<HeaderTopNavLinkProps> = (props) => {
  const { children, ...rest } = props;

  return (
    <HeaderTopNavLinkBase>
      <LinkBase linkComponent={HeaderTopNavRouterLink} anchorComponent={HeaderTopNavAnchorLink} {...rest}>
        {children}
      </LinkBase>
    </HeaderTopNavLinkBase>
  );
};

export default HeaderTopNavLink;

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Breadcrumb > renders breadcrumb 1`] = `
.c0 {
  margin: var(--ajds-Breadcrumb-margin-top,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-right,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-bottom,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-left,var(--ajds-Breadcrumb-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style-type: none;
  row-gap: var(--ajds-spacing-2);
}

.c2 {
  color: var(--ajds-color-character-primary);
}

.c2:not(:last-of-type)::after {
  content: '/';
  margin: 0 8px;
  font-weight: 800;
  color: var(--ajds-color-character-accent);
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c3 svg {
  color: currentColor;
}

.c3:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <nav
    aria-label="パンくずリスト"
    class="c0"
  >
    <ul
      class="c1"
    >
      <li
        class="c2"
        role="listitem"
      >
        <a
          class="c3"
          href="/"
          style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
        >
          HOME
        </a>
      </li>
      <li
        class="c2"
        role="listitem"
      >
        <a
          class="c3"
          href="/"
          style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
        >
          Library
        </a>
      </li>
      <li
        aria-current="page"
        class="c2"
        role="listitem"
      >
        <span>
          Data
        </span>
      </li>
    </ul>
  </nav>
</div>
`;

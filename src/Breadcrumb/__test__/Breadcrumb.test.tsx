import React from 'react';
import { render } from '../../utils/testUtils';
import Breadcrumb from '../Breadcrumb';

describe('Breadcrumb', () => {
  test(`renders breadcrumb`, () => {
    const { getByText, container } = render(<Breadcrumb items={[{ text: 'HOME', to: '/' }, { text: 'Library', to: '/' }, { text: 'Data' }]} />);
    const homeElement = getByText(/HOME/i);
    const libraryElement = getByText(/Library/i);
    const dataElement = getByText(/Data/i);
    expect(homeElement).toBeInTheDocument();
    expect(libraryElement).toBeInTheDocument();
    expect(dataElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

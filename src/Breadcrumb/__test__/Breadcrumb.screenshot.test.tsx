import React from 'react';
import Breadcrumb from '../Breadcrumb';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Breadcrumb', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Breadcrumb items={[{ text: 'HOME', to: '/' }, { text: 'Library', to: '/' }, { text: 'data' }]} />, task);
  });

  test('Long', async ({ task }) => {
    await takeScreenshot(
      <Breadcrumb
        items={[
          { text: 'HOME', to: '/' },
          { text: 'Library', to: '/' },
          { text: 'data' },
          { text: 'data 2' },
          { text: 'data 3' },
          { text: 'data 4' },
          { text: 'data 5' },
          { text: 'data 6' },
          { text: 'data 7' },
        ]}
      />,
      task,
    );
  });
});

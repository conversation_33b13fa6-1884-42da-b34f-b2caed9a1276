import React from 'react';
import type { MarginSxPropType } from '../sx';
import { BreadcrumbBase, useSx } from './BreadcrumbBase';
import BreadcrumbList from './BreadcrumbList';
import BreadcrumbItem from './BreadcrumbItem';
import Link from '../Link';

export type BreadcrumbProps = {
  /** List of breadcrumb links */
  items: {
    text: string;
    to?: string;
  }[];
  /** Style overrides */
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'nav'>;

const Breadcrumb: React.FC<BreadcrumbProps> = ({ id, items, sx, ...rest }) => {
  return (
    <BreadcrumbBase {...rest} style={useSx(sx, {})} aria-label="パンくずリスト">
      <BreadcrumbList>
        {items.map((item, index) => {
          const key = id ? `${id}-${item.text}` : `${item.text}-${item.to || ''}`;
          if (index < items.length - 1) {
            return (
              <BreadcrumbItem key={key} role="listitem">
                <Link href={item.to}>{item.text}</Link>
              </BreadcrumbItem>
            );
          }
          return (
            <BreadcrumbItem key={key} role="listitem" aria-current="page">
              <span>{item.text}</span>
            </BreadcrumbItem>
          );
        })}
      </BreadcrumbList>
    </BreadcrumbBase>
  );
};

export default Breadcrumb;

In addition to props above, &lt;Breadcrumb&gt; accepts any default props for _div_ HTML tags.

### Breadcrumb Example:

```js
import Breadcrumb from '@axa-japan/design-system-react/Breadcrumb';

<Breadcrumb items={[{ text: 'Home', to: '/' }, { text: 'Library' }]} />;
```

```js
import Breadcrumb from '@axa-japan/design-system-react/Breadcrumb';

const breadcrumbs = [{ text: 'ページ１', to: '/' }, { text: 'ページ２', to: '/library' }, { text: 'ページ３' }];
<Breadcrumb items={breadcrumbs} />;
```

```js
import Breadcrumb from '@axa-japan/design-system-react/Breadcrumb';

const breadcrumbs = [
  { text: 'Some', to: '/' },
  { text: 'Super', to: '/' },
  { text: 'Duper', to: '/' },
  { text: 'Extremely', to: '/' },
  { text: 'Laughably', to: '/' },
  { text: 'Ludicrously', to: '/' },
  { text: 'Preposterously', to: '/' },
  { text: 'Comically', to: '/' },
  { text: 'Unusually', to: '/' },
  { text: 'Absurdly', to: '/' },
  { text: 'Stupidly', to: '/' },
  { text: 'Ridiculously', to: '/' },
  { text: 'Extraordinarily', to: '/' },
  { text: 'Long Breadcrumb' },
];
<Breadcrumb items={breadcrumbs} />;
```

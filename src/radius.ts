const radius = {
  none: '0', // 0px
  xs: '0.125rem', // 2px
  sm: '0.25rem', // 4px
  md: '0.375rem', // 6px
  lg: '0.5rem', // 8px
  full: 'calc(infinity * 1px)',
} as const;

// Extract the keys of the radius object while keeping their original types
export const radiusKeys = Object.keys(radius).map((key) => (Number.isNaN(+key) ? key : +key)) as Array<keyof typeof radius>;

type CssRadiusVarMapType = {
  [K in keyof typeof radius]: {
    name: `--ajds-radius-${K}`;
    value: string;
  };
};

const createCssVarName = (key: keyof typeof radius): string => {
  return `--ajds-radius-${String(key).replace('.', '-')}`;
};

export const cssRadiusVarMap = radiusKeys.reduce<CssRadiusVarMapType>(
  (acc, key) => ({
    ...acc,
    [key]: { name: createCssVarName(key), value: radius[key] },
  }),
  {} as CssRadiusVarMapType,
);

export type RadiusKeysType = keyof typeof radius;

export function getRadiusVar(key: RadiusKeysType): string {
  return `var(${(cssRadiusVarMap[String(key) as keyof typeof cssRadiusVarMap] || {}).name})`;
}

export default radius;

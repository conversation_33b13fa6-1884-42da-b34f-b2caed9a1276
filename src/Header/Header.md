### Example header:

```js
import Header from '@axa-japan/design-system-react/Header';
import HeaderTopNav from '@axa-japan/design-system-react/HeaderTopNav';
import HeaderTopNavLink from '@axa-japan/design-system-react/HeaderTopNavLink';
import HeaderMainNav from '@axa-japan/design-system-react/HeaderMainNav';
import HeaderMainNavSection from '@axa-japan/design-system-react/HeaderMainNavSection';
import HeaderSubNavColumn from '@axa-japan/design-system-react/HeaderSubNavColumn';
import HeaderMobileNav from '@axa-japan/design-system-react/HeaderMobileNav';
import OffCanvasNavSection from '@axa-japan/design-system-react/OffCanvasNavSection';
import OffCanvasNavLink from '@axa-japan/design-system-react/OffCanvasNavLink';

<Header>
  <HeaderTopNav>
    <HeaderTopNavLink to="#">LINK 1</HeaderTopNavLink>
    <HeaderTopNavLink to="#">LINK 2</HeaderTopNavLink>
    <HeaderTopNavLink to="#">LINK 3</HeaderTopNavLink>
    <HeaderTopNavLink to="#" color="blue">
      LINK 4
    </HeaderTopNavLink>
  </HeaderTopNav>
  <HeaderMainNav headerLogoUseRouter={false}>
    <HeaderMainNavSection to="#" label="MAIN NAV 1" />
    <HeaderMainNavSection to="#" label="MAIN NAV 2">
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 1' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 2' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 3' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
    </HeaderMainNavSection>
    <HeaderMainNavSection to="#" label="MAIN NAV 3">
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 1' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 2' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 3' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
      <HeaderSubNavColumn
        title={{ to: '#', label: 'Title Label 4' }}
        links={[
          { to: '#1', label: 'Link 1' },
          { to: '#2', label: 'Link 2' },
          { to: '#3', label: 'Link 3' },
        ]}
      />
    </HeaderMainNavSection>
  </HeaderMainNav>
  <HeaderMobileNav>
    <OffCanvasNavSection label="Section 1">
      <OffCanvasNavLink label="Link 1" to="#1" />
      <OffCanvasNavLink label="Link 2" to="#2" />
      <OffCanvasNavLink label="Link 3" to="#3" />
      <OffCanvasNavSection label="Sub Section 1">
        <OffCanvasNavLink label="Link 1" to="#1" />
        <OffCanvasNavLink label="Link 2" to="#2" />
        <OffCanvasNavLink label="Link 3" to="#3" />
      </OffCanvasNavSection>
    </OffCanvasNavSection>
    <OffCanvasNavSection label="Section 2">
      <OffCanvasNavSection label="Sub Section 1">
        <OffCanvasNavSection label="Sub Sub Section 1">
          <OffCanvasNavLink label="Link 1" to="#1" />
          <OffCanvasNavLink label="Link 2" to="#2" />
          <OffCanvasNavLink label="Link 3" to="#3" />
        </OffCanvasNavSection>
        <OffCanvasNavLink label="Link 1" to="#1" />
        <OffCanvasNavLink label="Link 2" to="#2" />
        <OffCanvasNavLink label="Link 3" to="#3" />
      </OffCanvasNavSection>
    </OffCanvasNavSection>
    <OffCanvasNavSection label="Section 3">
      <OffCanvasNavSection label="Sub Section 1">
        <OffCanvasNavLink label="Link 1" to="#1" />
        <OffCanvasNavLink label="Link 2" to="#2" />
        <OffCanvasNavLink label="Link 3" to="#3" />
      </OffCanvasNavSection>
    </OffCanvasNavSection>
  </HeaderMobileNav>
</Header>;
```

import React from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { render } from '../../utils/testUtils';
import IconButton from '../IconButton';
import { AddIcon } from '../../Icons';

// Mock the ResizeObserver
const ResizeObserverMock = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Stub the global ResizeObserver
vi.stubGlobal('ResizeObserver', ResizeObserverMock);

describe('IconButton', () => {
  test('renders without crashing', () => {
    const { getByTestId, container } = render(<IconButton icon={<AddIcon />} data-testid="test" />);
    const testElement = getByTestId(/test/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  (['blue', 'grey', 'white'] as const).forEach((color) => {
    test(`renders with color ${color} without crashing`, () => {
      const { getByTestId, container } = render(<IconButton icon={<AddIcon />} color={color} data-testid="test" />);
      const testElement = getByTestId(/test/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test('renders without crashing with tooltip', async () => {
    const { getByTestId, container } = render(<IconButton icon={<AddIcon />} tooltip={{ text: 'tooltip' }} data-testid="test" />);
    const tooltipTrigger = getByTestId('test');
    expect(tooltipTrigger).toBeInTheDocument();

    await userEvent.hover(tooltipTrigger);

    await waitFor(() => {
      expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('open');
    });

    await userEvent.unhover(tooltipTrigger);

    await waitFor(() => {
      expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('closed');
    });
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled true', () => {
    const { getByTestId, container } = render(<IconButton icon={<AddIcon />} disabled={true} data-testid="test" />);
    const testElement = getByTestId(/test/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`IconButton > renders with color blue without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    class="c0"
    data-testid="test"
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
    type="button"
  >
    <div
      class="c1"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
</div>
`;

exports[`IconButton > renders with color grey without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    class="c0"
    data-testid="test"
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
    type="button"
  >
    <div
      class="c1"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
</div>
`;

exports[`IconButton > renders with color white without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    class="c0"
    data-testid="test"
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
    type="button"
  >
    <div
      class="c1"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
</div>
`;

exports[`IconButton > renders without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    class="c0"
    data-testid="test"
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
    type="button"
  >
    <div
      class="c1"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
</div>
`;

exports[`IconButton > renders without crashing with disabled true 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    class="c0"
    data-testid="test"
    disabled=""
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
    type="button"
  >
    <div
      class="c1"
      data-disabled="true"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
</div>
`;

exports[`IconButton > renders without crashing with tooltip 1`] = `
.c7 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c5 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c6 {
  --arrow-background: var(--ajds-color-interactive-active-grey);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-grey);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-xs);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2) var(--ajds-spacing-1) var(--ajds-spacing-2);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c1 svg {
  color: currentColor;
}

.c1[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c2 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c1:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <button
    aria-label="tooltip"
    class="c0"
    data-part="trigger"
    data-scope="tooltip"
    data-state="closed"
    data-testid="test"
    dir="ltr"
    id="tooltip::r0::trigger"
    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
    type="button"
  >
    <div
      class="c1"
      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
    >
      <span
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
          />
        </svg>
      </span>
    </div>
  </button>
  <div
    data-part="positioner"
    data-scope="tooltip"
    dir="ltr"
    id="tooltip::r0::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(var(--x), var(--y), 0); z-index: var(--z-index); --transform-origin: top center; --reference-width: 0px; --available-width: -16px; --available-height: -16px; --x: 0px; --y: 8px;"
  >
    <div
      class="c4"
      data-part="content"
      data-placement="bottom"
      data-scope="tooltip"
      data-state="closed"
      data-testid="tooltip-content"
      dir="ltr"
      id="tooltip::r0::content"
      role="tooltip"
      style="pointer-events: none;"
    >
      <div
        class="c5"
        data-part="arrow"
        data-scope="tooltip"
        data-testid="tooltip-arrow"
        dir="ltr"
        id="tooltip::r0::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1); left: 0px; bottom: calc(100% + var(--arrow-offset));"
      >
        <div
          class="c6"
          data-part="arrow-tip"
          data-scope="tooltip"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit; transform: rotate(45deg);"
        />
      </div>
      <span
        class="c7"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        tooltip
      </span>
    </div>
  </div>
</div>
`;

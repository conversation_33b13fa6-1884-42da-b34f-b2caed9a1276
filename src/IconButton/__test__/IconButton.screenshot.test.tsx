import React from 'react';
import IconButton from '../IconButton';
import Stack from '../../Stack';
import { AddIcon } from '../../Icons';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('IconButton', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<IconButton icon={<AddIcon />} />, task);
  });

  test('Blue', async ({ task }) => {
    await takeScreenshot(<IconButton icon={<AddIcon />} color="blue" />, task);
  });

  test('Grey', async ({ task }) => {
    await takeScreenshot(<IconButton icon={<AddIcon />} color="grey" />, task);
  });

  test('White', async ({ task }) => {
    await takeScreenshot(
      <Stack sx={{ 'background-color': 'utilityBackgroundOcean', padding: 3 }}>
        <IconButton icon={<AddIcon />} color="white" />
      </Stack>,
      task,
    );
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<IconButton icon={<AddIcon />} disabled={true} />, task);
  });
});

import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';
import button from '../styles/button';

const variants: VariantsType<'color'> = {
  color: {
    blue: {
      color: getColorVar('interactiveActivePrimary'),
      'color:hover': getColorVar('interactiveHoverPrimary'),
      'background-color:hover': getColorVar('interactiveHoverPrimaryTransparent'),
    },
    grey: {
      color: getColorVar('interactiveActiveGrey'),
      'color:hover': getColorVar('interactiveActiveGrey'),
      'background-color:hover': getColorVar('interactiveHoverGreyTransparent'),
    },
    white: {
      color: getColorVar('interactiveActiveWhite'),
      'color:hover': getColorVar('interactiveHoverGrey'),
      'background-color:hover': getColorVar('interactiveHoverWhiteTransparent'),
    },
  },
};

const { useSx, getSxStyleRules } = sx('IconButtonContainerBase', ['margin'], variants);

export { useSx };

export const IconButtonContainerBase = styled.div`
  ${button.base}

  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: ${getSpacingVar(10)};
  height: ${getSpacingVar(10)};
  background-color: transparent;
  border: none;

  ${getSxStyleRules()};

  /* Move disabled state after so that they override the variant styles */
  &[data-disabled='true'] {
    ${button.disabled};
    background-color: transparent;
  }
`;

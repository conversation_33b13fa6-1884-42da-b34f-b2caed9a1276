import styled from 'styled-components';
import sx, { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';
import button from '../styles/button';
import { getColorVar } from '../colors';
import { getRadiusVar } from '../radius';

const variants: VariantsType<'color'> = {
  color: {
    blue: {
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
    },
    grey: {
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
    },
    white: {
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusWhite')}`,
    },
  },
};

const { useSx, getSxStyleRules } = sx('IconButtonTargetBase', ['margin'], variants);

export { useSx };

export const IconButtonTargetBase = styled.button`
  ${button.base}

  flex-shrink: 0;
  width: ${getSpacingVar(12)};
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: ${getRadiusVar('lg')};

  ${getSxStyleRules()};

  /* Move disabled state after so that they override the variant styles */
  &:disabled {
    ${button.disabled};
  }
`;

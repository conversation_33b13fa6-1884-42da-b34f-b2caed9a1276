In addition to props above, _IconButton_ accepts any default props for _button_ HTML tag.

### Default IconButton:

```js
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';

<IconButton icon={<AddIcon />} aria-label="Add task" />;
```

### More Icons:

You can see all the icons available on this [link](/#/Icons).

```js
import Stack from '@axa-japan/design-system-react/Stack';
import IconButton from '@axa-japan/design-system-react/IconButton';
import { DownloadIcon, PhoneIcon, CheckIcon, ExternalIcon, InformationIcon } from '@axa-japan/design-system-react/Icons';

<Stack spacing={2} sx={{ padding: 2 }}>
  <IconButton icon={<DownloadIcon />} aria-label="ファイルをダウンロード" />
  <IconButton icon={<PhoneIcon />} aria-label="担当者に電話" />
  <IconButton icon={<CheckIcon />} aria-label="タスクを完了" />
  <IconButton icon={<InformationIcon />} aria-label="詳しく読む" />
  <IconButton icon={<ExternalIcon />} aria-label="新しいタブで開く" />
</Stack>;
```

### IconButton Colors:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';
<>
  <Stack spacing={2} sx={{ padding: 2 }}>
    <IconButton icon={<AddIcon />} color="blue" aria-label="Add task" />
    <IconButton icon={<AddIcon />} color="grey" aria-label="Add task" />
  </Stack>
  <Stack spacing={2} sx={{ padding: 2, 'background-color': 'utilityBackgroundOcean' }}>
    <IconButton icon={<AddIcon />} color="white" aria-label="Add task" />
  </Stack>
</>;
```

### Disabled:

```js
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';

<IconButton icon={<AddIcon />} disabled={true} aria-label="Add task" />;
```

### With tooltip

Note: If you add tooltip, the text will automatically be used as aria-label. So if aria-label is manually set, it will not be used.

Hover it:

```js
import IconButton from '@axa-japan/design-system-react/IconButton';
import { ExternalIcon } from '@axa-japan/design-system-react/Icons';

<IconButton icon={<ExternalIcon />} tooltip={{ text: '新しいタブで開く', placement: 'bottom' }} />;
```

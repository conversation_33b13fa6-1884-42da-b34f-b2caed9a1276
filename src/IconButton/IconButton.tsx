import React, { forwardRef } from 'react';
import Tooltip from '../Tooltip/Tooltip';
import { IconButtonTargetBase, useSx as useOuterButtonSx } from './IconButtonTargetBase';
import { IconButtonContainerBase, useSx as useInnerButtonSx } from './IconButtonContainerBase';
import { ButtonIcon } from '../ButtonIcon/ButtonIcon';
import type { MarginSxPropType } from '../sx';

export type IconButtonProps = {
  /** Component to use for the icon
   * In the future, could have a simple "icon" prop to
   * choose from a pre-existing list of icons.
   */
  icon: React.ReactElement;
  /** Changes the color of the button */
  color?: 'blue' | 'grey' | 'white';
  /** The tooltip parameters */
  tooltip?: {
    text: string;
    placement?: 'top' | 'bottom';
  };
  /** Style overrides */
  sx?: MarginSxPropType;
} & Omit<React.ComponentPropsWithRef<'button'>, 'children' | 'color'>;

const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(({ icon, color = 'blue', tooltip, type = 'button', sx, ...rest }, ref) => {
  const outerButtonSx = useOuterButtonSx(sx, { color });
  const innerButtonSx = useInnerButtonSx({}, { color });

  const iconButtonComponent = (
    <IconButtonTargetBase {...rest} ref={ref} aria-label={tooltip ? tooltip.text : rest['aria-label']} style={outerButtonSx} type={type}>
      <IconButtonContainerBase data-disabled={rest.disabled} style={innerButtonSx}>
        <ButtonIcon>{icon}</ButtonIcon>
      </IconButtonContainerBase>
    </IconButtonTargetBase>
  );

  if (tooltip?.text) {
    return (
      <Tooltip text={tooltip.text} placement={tooltip.placement}>
        {iconButtonComponent}
      </Tooltip>
    );
  }

  return iconButtonComponent;
});

IconButton.displayName = 'IconButton';

export default IconButton;

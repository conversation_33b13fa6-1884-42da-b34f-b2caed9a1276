import React from 'react';
import { render } from '../../utils/testUtils';
import TextField from '../TextField';

describe('TextField', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<TextField name="basic-input" label="Basic TextField" />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { getByRole, container } = render(<TextField name="no-label" />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with disabled ${condition}`, () => {
      const { getByRole, container } = render(<TextField name="basic-input" label="Basic Textarea" disabled={condition} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with required ${condition}`, () => {
      const { getByRole, container } = render(<TextField name="basic-input" label="Basic Textarea" required={condition} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders without crashing with input width`, () => {
    const { getByRole, container } = render(<TextField name="basic-input" label="Text Field with input width" inputWidth="20" />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test(`renders without crashing with error`, () => {
    const { getByText, container } = render(<TextField name="basic-input" label="Basic Textarea" errorMessage="Error" />);
    const testElement = getByText('Error');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders with suffix and prefix', () => {
    const { getByRole, container } = render(<TextField name="with-prefix-suffix" suffix="接尾辞" prefix="接頭辞" />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

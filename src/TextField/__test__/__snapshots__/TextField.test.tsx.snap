// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TextField > renders with suffix and prefix 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c2 {
  overflow-wrap: anywhere;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <span
        aria-hidden="true"
        class="c2"
      >
        接頭辞
      </span>
      <input
        aria-invalid="false"
        class="c3 c4"
        id="with-prefix-suffix"
        name="with-prefix-suffix"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
      <span
        aria-hidden="true"
        class="c2"
      >
        接尾辞
      </span>
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:r1:"
    >
      Basic TextField
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c3 c4"
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with disabled false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:r6:"
    >
      Basic Textarea
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c3 c4"
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with disabled true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:r4:"
    >
      Basic Textarea
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c3 c4"
        disabled=""
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with error 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:re:"
    >
      Basic Textarea
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="true"
        class="c3 c4"
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
    <div
      aria-live="polite"
      class="c5"
      role="alert"
    >
      <div
        class="c6"
      >
        <svg
          class="c7"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      Error
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with input width 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:rc:"
    >
      Text Field with input width
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c3 c4"
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: var(--ajds-spacing-20);"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with required false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:ra:"
    >
      Basic Textarea
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c3 c4"
        id="basic-input"
        name="basic-input"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing with required true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c6:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c6:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="basic-input"
      id="field-label-:r8:"
    >
      Basic Textarea
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </label>
    <div
      class="c4 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c5 c6"
        id="basic-input"
        name="basic-input"
        required=""
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

exports[`TextField > renders without crashing without label 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <input
        aria-invalid="false"
        class="c2 c3"
        id="no-label"
        name="no-label"
        style="--ajds-TextFieldBase-max-width: 100%;"
        type="text"
      />
    </div>
  </div>
</div>
`;

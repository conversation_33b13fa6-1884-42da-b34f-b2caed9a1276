import React from 'react';
import <PERSON><PERSON>ield from '../TextField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('TextField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<TextField name="basic-input" label="Basic TextField" />, task);
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <div style={{ margin: '5px' }}>
        <TextField name="focus-input" label="Focus TextField" />
      </div>,
      task,
      {
        interactionSelector: 'input[type="text"]',
        interactionType: 'focus',
      },
    );
  });

  test('Required', async ({ task }) => {
    await takeScreenshot(<TextField name="required-input" label="Required TextField" required />, task);
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(<TextField name="no-label" />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<TextField name="input-disabled" label="TextField Disabled" disabled />, task);
  });

  test('Placeholder', async ({ task }) => {
    await takeScreenshot(<TextField name="placeholder-input" label="Placeholder TextField" placeholder="Some placeholder text" />, task);
  });

  test('DefaultValue', async ({ task }) => {
    await takeScreenshot(<TextField name="default-value-input" label="Default Value TextField" defaultValue="Default value" />, task);
  });

  test('Error', async ({ task }) => {
    await takeScreenshot(<TextField name="input-with-error" label="TextField With Error" errorMessage="Danger danger danger!!!" />, task);
  });

  test('PrefixSuffix', async ({ task }) => {
    await takeScreenshot(<TextField name="input-with-prefix-suffix" label="TextField With Prefix Suffix" suffix="接尾辞" prefix="接頭辞" />, task);
  });

  test('WithInputWidth', async ({ task }) => {
    await takeScreenshot(<TextField name="input-with-input-width" label="TextField With Input Width" inputWidth="20" />, task);
  });

  test('WithMultilineError', async ({ task }) => {
    await takeScreenshot(
      <TextField
        name="input-with-multiline-error"
        label="TextField With Multiline Error"
        errorMessage={'エラーが発生しました\n項目１\n項目２\n項目３'}
      />,
      task,
    );
  });
});

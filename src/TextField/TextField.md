In addition to props above, &lt;TextField&gt; accepts any default props for _input_ HTML tags.

### Default TextField:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="basic-input" label="Basic TextField" />;
```

### Required:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="required-input" label="Required TextField" required={true} />;
```

### Without Label:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="no-label" />;
```

### Disabled:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="input-disabled" label="TextField Disabled" disabled={true} placeholder="Some placeholder text" />;
```

### With placeholder:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="placeholder-input" label="Placeholder TextField" placeholder="Some placeholder text" />;
```

### With Default value:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="default-value-input" label="Default Value TextField" defaultValue="Default value" />;
```

### With error:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<div style={{ width: '200px' }}>
  <TextField name="input-with-error" label="TextField With Error" errorMessage="エラーテキストが長い場合、数行に表示します" />
</div>;
```

### With prefix and suffix:

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="input-with-prefix-suffix" label="TextField With Prefix and Suffix" suffix="接尾辞" prefix="接頭辞" />;
```

### With specific input width:

Note: This width only impact the actual input within the field. It can only be set to predefined values that are using our spacing scale.

```js
import TextField from '@axa-japan/design-system-react/TextField';

<TextField name="width-input-width" label="With Input Width" inputWidth="20" />;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the underlying &lt;input&gt; component.

```js
import React, { useRef } from 'react';
import TextField from '@axa-japan/design-system-react/TextField';

const ref = useRef(null);
<TextField ref={ref} name="Ref" label="Ref" />;
```

import React, { forwardRef, useId } from 'react';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import TextFieldBase, { InputWidth } from '../TextFieldBase/TextFieldBase';
import checkHasError from '../utils/checkHasError';
import SuffixPrefixField from '../SuffixPrefixField/SuffixPrefixField';

export type TextFieldProps = {
  /** Width of the actual input, using predefined values chosen from our spacing/sizing scale (20) */
  inputWidth?: InputWidth;
  /** Add a prefix before the input */
  prefix?: string;
  /** Add a suffix after the input */
  suffix?: string;
} & CommonFieldBaseProps &
  React.ComponentPropsWithoutRef<'input'>;

const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      id,
      name,
      label,
      type = 'text',
      showError,
      errorMessage,
      required = false,
      disabled = false,
      suffix,
      prefix,
      sx,
      showRequiredIndicator,
      ...rest
    },
    ref,
  ) => {
    const fallbackId = `text-field-id-${useId()}`;
    const hasError = checkHasError(showError, errorMessage);
    return (
      <FieldBase
        id={id || name || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        {prefix && <SuffixPrefixField content={prefix} />}
        <TextFieldBase
          ref={ref}
          id={id || name || fallbackId}
          type={type}
          disabled={disabled}
          hasError={hasError}
          name={name}
          required={required}
          {...rest}
        />
        {suffix && <SuffixPrefixField content={suffix} />}
      </FieldBase>
    );
  },
);

TextField.displayName = 'TextField';

export default TextField;

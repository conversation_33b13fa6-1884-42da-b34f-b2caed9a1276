import React from 'react';
import { MemoryRouter, Link as ReactRouterLink } from 'react-router-dom';
import { render } from '../../utils/testUtils';
import Button from '../Button';
import { AddIcon } from '../../Icons';

describe('Button', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Button>ボタン</Button>);
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  (['filled', 'outlined', 'text'] as const).forEach((variant) => {
    (['blue', 'white'] as const).forEach((color) => {
      test(`renders with variant ${variant} and color ${color} without crashing`, () => {
        const { getByText, container } = render(
          <Button variant={variant} color={color}>
            ボタン
          </Button>,
        );
        const testElement = getByText(/ボタン/i);
        expect(testElement).toBeInTheDocument();
        expect(container).toMatchSnapshot();
      });
    });
  });

  test('renders without crashing with disabled true', () => {
    const { getByText, container } = render(<Button disabled={true}>ボタン</Button>);
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with fullWidth prop true', () => {
    const { getByText, container } = render(<Button fullWidth={true}>ボタン</Button>);
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  (['right', 'left'] as const).forEach((iconPosition) => {
    test(`renders without crashing with an icon on the ${iconPosition}`, () => {
      const { getByText, container } = render(
        <Button icon={<AddIcon />} iconPosition={iconPosition}>
          ボタン
        </Button>,
      );
      const testElement = getByText(/ボタン/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test('renders without crashing with loading prop', () => {
    const { getByText, container } = render(<Button loading>ボタン</Button>);
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with loading prop and icon', () => {
    const { getByText, container } = render(
      <Button loading icon={<AddIcon />}>
        ボタン
      </Button>,
    );
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with loading prop and loadingText', () => {
    const { getByText, container } = render(
      <Button loading loadingText="loading">
        ボタン
      </Button>,
    );
    const testElement = getByText(/loading/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with asChild prop', () => {
    const { getByText, container } = render(
      <MemoryRouter>
        <Button asChild>
          <ReactRouterLink to="#">ボタン</ReactRouterLink>
        </Button>
      </MemoryRouter>,
    );
    const testElement = getByText(/ボタン/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

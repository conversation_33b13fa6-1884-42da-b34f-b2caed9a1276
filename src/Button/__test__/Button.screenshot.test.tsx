import React from 'react';
import Button from '../Button';
import Stack from '../../Stack';
import { takeScreenshot } from '../../utils/screenshotTestUtils';
import { AddIcon } from '../../Icons';

describe('Button', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Button>ボタン</Button>, task);
  });
  test('FilledBlue', async ({ task }) => {
    await takeScreenshot(
      <Button variant="filled" color="blue">
        ボタン
      </Button>,
      task,
    );
  });
  test('FilledWhite', async ({ task }) => {
    await takeScreenshot(
      <Stack sx={{ 'background-color': 'utilityBackgroundOcean', padding: 3 }}>
        <Button variant="filled" color="white">
          ボタン
        </Button>
      </Stack>,
      task,
    );
  });
  test('OutlinedBlue', async ({ task }) => {
    await takeScreenshot(
      <Stack sx={{ 'background-color': 'statusInformationLight', padding: 3 }}>
        <Button variant="outlined" color="blue">
          ボタン
        </Button>
      </Stack>,
      task,
    );
  });
  test('OutlinedWhite', async ({ task }) => {
    await takeScreenshot(
      <Stack sx={{ 'background-color': 'utilityBackgroundOcean', padding: 3 }}>
        <Button variant="outlined" color="white">
          ボタン
        </Button>
      </Stack>,
      task,
    );
  });
  test('TextBlue', async ({ task }) => {
    await takeScreenshot(
      <Button variant="text" color="blue">
        ボタン
      </Button>,
      task,
    );
  });
  test('TextWhite', async ({ task }) => {
    await takeScreenshot(
      <Stack sx={{ 'background-color': 'utilityBackgroundOcean', padding: 3 }}>
        <Button variant="text" color="white">
          ボタン
        </Button>
      </Stack>,
      task,
    );
  });
  test('Loading', async ({ task }) => {
    await takeScreenshot(<Button loading>ボタン</Button>, task);
  });
  test('LoadingText', async ({ task }) => {
    await takeScreenshot(
      <Button loading loadingText="Loading...">
        ボタン
      </Button>,
      task,
    );
  });
  test('LoadingIcon', async ({ task }) => {
    await takeScreenshot(
      <Button loading icon={<AddIcon />}>
        ボタン
      </Button>,
      task,
    );
  });
  test('Disabled', async ({ task }) => {
    await takeScreenshot(<Button disabled>ボタン</Button>, task);
  });
  test('WithRightIcon', async ({ task }) => {
    await takeScreenshot(
      <Button icon={<AddIcon />} iconPosition="right">
        ボタン
      </Button>,
      task,
    );
  });
  test('WithLeftIcon', async ({ task }) => {
    await takeScreenshot(
      <Button icon={<AddIcon />} iconPosition="left">
        ボタン
      </Button>,
      task,
    );
  });
});

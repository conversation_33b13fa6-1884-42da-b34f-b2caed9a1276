import styled from 'styled-components';
import { getTypographyVar } from '../typography';
import sx, { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'isTextVariant'> = {
  isTextVariant: {
    false: {
      'text-decoration': 'none',
    },
    true: {
      'text-decoration': 'underline',
    },
  },
};

const { useSx, getSxStyleRules } = sx('ButtonText', [], variants);

export { useSx };

export const ButtonText = styled.span`
  font-size: ${getTypographyVar('defaultFontSize')};
  font-weight: ${getTypographyVar('boldFontWeight')};
  line-height: ${getTypographyVar('smLineHeight')};
  text-align: center;
  margin: 0 ${getSpacingVar(1)};
  ${getSxStyleRules()};

  &[data-text-variant='true'] {
    text-underline-offset: ${getSpacingVar(1)};
    text-decoration-thickness: ${getSpacingVar('px')};
  }
`;

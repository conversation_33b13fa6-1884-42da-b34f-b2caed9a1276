import React, { forwardRef } from 'react';
import { Slottable } from '@radix-ui/react-slot';
import { ButtonBase, ButtonSlotBase, useSx } from './ButtonBase';
import { ButtonText, useSx as useButtonTextSx } from './ButtonText';
import { ButtonIcon, ButtonLoadingIcon } from '../ButtonIcon/ButtonIcon';
import type { MarginSxPropType } from '../sx';
import Loader from '../Loader';

// Base props that are always available
type BaseButtonProps = {
  children: string | React.ReactElement;
  /** Changes the variant of the button */
  variant?: 'filled' | 'outlined' | 'text';
  color?: 'blue' | 'white';
  /** Component to use for the icon
   * In the future, could have a simple "icon" prop to
   * choose from a pre-existing list of icons.
   */
  icon?: React.ReactElement;
  iconPosition?: 'left' | 'right';
  /** Sets the button to be full width */
  fullWidth?: boolean;
  /** Style overrides */
  sx?: MarginSxPropType;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color' | 'disabled'>;

// When asChild is true, loading, loadingText, and disabled are not allowed
type AsChildButtonProps = BaseButtonProps & {
  /** Make the button act as the provided child, useful for navigation purpose */
  asChild: true;
  disabled?: never;
  loading?: never;
  loadingText?: never;
};

// When asChild is false or undefined, loading, loadingText, and disabled are allowed
type RegularButtonProps = BaseButtonProps & {
  asChild?: false;
  /** Disables the button */
  disabled?: boolean;
  /** Sets the loading state
   * Will show the disabled state
   * Will show a loader instead of the normal icon if icon is set
   * Will show the loadingText text if set
   */
  loading?: boolean;
  /** Sets the text that should appear when loading */
  loadingText?: string;
};

// Union type for conditional props
export type ButtonProps = AsChildButtonProps | RegularButtonProps;

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'filled',
      color = 'blue',
      icon,
      iconPosition = 'right',
      loading = false,
      loadingText,
      fullWidth = false,
      disabled = false,
      asChild = false,
      children,
      sx,
      type = 'button',
      ...rest
    },
    ref,
  ) => {
    const isTextVariant = variant === 'text';
    const buttonBaseSx = useSx(sx, {
      compoundVariant: `${variant}-${color}`,
      iconPosition,
      fullWidth: String(fullWidth),
      isText: String(isTextVariant),
    });
    const buttonTextSx = useButtonTextSx(sx, { isTextVariant: String(isTextVariant) });

    // Only show the loading icon if there is already an icon
    const loadingIcon = !!icon && loading && (
      <ButtonLoadingIcon>
        <Loader size="sm" />
      </ButtonLoadingIcon>
    );

    const buttonIcon = !!icon && <ButtonIcon aria-hidden="true">{icon}</ButtonIcon>;

    const isLoading = loading && !!loadingText;
    const isDisabled = disabled || loading;

    if (asChild) {
      // Don't try to render anything if children are invalid
      if (typeof children === 'string' || typeof children.props.children !== 'string') {
        return null;
      }

      // Get the text from the children of the child passed
      const childrenText = children.props.children as string;

      // Create cleansed children with disabled link handling
      const cleansedChildren = React.cloneElement(children, { children: undefined });

      return (
        <ButtonSlotBase {...rest} aria-label={childrenText} style={buttonBaseSx}>
          <ButtonText style={buttonTextSx} data-text-variant={isTextVariant}>
            {childrenText}
          </ButtonText>
          <Slottable>{cleansedChildren}</Slottable>
          {loadingIcon || buttonIcon}
        </ButtonSlotBase>
      );
    }

    // Don't try to render anything if children is not a string, as it would break accessibility
    if (typeof children !== 'string') {
      return null;
    }

    // Logic to show loading text or normal text
    const text = isLoading ? loadingText : children;

    return (
      <ButtonBase {...rest} ref={ref} disabled={isDisabled} aria-label={children} style={buttonBaseSx} type={type}>
        <ButtonText style={buttonTextSx} data-text-variant={isTextVariant}>
          {text}
        </ButtonText>
        {loadingIcon || buttonIcon}
      </ButtonBase>
    );
  },
);

Button.displayName = 'Button';

export default Button;

In addition to props above, _<PERSON><PERSON>_ accepts any default props for _button_ HTML tag.

### Default Button:

Note: You cannot pass something other than a string as children (except if you use asChild prop, see below)

```js
import Button from '@axa-japan/design-system-react/Button';

<Button>ボタン</Button>;
```

### Variants:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';

<Stack spacing={2}>
  <Button variant="filled">Filled</Button>
  <Button variant="outlined">Outlined</Button>
  <Button variant="text">Text</Button>
</Stack>;
```

### White button:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';

<Stack spacing={2} sx={{ padding: 2, 'background-color': 'utilityBackgroundOcean' }}>
  <Button variant="filled" color="white">
    White
  </Button>
  <Button variant="outlined" color="white">
    Outlined White
  </Button>
  <Button variant="text" color="white">
    Text White
  </Button>
</Stack>;
```

### With icon:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';
import { AddIcon } from '@axa-japan/design-system-react/Icons';

<Stack spacing={2} direction="column">
  <Stack spacing={2}>
    <Button icon={<AddIcon />}>ボタン</Button>
    <Button icon={<AddIcon />} iconPosition="left">
      ボタン
    </Button>
  </Stack>
  <Stack spacing={2}>
    <Button variant="text" icon={<AddIcon />}>
      ボタン
    </Button>
    <Button variant="text" icon={<AddIcon />} iconPosition="left">
      ボタン
    </Button>
  </Stack>
</Stack>;
```

### Disabled:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';

<Stack spacing={2}>
  <Button disabled>Disabled Button</Button>
  <Button variant="text" disabled>
    Disabled Text Button
  </Button>
  <Button disabled loading>
    Disabled Button
  </Button>
</Stack>;
```

### Loading:

Note: Loading has a different behavior depending on the current setup of your button.

If your button already has an icon, it will replace it with the loader icon:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';
import { AddIcon } from '@axa-japan/design-system-react/Icons';

<Stack spacing={2}>
  <Button loading icon={<AddIcon />}>
    Button With Icon
  </Button>
  <Button variant="text" loading icon={<AddIcon />}>
    Text Button With Icon
  </Button>
</Stack>;
```

If not, it will just put the normal disabled state. But it is recommended to put a loadingText:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';

<Stack spacing={2}>
  <Button loading loadingText="Loading...">
    Button With Icon
  </Button>
  <Button variant="text" loading loadingText="Loading...">
    Text Button With Icon
  </Button>
  <Button loading>No loading text</Button>
</Stack>;
```

### With fullWidth:

Note: This option is not part of the official AXA web guidelines

```js
import Button from '@axa-japan/design-system-react/Button';

<Button fullWidth={true}>ボタン</Button>;
```

### With navigation (asChild)

You can pass a link to the Button after setting the asChild prop to true.
The link would have the same styles as the Button component.

Important:

- Please check the code below to see wrong usage of this feature to understand how to not use it.
- The _loading_, _loadingText_, and _disabled_ props are not available when using asChild, as links should not be in a loading or disabled state.

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Button from '@axa-japan/design-system-react/Button';
import { Link } from 'react-router-dom';

<Stack spacing={2}>
  <Button variant="filled" asChild>
    <a href="https://www.axa.co.jp/">Anchor link</a>
  </Button>
  <Button variant="filled" asChild>
    <Link to="/example-page">React Router Link</Link>
  </Button>
  {/* Invalid example that will not display anything! */}
  <Button variant="filled" asChild>
    Child component is missing!
  </Button>
  <Button variant="filled" asChild>
    <Link to="/example-page">
      <div>Extra html tag!</div>
    </Link>
  </Button>
</Stack>;
```

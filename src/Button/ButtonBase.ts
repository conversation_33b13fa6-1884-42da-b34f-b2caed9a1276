import styled, { css } from 'styled-components';
import { Slot } from '@radix-ui/react-slot';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';
import button from '../styles/button';

// Common style objects to reduce duplication
const disabledStyles = {
  'background-color:disabled': getColorVar('interactiveDisabledLight'),
  'background-color:[aria-disabled="true"]': getColorVar('interactiveDisabledLight'),
  'border:disabled': `2px solid ${getColorVar('interactiveDisabledLight')}`,
  'border:[aria-disabled="true"]': `2px solid ${getColorVar('interactiveDisabledLight')}`,
};

const variants: VariantsType<'compoundVariant' | 'fullWidth' | 'iconPosition' | 'isText'> = {
  compoundVariant: {
    'filled-blue': {
      color: getColorVar('interactiveActiveWhite'),
      'color:hover': getColorVar('interactiveActiveWhite'),
      'background-color': getColorVar('interactiveActivePrimary'),
      'background-color:hover': getColorVar('interactiveHoverPrimary'),
      // add a border to keep consistency of height with outlined button
      border: `2px solid ${getColorVar('interactiveActivePrimary')}`,
      'border:hover': `2px solid ${getColorVar('interactiveHoverPrimary')}`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
      ...disabledStyles,
    },
    'filled-white': {
      color: getColorVar('interactiveActivePrimary'),
      'color:hover': getColorVar('interactiveHoverPrimary'),
      'background-color': getColorVar('interactiveActiveWhite'),
      'background-color:hover': getColorVar('interactiveHoverGrey'),
      // add a border for the filled to keep consistency of height with outlined button
      border: `2px solid ${getColorVar('interactiveActiveWhite')}`,
      'border:hover': `2px solid ${getColorVar('interactiveHoverGrey')}`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusWhite')}`,
      ...disabledStyles,
    },
    'outlined-blue': {
      color: getColorVar('interactiveActivePrimary'),
      'color:hover': getColorVar('interactiveHoverPrimary'),
      'background-color': 'transparent',
      'background-color:hover': getColorVar('interactiveHoverPrimaryTransparent'),
      border: `2px solid ${getColorVar('interactiveActivePrimary')}`,
      'border:hover': `2px solid ${getColorVar('interactiveHoverPrimary')}`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
      ...disabledStyles,
    },
    'outlined-white': {
      color: getColorVar('interactiveActiveWhite'),
      'color:hover': getColorVar('interactiveHoverGrey'),
      'background-color': 'transparent',
      'background-color:hover': getColorVar('interactiveHoverWhiteTransparent'),
      border: `2px solid ${getColorVar('interactiveActiveWhite')}`,
      'border:hover': `2px solid ${getColorVar('interactiveHoverGrey')}`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusWhite')}`,
      ...disabledStyles,
    },
    'text-blue': {
      color: getColorVar('interactiveActivePrimary'),
      'color:hover': getColorVar('interactiveHoverPrimary'),
      'background-color': 'transparent',
      'background-color:hover': getColorVar('interactiveHoverPrimaryTransparent'),
      'background-color:disabled': 'transparent',
      'background-color:[aria-disabled="true"]': 'transparent',
      border: `none`,
      'border:hover': `none`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
    },
    'text-white': {
      color: getColorVar('interactiveActiveWhite'),
      'color:hover': getColorVar('grey200'),
      'background-color': 'transparent',
      'background-color:hover': getColorVar('interactiveHoverWhiteTransparent'),
      'background-color:disabled': 'transparent',
      'background-color:[aria-disabled="true"]': 'transparent',
      border: `none`,
      'border:hover': `none`,
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusWhite')}`,
    },
  },
  iconPosition: {
    right: {
      'flex-direction': 'row',
    },
    left: {
      'flex-direction': 'row-reverse',
    },
  },
  fullWidth: {
    false: {
      width: 'auto',
    },
    true: {
      width: '100%',
    },
  },
  isText: {
    false: {
      padding: `${getSpacingVar(3)} ${getSpacingVar(5)}`,
      'outline-offset': getSpacingVar(0.5),
    },
    true: {
      padding: `${getSpacingVar(3)} ${getSpacingVar(1)}`,
      'outline-offset': getSpacingVar(0),
    },
  },
};

const { useSx, getSxStyleRules } = sx('Button', ['margin'], variants);

export { useSx };

export const ButtonBaseStyles = css`
  ${button.base}

  ${getSxStyleRules()}
`;

export const ButtonBase = styled.button`
  ${ButtonBaseStyles}

  &:disabled {
    ${button.disabled}
  }
`;

export const ButtonSlotBase = styled(Slot)`
  /* All following styles are here to make sure that anchor global styles are not applied on button */
  text-decoration: none;

  &:hover,
  &:focus {
    text-decoration: none;
  }

  /* Necessary as we don't want any global styles applied to visited state */
  &:visited {
    ${getSxStyleRules()}
  }

  ${ButtonBaseStyles}

  &[aria-disabled='true'] {
    ${button.disabled}
  }
`;

import { cssColorVarMap } from './colors';

export type TypographyTheme = {
  fontColor: string;
  fontFamily: string;
  defaultFontSize: string;
  '2xlFontSize': string;
  '2xlFontSizeMobile': string;
  xlFontSize: string;
  lgFontSize: string;
  smFontSize: string;
  smLineHeight: string;
  lineHeight: string;
  defaultFontWeight: string;
  boldFontWeight: string;
};

const typography: TypographyTheme = {
  fontColor: cssColorVarMap.characterPrimary.value,
  fontFamily: "'Noto Sans JP', sans-serif",
  defaultFontSize: '1rem',
  '2xlFontSize': '2rem',
  '2xlFontSizeMobile': '1.75rem',
  xlFontSize: '1.5rem',
  lgFontSize: '1.25rem',
  smFontSize: '0.875rem',
  smLineHeight: '1.5',
  lineHeight: '1.6',
  defaultFontWeight: '400',
  boldFontWeight: '700',
};

// ajds for AXA Japan design system
export const cssTypographyVarMap = {
  fontColor: { name: '--ajds-font-color', value: typography.fontColor },
  fontFamily: { name: '--ajds-font-family', value: typography.fontFamily },
  defaultFontSize: { name: '--ajds-font-size-default', value: typography.defaultFontSize },
  '2xlFontSize': { name: '--ajds-font-size-2xl', value: typography['2xlFontSize'] },
  '2xlFontSizeMobile': { name: '--ajds-font-size-2xl-mobile', value: typography['2xlFontSizeMobile'] },
  xlFontSize: { name: '--ajds-font-size-xl', value: typography.xlFontSize },
  lgFontSize: { name: '--ajds-font-size-lg', value: typography.lgFontSize },
  smFontSize: { name: '--ajds-font-size-sm', value: typography.smFontSize },
  smLineHeight: { name: '--ajds-line-height-sm', value: typography.smLineHeight },
  lineHeight: { name: '--ajds-line-height', value: typography.lineHeight },
  defaultFontWeight: { name: '--ajds-font-weight-default', value: typography.defaultFontWeight },
  boldFontWeight: { name: '--ajds-font-weight-bold', value: typography.boldFontWeight },
} as const;

export function getTypographyVar(key: keyof typeof cssTypographyVarMap): string {
  return `var(${(cssTypographyVarMap[key] || {}).name})`;
}

export default typography;

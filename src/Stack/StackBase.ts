import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'direction' | 'spacing'> = {
  direction: {
    row: {
      'flex-direction': 'row',
    },
    column: {
      'flex-direction': 'column',
    },
  },
  spacing: {
    0: {
      gap: getSpacingVar(0),
    },
    1: {
      gap: getSpacingVar(1),
    },
    2: {
      gap: getSpacingVar(2),
    },
    3: {
      gap: getSpacingVar(3),
    },
    4: {
      gap: getSpacingVar(4),
    },
    5: {
      gap: getSpacingVar(5),
    },
  },
};

const { useSx, getSxStyleRules } = sx('StackBase', ['margin', 'padding', 'background-color'], variants);

export { useSx };

export const StackBase = styled.div`
  display: flex;
  ${getSxStyleRules()}
`;

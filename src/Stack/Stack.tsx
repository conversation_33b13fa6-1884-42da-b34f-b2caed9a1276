import React from 'react';
import { useSx, StackBase } from './StackBase';
import { MarginSxPropType } from '../sx';
import { PaddingSxPropType } from '../sx/attributes/padding';
import { BackgroundColorSxPropType } from '../sx/attributes/backgroundColor';

export type StackProps = {
  /** The direction to stack the items */
  direction?: 'row' | 'column';
  /** Spacing to use between items */
  spacing?: 0 | 1 | 2 | 3 | 4 | 5;
  /** Style overrides */
  sx?: MarginSxPropType & PaddingSxPropType & BackgroundColorSxPropType;
} & React.ComponentPropsWithoutRef<'div'>;

const Stack: React.FC<StackProps> = ({ children, direction = 'row', spacing = 0, sx, ...rest }) => {
  return (
    <StackBase {...rest} style={useSx(sx, { direction, spacing: String(spacing) })}>
      {children}
    </StackBase>
  );
};

export default Stack;

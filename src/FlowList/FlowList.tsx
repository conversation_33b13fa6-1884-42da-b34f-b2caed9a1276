import React, { ReactNode } from 'react';
import { FlowListContainer, FlowListContentContainer, FlowListStepIcon, FlowListStepItem, FlowListStepTitle } from './FlowList.styles';

export type FlowListTitleOptions = 'p' | 'h2' | 'h3' | 'h4';

export type FlowListStep = {
  title: string;
  content?: ReactNode;
};

export type FlowListProps = {
  steps: FlowListStep[];
  titleAs?: FlowListTitleOptions;
};

const FlowList: React.FC<FlowListProps> = ({ steps, titleAs = 'p' }) => (
  <FlowListContainer>
    {steps.map((step, stepIndex) => {
      const { title, content } = step;
      return (
        <FlowListStepItem key={stepIndex}>
          <FlowListStepIcon />
          <FlowListContentContainer>
            <FlowListStepTitle as={titleAs}>{title}</FlowListStepTitle>
            {content && (typeof content === 'string' ? <p>{content}</p> : content)}
          </FlowListContentContainer>
        </FlowListStepItem>
      );
    })}
  </FlowListContainer>
);

export default FlowList;

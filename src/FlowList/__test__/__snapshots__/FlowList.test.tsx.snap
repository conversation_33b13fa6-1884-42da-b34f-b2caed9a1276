// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FlowList > matches snapshot 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c3::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c3::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c1 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c1:last-child > .c2::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c1:last-child > .c4 {
  margin-bottom: var(--ajds-spacing-0);
}

.c6 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

@media (max-width:599px) {
  .c3::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c3::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c5 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c6 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

<div>
  <ul
    class="c0"
  >
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <p
          class="c6"
        >
          テストテストテストテスト
        </p>
        <p>
          テストテスト
        </p>
      </div>
    </li>
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <p
          class="c6"
        >
          テスト２テスト２テスト
２テスト２テスト２テスト２
        </p>
        <p>
          テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト
        </p>
      </div>
    </li>
  </ul>
</div>
`;

exports[`FlowList > matches snapshot for different title tags - titleAs h2  1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c3::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c3::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c1 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c1:last-child > .c2::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c1:last-child > .c4 {
  margin-bottom: var(--ajds-spacing-0);
}

.c6 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

@media (max-width:599px) {
  .c3::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c3::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c5 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c6 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

<div>
  <ul
    class="c0"
  >
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h2
          class="c6"
        >
          テストテストテストテスト
        </h2>
        <p>
          テストテスト
        </p>
      </div>
    </li>
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h2
          class="c6"
        >
          テスト２テスト２テスト
２テスト２テスト２テスト２
        </h2>
        <p>
          テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト
        </p>
      </div>
    </li>
  </ul>
</div>
`;

exports[`FlowList > matches snapshot for different title tags - titleAs h3  1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c3::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c3::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c1 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c1:last-child > .c2::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c1:last-child > .c4 {
  margin-bottom: var(--ajds-spacing-0);
}

.c6 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

@media (max-width:599px) {
  .c3::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c3::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c5 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c6 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

<div>
  <ul
    class="c0"
  >
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h3
          class="c6"
        >
          テストテストテストテスト
        </h3>
        <p>
          テストテスト
        </p>
      </div>
    </li>
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h3
          class="c6"
        >
          テスト２テスト２テスト
２テスト２テスト２テスト２
        </h3>
        <p>
          テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト
        </p>
      </div>
    </li>
  </ul>
</div>
`;

exports[`FlowList > matches snapshot for different title tags - titleAs h4  1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c3::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c3::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c1 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c1:last-child > .c2::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c1:last-child > .c4 {
  margin-bottom: var(--ajds-spacing-0);
}

.c6 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

@media (max-width:599px) {
  .c3::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c3::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c5 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c6 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

<div>
  <ul
    class="c0"
  >
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h4
          class="c6"
        >
          テストテストテストテスト
        </h4>
        <p>
          テストテスト
        </p>
      </div>
    </li>
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <h4
          class="c6"
        >
          テスト２テスト２テスト
２テスト２テスト２テスト２
        </h4>
        <p>
          テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト
        </p>
      </div>
    </li>
  </ul>
</div>
`;

exports[`FlowList > matches snapshot for different title tags - titleAs p  1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c3::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c3::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c1 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c1:last-child > .c2::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c1:last-child > .c4 {
  margin-bottom: var(--ajds-spacing-0);
}

.c6 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

@media (max-width:599px) {
  .c3::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c3::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c5 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c6 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

<div>
  <ul
    class="c0"
  >
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <p
          class="c6"
        >
          テストテストテストテスト
        </p>
        <p>
          テストテスト
        </p>
      </div>
    </li>
    <li
      class="c1"
    >
      <div
        class="c2 c3"
      />
      <div
        class="c4 c5"
      >
        <p
          class="c6"
        >
          テスト２テスト２テスト
２テスト２テスト２テスト２
        </p>
        <p>
          テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト
        </p>
      </div>
    </li>
  </ul>
</div>
`;

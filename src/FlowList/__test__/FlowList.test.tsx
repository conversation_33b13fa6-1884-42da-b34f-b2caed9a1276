import React from 'react';
import { RenderResult } from '@testing-library/react';
import FlowList, { FlowListStep, FlowListTitleOptions } from '../FlowList';
import { render } from '../../utils/testUtils';

const defaultSteps: FlowListStep[] = [
  {
    title: 'テストテストテストテスト',
    content: 'テストテスト',
  },
  {
    title: `テスト２テスト２テスト
２テスト２テスト２テスト２`,
    content: `テストテスト
テストテストテストテストテストテス
    
トテストテストテストテストテストテスト`,
  },
];

const titleOptions: FlowListTitleOptions[] = ['p', 'h2', 'h3', 'h4'];

const renderComponent = (steps: FlowListStep[] = defaultSteps, titleAs: FlowListTitleOptions = 'p'): RenderResult => {
  return render(<FlowList steps={steps} titleAs={titleAs} />);
};

const checkSnapshot = ({ container }: RenderResult) => {
  expect(container).toMatchSnapshot();
};

describe('FlowList', () => {
  it('matches snapshot', () => {
    const component = renderComponent();
    checkSnapshot(component);
  });

  it.each(titleOptions)('matches snapshot for different title tags - titleAs %s ', (titleOption) => {
    const component = renderComponent(defaultSteps, titleOption);
    checkSnapshot(component);
  });
});

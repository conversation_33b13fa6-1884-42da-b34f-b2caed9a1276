import React from 'react';
import FlowList, { FlowListStep } from '../FlowList';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

const defaultSteps: FlowListStep[] = [
  {
    title: 'テストテストテストテスト',
    content: 'テストテスト',
  },
  {
    title: `テスト２テスト２テスト`,
    content: `テストテスト\r\nテストテストテストテストテストテス`,
  },
];

describe('FlowList', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<FlowList steps={defaultSteps} />, task);
  });

  test('WithLongText', async ({ task }) => {
    const steps: FlowListStep[] = [
      ...defaultSteps,
      {
        title: `以下のボタンからお客さま専用ページにログインします。
asdfasdfsfd`,
        content: `説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文

説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文

説明文説明文説明文`,
      },
      {
        title: `お客さま専用ページにログインし、「設定はこちら」ボタンを押します。`,
        content: `説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
説明文説明文説明文説明文説明文説明文


説明文説明文説明文`,
      },
    ];
    await takeScreenshot(<FlowList steps={steps} />, task, {
      viewport: { height: 1000 },
    });
  });
});

**_titleAs_** : _p | h2 | h4 | h3_ `optional` -> html tag that will be used for the title, default value is `p` tag.

**_steps_**

This prop is a list of FlowList's steps.

Each element will be an object with the structure below:

- **_title_** : _string_ `required` -> Title of each step.

- **_content_** : _ReactNode_ `optional` -> Description of each step if a string is passed it will be wrapped in a `p` tag, if another component is passed it will be rendered.

### FlowList example:

```js
import FlowList from '@axa-japan/design-system-react/FlowList';

<FlowList
  titleAs="h3"
  steps={[
    {
      title: 'テストテストテストテスト',
      content:
        'テストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテストテスト',
    },
    {
      title: `テスト２テスト２テスト
２テスト２テスト２テスト２`,
      content: `テストテスト
テストテストテストテストテストテス
      
トテストテストテストテストテストテスト`,
    },
    {
      title: 'テスト３',
      content: 'テストテスト\r\nテストテストテストテスト',
    },
  ]}
/>;
```

### FlowList example (content is different of a text):

```js
import FlowList from '@axa-japan/design-system-react/FlowList';

<FlowList
  titleAs="h3"
  steps={[
    {
      title: 'テストテストテストテスト',
    },
    {
      title: 'テスト２',
      content: (
        <div>
          <h3>I am a h3 tag</h3>
          <img src="https://placehold.jp/600x300.png" />
        </div>
      ),
    },
    {
      title: 'テスト３',
      content: 'テストテスト\r\nテストテストテストテスト',
    },
  ]}
/>;
```

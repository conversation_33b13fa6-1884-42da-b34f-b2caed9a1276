import styled from 'styled-components';
import media from '../Breakpoints/Breakpoints';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

export const FlowListContainer = styled.ul`
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  margin: ${getSpacingVar(0)};
`;

export const FlowListStepIcon = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: ${getSpacingVar(1)};
  align-items: center;

  &::before {
    color: ${getColorVar('interactiveActiveWhite')};
    display: flex;
    justify-content: center;
    align-items: center;
    content: counter(stepCounter);
    font-size: ${getTypographyVar('lgFontSize')};
    font-weight: ${getTypographyVar('boldFontWeight')};
    background-color: ${getColorVar('interactiveActivePrimary')};
    border-radius: ${getRadiusVar('full')};
    width: ${getSpacingVar(10)};
    height: ${getSpacingVar(10)};
    ${media.extraSmallOnly} {
      width: ${getSpacingVar(8)};
      height: ${getSpacingVar(8)};
      font-size: ${getTypographyVar('defaultFontSize')};
    }
  }

  &::after {
    content: '';
    border-left: ${getSpacingVar(0.5)} solid ${getColorVar('interactiveActivePrimary')};
    flex-grow: 1;
    margin-bottom: ${getSpacingVar(1)};
    ${media.extraSmallOnly} {
      min-height: 70px;
    }
  }
`;

export const FlowListContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: ${getSpacingVar(2)};
  margin-bottom: ${getSpacingVar(14)};
  ${media.extraSmallOnly} {
    margin-bottom: ${getSpacingVar(12)};
  }
`;

export const FlowListStepItem = styled.li`
  counter-increment: stepCounter 1;
  list-style: none;
  display: flex;
  column-gap: ${getSpacingVar(3.5)};
  white-space: break-spaces;
  ${media.extraSmallOnly} {
    column-gap: ${getSpacingVar(3)};
  }

  &:last-child {
    & > ${FlowListStepIcon} {
      ::after {
        height: 0;
        min-height: 0;
        margin: 0;
        border: none;
        flex-grow: 0;
      }
    }
    & > ${FlowListContentContainer} {
      margin-bottom: ${getSpacingVar(0)};
    }
  }
`;

export const FlowListStepTitle = styled.p`
  font-size: ${getTypographyVar('lgFontSize')};
  font-weight: ${getTypographyVar('boldFontWeight')};
  margin-top: ${getSpacingVar(1)};
  line-height: 1.625;

  ${media.extraSmallOnly} {
    margin-top: ${getSpacingVar(0.5)};
    font-size: ${getTypographyVar('defaultFontSize')};
  }
`;

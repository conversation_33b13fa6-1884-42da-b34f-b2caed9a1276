import React, { forwardRef, useCallback } from 'react';
import { TextAreaBase } from './TextAreaBase';

export type TextAreaProps = {
  /** Flag for showing error state */
  hasError?: boolean;
} & Omit<React.ComponentPropsWithRef<'textarea'>, 'rows'>;

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({ disabled = false, hasError = false, ...rest }, ref) => {
  // we want to scroll to the bottom of textarea if enter key is pressed & end of content
  const autoScrollTextArea = useCallback(
    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter') {
        // Allow the event to register in the DOM
        setTimeout(() => {
          if (ref && 'current' in ref && ref.current) {
            const textArea = ref.current;
            // Get the cursor position and the length of the textarea content
            const cursorPosition = textArea.selectionStart;
            const contentLength = textArea.value.length;
            // Check if the cursor is at the end of the content
            if (cursorPosition === contentLength) {
              // scroll to the bottom
              textArea.scrollTop = textArea.scrollHeight;
            }
          }
        }, 0);
      }
    },
    [ref],
  );

  return <TextAreaBase ref={ref} onKeyDown={autoScrollTextArea} rows={6} disabled={disabled} aria-invalid={hasError} {...rest} />;
});

TextArea.displayName = 'TextArea';

export default React.memo(TextArea);

import React from 'react';
import { render } from '../../utils/testUtils';
import TextArea from '../TextArea';

describe('TextArea', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<TextArea />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((disabled) => {
    test(`renders prop disabled ${disabled}, without crashing`, () => {
      const { getByRole, container } = render(<TextArea disabled={disabled} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((hasError) => {
    test(`renders prop hasError ${hasError}, without crashing`, () => {
      const { getByRole, container } = render(<TextArea hasError={hasError} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders prop sx without crashing`, () => {
    const { getByRole, container } = render(<TextArea />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TextArea > renders prop disabled false, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="false"
    class="c0 c1"
    rows="6"
  />
</div>
`;

exports[`TextArea > renders prop disabled true, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="false"
    class="c0 c1"
    disabled=""
    rows="6"
  />
</div>
`;

exports[`TextArea > renders prop hasError false, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="false"
    class="c0 c1"
    rows="6"
  />
</div>
`;

exports[`TextArea > renders prop hasError true, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="true"
    class="c0 c1"
    rows="6"
  />
</div>
`;

exports[`TextArea > renders prop sx without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="false"
    class="c0 c1"
    rows="6"
  />
</div>
`;

exports[`TextArea > renders without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

<div>
  <textarea
    aria-invalid="false"
    class="c0 c1"
    rows="6"
  />
</div>
`;

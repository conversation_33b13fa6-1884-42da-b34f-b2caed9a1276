// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Tabs > change tab 1`] = `
.c1 {
  all: unset;
  display: inline-block;
  cursor: pointer;
  padding: var(--ajds-spacing-4);
  color: var(--ajds-color-interactive-active-primary);
  position: relative;
}

.c1[data-selected='true']:not(:disabled) {
  font-weight: var(--ajds-font-weight-bold);
}

.c1[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
  color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover:disabled {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: no-drop;
}

.c1:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c0 {
  overflow: auto hidden;
  white-space: nowrap;
  -ms-overflow-style: none;
  -webkit-scrollbar-width: none;
  -moz-scrollbar-width: none;
  -ms-scrollbar-width: none;
  scrollbar-width: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c0::-webkit-scrollbar {
  display: none;
}

.c2 {
  padding: var(--ajds-spacing-4);
}

.c3[data-hidden='true'] {
  visibility: hidden;
  height: 0;
  overflow: hidden;
}

<div>
  <div
    class=""
  >
    <div
      class="c0"
      role="tablist"
    >
      <button
        aria-controls="1-panel"
        aria-selected="true"
        class="c1"
        data-selected="true"
        id="1"
        role="tab"
        tabindex="0"
      >
        タブアイテム1
      </button>
      <button
        aria-controls="2-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        id="2"
        role="tab"
        tabindex="0"
      >
        タブアイテム2
      </button>
      <button
        aria-controls="3-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        disabled=""
        id="3"
        role="tab"
        tabindex="0"
      >
        タブアイテム3
      </button>
    </div>
    <div
      class="c2"
    >
      <div
        aria-hidden="false"
        aria-labelledby="1"
        class="c3"
        data-hidden="false"
        id="1-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 1
        </p>
      </div>
      <div
        aria-hidden="true"
        aria-labelledby="2"
        class="c3"
        data-hidden="true"
        id="2-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 2
        </p>
      </div>
      <div
        aria-hidden="true"
        aria-labelledby="3"
        class="c3"
        data-hidden="true"
        id="3-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          disable
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tabs > default tab 1`] = `
.c1 {
  all: unset;
  display: inline-block;
  cursor: pointer;
  padding: var(--ajds-spacing-4);
  color: var(--ajds-color-interactive-active-primary);
  position: relative;
}

.c1[data-selected='true']:not(:disabled) {
  font-weight: var(--ajds-font-weight-bold);
}

.c1[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
  color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover:disabled {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: no-drop;
}

.c1:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c0 {
  overflow: auto hidden;
  white-space: nowrap;
  -ms-overflow-style: none;
  -webkit-scrollbar-width: none;
  -moz-scrollbar-width: none;
  -ms-scrollbar-width: none;
  scrollbar-width: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c0::-webkit-scrollbar {
  display: none;
}

.c2 {
  padding: var(--ajds-spacing-4);
}

.c3[data-hidden='true'] {
  visibility: hidden;
  height: 0;
  overflow: hidden;
}

<div>
  <div
    class=""
  >
    <div
      class="c0"
      role="tablist"
    >
      <button
        aria-controls="1-panel"
        aria-selected="true"
        class="c1"
        data-selected="true"
        id="1"
        role="tab"
        tabindex="0"
      >
        タブアイテム1
      </button>
      <button
        aria-controls="2-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        id="2"
        role="tab"
        tabindex="0"
      >
        タブアイテム2
      </button>
      <button
        aria-controls="3-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        disabled=""
        id="3"
        role="tab"
        tabindex="0"
      >
        タブアイテム3
      </button>
    </div>
    <div
      class="c2"
    >
      <div
        aria-hidden="false"
        aria-labelledby="1"
        class="c3"
        data-hidden="false"
        id="1-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 1
        </p>
      </div>
      <div
        aria-hidden="true"
        aria-labelledby="2"
        class="c3"
        data-hidden="true"
        id="2-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 2
        </p>
      </div>
      <div
        aria-hidden="true"
        aria-labelledby="3"
        class="c3"
        data-hidden="true"
        id="3-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          disable
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Tabs > renders without crashing 1`] = `
.c1 {
  all: unset;
  display: inline-block;
  cursor: pointer;
  padding: var(--ajds-spacing-4);
  color: var(--ajds-color-interactive-active-primary);
  position: relative;
}

.c1[data-selected='true']:not(:disabled) {
  font-weight: var(--ajds-font-weight-bold);
}

.c1[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
  color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover[data-selected='true']:not(:disabled)::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c1:hover:disabled {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: no-drop;
}

.c1:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c0 {
  overflow: auto hidden;
  white-space: nowrap;
  -ms-overflow-style: none;
  -webkit-scrollbar-width: none;
  -moz-scrollbar-width: none;
  -ms-scrollbar-width: none;
  scrollbar-width: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c0::-webkit-scrollbar {
  display: none;
}

.c2 {
  padding: var(--ajds-spacing-4);
}

.c3[data-hidden='true'] {
  visibility: hidden;
  height: 0;
  overflow: hidden;
}

<div>
  <div
    class=""
  >
    <div
      class="c0"
      role="tablist"
    >
      <button
        aria-controls="1-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        id="1"
        role="tab"
        tabindex="0"
      >
        タブアイテム1
      </button>
      <button
        aria-controls="2-panel"
        aria-selected="true"
        class="c1"
        data-selected="true"
        id="2"
        role="tab"
        tabindex="0"
      >
        タブアイテム2
      </button>
      <button
        aria-controls="3-panel"
        aria-selected="false"
        class="c1"
        data-selected="false"
        disabled=""
        id="3"
        role="tab"
        tabindex="0"
      >
        タブアイテム3
      </button>
    </div>
    <div
      class="c2"
    >
      <div
        aria-hidden="true"
        aria-labelledby="1"
        class="c3"
        data-hidden="true"
        id="1-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 1
        </p>
      </div>
      <div
        aria-hidden="false"
        aria-labelledby="2"
        class="c3"
        data-hidden="false"
        id="2-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          Content 2
        </p>
      </div>
      <div
        aria-hidden="true"
        aria-labelledby="3"
        class="c3"
        data-hidden="true"
        id="3-panel"
        role="tabpanel"
        tabindex="0"
      >
        <p>
          disable
        </p>
      </div>
    </div>
  </div>
</div>
`;

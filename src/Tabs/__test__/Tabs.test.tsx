import React from 'react';
import { vi } from 'vitest';
import { waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../utils/testUtils';
import Tabs from '../Tabs';

const Content1 = () => {
  return <p>Content 1</p>;
};

const Content2 = () => {
  return <p>Content 2</p>;
};

describe('Tabs', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(
      <Tabs
        tabs={[
          { id: '1', title: 'タブアイテム1', content: <Content1 /> },
          { id: '2', title: 'タブアイテム2', content: <Content2 />, isDefault: true },
          { id: '3', title: 'タブアイテム3', content: <p>disable</p>, isDisabled: true },
        ]}
      />,
    );
    const title1 = getByText('タブアイテム1');
    const title2 = getByText('タブアイテム2');
    const title3 = getByText('タブアイテム3');
    const content1 = getByText('Content 1');
    const content2 = getByText('Content 2');
    expect(title1).toBeInTheDocument();
    expect(title2).toBeInTheDocument();
    expect(title3).toBeInTheDocument();
    expect(content1).not.toBeVisible();
    expect(content2).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('default tab', async () => {
    const { getByText, container } = render(
      <Tabs
        tabs={[
          { id: '1', title: 'タブアイテム1', content: <Content1 /> },
          { id: '2', title: 'タブアイテム2', content: <Content2 /> },
          { id: '3', title: 'タブアイテム3', content: <p>disable</p>, isDisabled: true },
        ]}
      />,
    );

    expect(getByText('Content 1')).toBeInTheDocument();
    expect(getByText('Content 2')).not.toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('change tab', async () => {
    const { getByText, container } = render(
      <Tabs
        tabs={[
          { id: '1', title: 'タブアイテム1', content: <Content1 /> },
          { id: '2', title: 'タブアイテム2', content: <Content2 />, isDefault: true },
          { id: '3', title: 'タブアイテム3', content: <p>disable</p>, isDisabled: true },
        ]}
      />,
    );
    const title1 = getByText('タブアイテム1');
    const title3 = getByText('タブアイテム3');

    expect(getByText('Content 1')).not.toBeVisible();
    expect(getByText('Content 2')).toBeVisible();

    await userEvent.click(title1);

    await waitFor(() => {
      expect(getByText('Content 1')).toBeVisible();
      expect(getByText('Content 2')).not.toBeVisible();
      expect(container).toMatchSnapshot();
    });

    // the button is disabled, the content should be the same
    userEvent.click(title3);

    await waitFor(() => {
      expect(getByText('Content 1')).toBeInTheDocument();
      expect(getByText('Content 2')).not.toBeVisible();
    });
  });

  test('onChange tab called when tab changes', async () => {
    const onChange = vi.fn();

    const { getByText } = render(
      <Tabs
        tabs={[
          { id: '1', title: 'タブアイテム1', content: <Content1 /> },
          { id: '2', title: 'タブアイテム2', content: <Content2 />, isDefault: true },
          { id: '3', title: 'タブアイテム3', content: <p>disable</p>, isDisabled: true },
        ]}
        onChange={onChange}
      />,
    );

    expect(getByText('Content 2')).toBeInTheDocument();

    await userEvent.click(getByText('タブアイテム1'));

    expect(onChange).toHaveBeenCalledTimes(2);
    expect(onChange).toHaveBeenCalledWith('1');
  });
});

import React from 'react';
import Tabs from '../Tabs';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

const Content1 = () => {
  return <p>Content 1</p>;
};

const Content2 = () => {
  return <p>Content 2</p>;
};

describe('Tabs', () => {
  test('tabs', async ({ task }) => {
    await takeScreenshot(
      <Tabs
        tabs={[
          { id: '1', title: 'タブアイテム1', content: <Content1 /> },
          { id: '2', title: 'タブアイテム2', content: <Content2 />, isDefault: true },
          { id: '3', title: 'タブアイテム3', content: <>disabled</>, isDisabled: true },
        ]}
      />,
      task,
    );
  });
});

### Tabs example:

```js
import React from 'react';
import Tabs from '@axa-japan/design-system-react/Tabs';

const handleChange = (activeTab) => {
  // Change handler code here
};

const Content1 = () => {
  return <p>Content 1</p>;
};

const Content2 = () => {
  return <p>Content 2</p>;
};

<Tabs
  tabs={[
    { id: '1', title: 'タブアイテム1', content: <Content1 /> },
    { id: '2', title: 'タブアイテム2', content: <Content2 />, isDefault: true },
    { id: '3', title: 'タブアイテム3', content: <p>Content 3</p> },
  ]}
  onChange={(activeTab) => handleChange(activeTab)}
/>;
```

### Disabled tabs:

```js
import React from 'react';
import Tabs from '@axa-japan/design-system-react/Tabs';

const handleChange = (activeTab) => {
  // Change handler code here
};

const Content1 = () => {
  return <p>Content 1</p>;
};

const Content2 = () => {
  return <p>Content 2</p>;
};

<Tabs
  tabs={[
    { id: '1', title: 'タブアイテム1', content: <Content1 /> },
    { id: '2', title: 'タブアイテム2', content: <Content2 />, isDisabled: true },
    { id: '3', title: 'タブアイテム3', content: <p>disabled</p> },
  ]}
  onChange={(activeTab) => handleChange(activeTab)}
/>;
```

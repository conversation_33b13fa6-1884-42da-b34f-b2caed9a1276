import styled from 'styled-components';
import { getColorVar } from '../colors';

const TabMenu = styled.div`
  overflow: auto hidden;
  white-space: nowrap;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  ::-webkit-scrollbar {
    display: none;
  }

  display: flex;
  flex-direction: row;
  width: 100%;
  border-bottom: 1px solid ${getColorVar('utilityStrokeLight')};
`;

export default TabMenu;

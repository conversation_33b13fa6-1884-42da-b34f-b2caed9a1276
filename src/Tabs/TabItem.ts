import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getTypographyVar } from '../typography';

const TabItem = styled.button`
  all: unset;
  display: inline-block;
  cursor: pointer;
  padding: ${getSpacingVar(4)};
  color: ${getColorVar('interactiveActivePrimary')};
  position: relative;

  &[data-selected='true']:not(:disabled) {
    font-weight: ${getTypographyVar('boldFontWeight')};
  }

  &[data-selected='true']:not(:disabled)::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: ${getColorVar('interactiveActivePrimary')};
  }

  :hover {
    background-color: ${getColorVar('interactiveHoverGreyTransparent')};
    color: ${getColorVar('interactiveHoverPrimary')};

    &[data-selected='true']:not(:disabled)::before {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 4px;
      background-color: ${getColorVar('interactiveHoverPrimary')};
    }

    &:disabled {
      background-color: ${getColorVar('utilityBackgroundWhite')};
    }
  }

  &:disabled {
    color: ${getColorVar('interactiveDisabledDark')};
    cursor: no-drop;
  }

  &:focus-visible:not(:disabled) {
    box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
  }
`;

export default TabItem;

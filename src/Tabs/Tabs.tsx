import React, { useEffect, useState } from 'react';
import TabsBase, { useSx } from './TabsBase';
import TabItem from './TabItem';
import TabMenu from './TabMenu';
import TabContent from './TabContent';
import { MarginSxPropType } from '../sx';
import TabContentItem from './TabContentItem';

export type TabsProps = {
  /** Define your tab */
  tabs: {
    id: string;
    title: string;
    content: React.ReactElement;
    isDisabled?: boolean;
    isDefault?: boolean;
  }[];
  onChange?: (id: string) => void;
  /** Style overrides */
  sx?: MarginSxPropType;
} & Omit<React.ComponentPropsWithoutRef<'div'>, 'onChange'>;

const Tabs: React.FC<TabsProps> = ({ tabs, onChange, sx, ...rest }) => {
  const [activeTab, setActiveTab] = useState<string>(tabs.find((tab) => tab.isDefault)?.id ?? tabs[0].id);

  useEffect(() => {
    onChange?.(activeTab);
  }, [activeTab, onChange]);

  return (
    <TabsBase {...rest} style={useSx(sx, {})}>
      <TabMenu role="tablist">
        {tabs.map((tab) => (
          <TabItem
            role="tab"
            tabIndex={0}
            id={tab.id}
            key={tab.id}
            onClick={() => !tab.isDisabled && setActiveTab(tab.id)}
            disabled={!!tab.isDisabled}
            aria-controls={`${tab.id}-panel`}
            aria-selected={activeTab === tab.id}
            data-selected={activeTab === tab.id}
          >
            {tab.title}
          </TabItem>
        ))}
      </TabMenu>
      <TabContent>
        {tabs.map((tab) => (
          <TabContentItem
            role="tabpanel"
            id={`${tab.id}-panel`}
            key={`${tab.id}-panel`}
            tabIndex={0}
            data-hidden={tab.id !== activeTab}
            aria-hidden={tab.id !== activeTab}
            aria-labelledby={tab.id}
          >
            {tab.content}
          </TabContentItem>
        ))}
      </TabContent>
    </TabsBase>
  );
};

export default Tabs;

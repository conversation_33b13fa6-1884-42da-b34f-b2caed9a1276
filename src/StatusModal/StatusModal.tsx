import React, { useId } from 'react';
import * as Dialog from '@radix-ui/react-dialog';

import ModalOverlay from '../ModalBase/ModalOverlay';
import ModalContent from '../ModalBase/ModalContent';
import ModalDescription from '../ModalBase/ModalDescription';
import ModalPositioner from '../ModalBase/ModalPositioner';
import ModalActions from '../ModalBase/ModalActions';
import StatusModalTitleContainer from './StatusModalTitleContainer';
import { WarningIcon } from '../Icons';
import Button from '../Button/Button';
import StatusModalTitle from './StatusModalTitle';

export type StatusModalButtonProps = {
  /** button text */
  text: string;
  /** pass true if modal should be closed after click (uncontrolled) */
  closeModalOnClick?: boolean;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color'>;

export type StatusModalButtonWithActionProps = {
  text: string;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color' | 'onClick'>;

export type StatusModalProps = {
  /** Optional modal trigger */
  children?: React.ReactElement;
  /** Modal title text */
  title: string;
  /** Modal content */
  content: React.ReactNode;
  /** Function to update the modal status */
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  /** List of 1-2 modal button props, in case of two button, the second is to close the modal and you can only change the name */
  buttons: [StatusModalButtonWithActionProps] | [StatusModalButtonWithActionProps, StatusModalButtonProps];
} & Pick<Dialog.DialogProps, 'defaultOpen' | 'open' | 'onOpenChange'> &
  Pick<React.ComponentPropsWithoutRef<'div'>, 'id'>;

const StatusModal: React.FC<StatusModalProps> = ({ id, title, buttons: modalButtonProps, open, setOpen, children, content, ...rest }) => {
  const randomId = useId();
  const isStatusModalControlled = open !== undefined && setOpen !== undefined;
  const renderModalActionArea = () => {
    const closeButton = modalButtonProps[0] && (
      <Dialog.Close asChild>
        <Button
          variant={modalButtonProps.length === 1 ? 'outlined' : 'text'}
          color="blue"
          onClick={isStatusModalControlled ? () => setOpen(false) : undefined}
          {...modalButtonProps[0]}
        >
          {modalButtonProps[0].text}
        </Button>
      </Dialog.Close>
    );
    const actionButton = modalButtonProps[1] && (
      <Button variant="outlined" color="blue" {...modalButtonProps[1]}>
        {modalButtonProps[1].text}
      </Button>
    );
    // If the modal is uncontrolled and closeModalOnClick is specified, close modal when button is clicked
    if (!isStatusModalControlled) {
      return (
        <>
          {closeButton}
          {modalButtonProps[1]?.closeModalOnClick ? <Dialog.Close asChild>{actionButton}</Dialog.Close> : actionButton}
        </>
      );
    }
    return (
      <>
        {closeButton}
        {actionButton}
      </>
    );
  };
  return (
    <Dialog.Root open={isStatusModalControlled ? open : undefined} {...rest}>
      {children && modalButtonProps.length > 0 ? <Dialog.Trigger asChild>{children}</Dialog.Trigger> : null}
      <Dialog.Portal>
        <ModalOverlay />
        <ModalPositioner
          data-testid="modal-positioner"
          id={`status-modal-id-${id || randomId}`}
          onEscapeKeyDown={isStatusModalControlled ? () => setOpen(false) : undefined}
          onPointerDownOutside={isStatusModalControlled ? () => setOpen(false) : undefined}
          onInteractOutside={isStatusModalControlled ? () => setOpen(false) : undefined}
        >
          <ModalContent data-testid="modal-content">
            <StatusModalTitleContainer>
              <WarningIcon size="large" aria-hidden />
              <StatusModalTitle data-testid="modal-title">{title}</StatusModalTitle>
            </StatusModalTitleContainer>
            <Dialog.Description asChild>
              <ModalDescription data-testid="modal-description">{content}</ModalDescription>
            </Dialog.Description>
            <ModalActions>{renderModalActionArea()}</ModalActions>
          </ModalContent>
        </ModalPositioner>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default StatusModal;

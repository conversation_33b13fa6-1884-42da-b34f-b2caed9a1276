import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';

const StatusModalTitleContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${getSpacingVar(2)};
  padding: ${getSpacingVar(8)} ${getSpacingVar(6)} ${getSpacingVar(2)} ${getSpacingVar(6)};

  svg {
    fill: ${getColorVar('statusWarningDark')};
  }
`;

export default StatusModalTitleContainer;

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`StatusModal > does not render when open is false 1`] = `<div />`;

exports[`StatusModal > renders without crashing (controlled) 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c2 {
  background-color: var(--ajds-color-utility-overlay);
  left: 0;
  position: fixed;
  top: 0;
}

.c2[data-state='open'] {
  height: 100%;
  width: 100%;
  -webkit-animation: cJoqxJ 0.6s ease;
  animation: cJoqxJ 0.6s ease;
  z-index: var(--ajds-z-index-overlay);
  pointer-events: none;
}

.c2[data-state='closed'] {
  -webkit-animation: ehzjup 0.6s ease;
  animation: ehzjup 0.6s ease;
}

.c4 {
  background: var(--ajds-color-utility-background-white);
  border-radius: var(--ajds-radius-sm);
  overflow: auto;
}

.c8 {
  background: var(--ajds-color-utility-background-white);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-6);
  overflow-y: auto;
  line-height: 1.6rem;
  overflow-wrap: anywhere;
  white-space: break-spaces;
}

.c3 {
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  width: 552px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  max-height: 100%;
  padding: var(--ajds-spacing-20) 0;
  z-index: var(--ajds-z-index-overlay);
}

.c3[data-state='open'] > div {
  -webkit-animation: cJoqxJ 0.6s ease;
  animation: cJoqxJ 0.6s ease;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-state='closed'] > div {
  -webkit-animation: ehzjup 0.6s ease;
  animation: ehzjup 0.6s ease;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-6) var(--ajds-spacing-6) var(--ajds-spacing-6);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  gap: var(--ajds-spacing-4);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-8) var(--ajds-spacing-6) var(--ajds-spacing-2) var(--ajds-spacing-6);
}

.c5 svg {
  fill: var(--ajds-color-status-warning-dark);
}

.c6 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  overflow-wrap: anywhere;
  text-align: center;
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-xl);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (max-width:599px) {
  .c3 {
    width: 100%;
    padding: var(--ajds-spacing-10) var(--ajds-spacing-4);
  }
}

@media (max-width:599px) {
  .c9 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    gap: var(--ajds-spacing-4);
  }
}

<body
  data-scroll-locked="1"
  style="pointer-events: none;"
>
  <span
    aria-hidden="true"
    data-aria-hidden="true"
    data-radix-focus-guard=""
    style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
    tabindex="0"
  />
  <div
    aria-hidden="true"
    data-aria-hidden="true"
  >
    <button
      aria-controls="radix-:r1:"
      aria-expanded="true"
      aria-haspopup="dialog"
      aria-label="Modal Trigger"
      class="c0"
      data-state="open"
      style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
      type="button"
    >
      <span
        class="c1"
        data-text-variant="false"
        style="--ajds-ButtonText-text-decoration: none;"
      >
        Modal Trigger
      </span>
    </button>
  </div>
  <div
    class="chakra-portal"
  >
    <div
      aria-label="Notifications-top"
      aria-live="polite"
      id="chakra-toast-manager-top"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column; margin: 0px auto;"
    />
    <div
      aria-label="Notifications-top-left"
      aria-live="polite"
      id="chakra-toast-manager-top-left"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-top-right"
      aria-live="polite"
      id="chakra-toast-manager-top-right"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-bottom-left"
      aria-live="polite"
      id="chakra-toast-manager-bottom-left"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-bottom"
      aria-live="polite"
      id="chakra-toast-manager-bottom"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column; margin: 0px auto;"
    />
    <div
      aria-label="Notifications-bottom-right"
      aria-live="polite"
      id="chakra-toast-manager-bottom-right"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
  </div>
  <div
    aria-hidden="true"
    class="c2"
    data-aria-hidden="true"
    data-state="open"
    style="pointer-events: auto;"
  />
  <div
    aria-describedby="radix-:r3:"
    aria-labelledby="radix-:r2:"
    class="c3"
    data-state="open"
    data-testid="modal-positioner"
    id="status-modal-id-default-modal"
    role="dialog"
    style="pointer-events: auto;"
    tabindex="-1"
  >
    <div
      class="c4"
      data-testid="modal-content"
    >
      <div
        class="c5"
      >
        <svg
          aria-hidden="true"
          class="c6"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
            fill-rule="evenodd"
          />
        </svg>
        <h2
          class="c7"
          data-testid="modal-title"
          id="radix-:r2:"
        >
          Default Modal
        </h2>
      </div>
      <div
        class="c8"
        data-testid="modal-description"
        id="radix-:r3:"
      >
        Content for the Modal
      </div>
      <div
        class="c9"
      >
        <button
          aria-label="Close"
          class="c0"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
          type="button"
        >
          <span
            class="c1"
            data-text-variant="false"
            style="--ajds-ButtonText-text-decoration: none;"
          >
            Close
          </span>
        </button>
      </div>
    </div>
  </div>
  <span
    aria-hidden="true"
    data-aria-hidden="true"
    data-radix-focus-guard=""
    style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
    tabindex="0"
  />
</body>
`;

exports[`StatusModal > renders without crashing (uncontrolled) 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c2 {
  background-color: var(--ajds-color-utility-overlay);
  left: 0;
  position: fixed;
  top: 0;
}

.c2[data-state='open'] {
  height: 100%;
  width: 100%;
  -webkit-animation: cJoqxJ 0.6s ease;
  animation: cJoqxJ 0.6s ease;
  z-index: var(--ajds-z-index-overlay);
  pointer-events: none;
}

.c2[data-state='closed'] {
  -webkit-animation: ehzjup 0.6s ease;
  animation: ehzjup 0.6s ease;
}

.c4 {
  background: var(--ajds-color-utility-background-white);
  border-radius: var(--ajds-radius-sm);
  overflow: auto;
}

.c8 {
  background: var(--ajds-color-utility-background-white);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-6);
  overflow-y: auto;
  line-height: 1.6rem;
  overflow-wrap: anywhere;
  white-space: break-spaces;
}

.c3 {
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  width: 552px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  max-height: 100%;
  padding: var(--ajds-spacing-20) 0;
  z-index: var(--ajds-z-index-overlay);
}

.c3[data-state='open'] > div {
  -webkit-animation: cJoqxJ 0.6s ease;
  animation: cJoqxJ 0.6s ease;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-state='closed'] > div {
  -webkit-animation: ehzjup 0.6s ease;
  animation: ehzjup 0.6s ease;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-6) var(--ajds-spacing-6) var(--ajds-spacing-6);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  gap: var(--ajds-spacing-4);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-8) var(--ajds-spacing-6) var(--ajds-spacing-2) var(--ajds-spacing-6);
}

.c5 svg {
  fill: var(--ajds-color-status-warning-dark);
}

.c6 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  overflow-wrap: anywhere;
  text-align: center;
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-xl);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (max-width:599px) {
  .c3 {
    width: 100%;
    padding: var(--ajds-spacing-10) var(--ajds-spacing-4);
  }
}

@media (max-width:599px) {
  .c9 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    gap: var(--ajds-spacing-4);
  }
}

<body
  data-scroll-locked="1"
  style="pointer-events: none;"
>
  <span
    aria-hidden="true"
    data-aria-hidden="true"
    data-radix-focus-guard=""
    style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
    tabindex="0"
  />
  <div
    aria-hidden="true"
    data-aria-hidden="true"
  >
    <button
      aria-controls="radix-:r5:"
      aria-expanded="true"
      aria-haspopup="dialog"
      aria-label="Modal Trigger"
      class="c0"
      data-state="open"
      style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
      type="button"
    >
      <span
        class="c1"
        data-text-variant="false"
        style="--ajds-ButtonText-text-decoration: none;"
      >
        Modal Trigger
      </span>
    </button>
  </div>
  <div
    class="chakra-portal"
  >
    <div
      aria-label="Notifications-top"
      aria-live="polite"
      id="chakra-toast-manager-top"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column; margin: 0px auto;"
    />
    <div
      aria-label="Notifications-top-left"
      aria-live="polite"
      id="chakra-toast-manager-top-left"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-top-right"
      aria-live="polite"
      id="chakra-toast-manager-top-right"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-bottom-left"
      aria-live="polite"
      id="chakra-toast-manager-bottom-left"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
    <div
      aria-label="Notifications-bottom"
      aria-live="polite"
      id="chakra-toast-manager-bottom"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column; margin: 0px auto;"
    />
    <div
      aria-label="Notifications-bottom-right"
      aria-live="polite"
      id="chakra-toast-manager-bottom-right"
      role="region"
      style="position: fixed; z-index: var(--toast-z-index, 5500); pointer-events: none; display: flex; flex-direction: column;"
    />
  </div>
  <div
    aria-hidden="true"
    class="c2"
    data-aria-hidden="true"
    data-state="open"
    style="pointer-events: auto;"
  />
  <div
    aria-describedby="radix-:r7:"
    aria-labelledby="radix-:r6:"
    class="c3"
    data-state="open"
    data-testid="modal-positioner"
    id="status-modal-id-default-modal"
    role="dialog"
    style="pointer-events: auto;"
    tabindex="-1"
  >
    <div
      class="c4"
      data-testid="modal-content"
    >
      <div
        class="c5"
      >
        <svg
          aria-hidden="true"
          class="c6"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
            fill-rule="evenodd"
          />
        </svg>
        <h2
          class="c7"
          data-testid="modal-title"
          id="radix-:r6:"
        >
          Default Modal
        </h2>
      </div>
      <div
        class="c8"
        data-testid="modal-description"
        id="radix-:r7:"
      >
        Content for the Modal
      </div>
      <div
        class="c9"
      >
        <button
          aria-label="Close"
          class="c0"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
          type="button"
        >
          <span
            class="c1"
            data-text-variant="true"
            style="--ajds-ButtonText-text-decoration: underline;"
          >
            Close
          </span>
        </button>
        <button
          aria-label="Action Button"
          class="c0"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
          type="button"
        >
          <span
            class="c1"
            data-text-variant="false"
            style="--ajds-ButtonText-text-decoration: none;"
          >
            Action Button
          </span>
        </button>
      </div>
    </div>
  </div>
  <span
    aria-hidden="true"
    data-aria-hidden="true"
    data-radix-focus-guard=""
    style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
    tabindex="0"
  />
</body>
`;

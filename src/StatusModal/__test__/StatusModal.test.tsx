import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import Button from '../../Button';
import { render } from '../../utils/testUtils';
import StatusModal from '../StatusModal';

describe('StatusModal', () => {
  test('renders without crashing (controlled)', async () => {
    const Component: React.FC = () => {
      const [isOpen, setIsOpen] = useState(false);

      const onClick = () => setIsOpen((currentValue) => !currentValue);

      return (
        <StatusModal
          id="default-modal"
          open={isOpen}
          setOpen={setIsOpen}
          title="Default Modal"
          content="Content for the Modal"
          buttons={[
            {
              text: 'Close',
            },
          ]}
        >
          <Button variant="filled" color="blue" onClick={onClick}>
            Modal Trigger
          </Button>
        </StatusModal>
      );
    };

    const { getByText, getByTestId, baseElement } = render(<Component />);

    // open modal and test the close modal icon button
    await userEvent.click(getByText('Modal Trigger'));

    const modal = getByTestId('modal-positioner');
    expect(getByText('Content for the Modal')).toBeInTheDocument();
    expect(getByText('Default Modal')).toBeInTheDocument();
    expect(getByText('Close')).toBeInTheDocument();
    expect(baseElement).toMatchSnapshot();

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByText('Close'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });
  });

  test('renders without crashing (uncontrolled)', async () => {
    const { getByText, getByTestId, baseElement } = render(
      <StatusModal
        id="default-modal"
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Close',
          },
          {
            text: 'Action Button',
            closeModalOnClick: true,
          },
        ]}
      >
        <Button variant="filled" color="blue">
          Modal Trigger
        </Button>
      </StatusModal>,
    );

    // open modal and test the close modal icon button
    await userEvent.click(getByText('Modal Trigger'));

    let modal = getByTestId('modal-positioner');
    expect(getByText('Content for the Modal')).toBeInTheDocument();
    expect(getByText('Default Modal')).toBeInTheDocument();
    expect(getByText('Close')).toBeInTheDocument();
    expect(baseElement).toMatchSnapshot();

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByText('Close'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });

    // open modal and test the closeModalOnClick prop on action button
    await userEvent.click(getByText('Modal Trigger'));

    modal = getByTestId('modal-positioner');

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByText('Action Button'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });
  });

  test('does not render when open is false', () => {
    const { queryByText, container } = render(
      <StatusModal
        id="close-modal"
        open={false}
        setOpen={() => {}}
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Close',
          },
          {
            text: 'Submit',
          },
        ]}
      />,
    );
    expect(queryByText('Content for the Modal')).not.toBeInTheDocument();
    expect(queryByText('Default Modal')).not.toBeInTheDocument();
    expect(queryByText('Close')).not.toBeInTheDocument();
    expect(queryByText('Submit')).not.toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

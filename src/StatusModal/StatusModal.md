Note: In order to use the Modal component, you need to create your own trigger to open the Modal, pass it as _children_ to the modal, and handle the logic yourself.

You can also use the modal without a passing any trigger to _children_. This is useful if you want to trigger the modal depending on specific conditions, like on page load.

You can also define 0-1 action buttons for the Modal and handle the logic yourself. Additionally a button is always added to close the modal. And also add unique id to your all modal use in project.

We also have a more generic modal component, the [Modal](https://axa-japan-design-system.axa.co.jp/#/Overlay/Modal).

### Status Modal (Controlled):

For the _StatusModal_, the first button passed will always be a _Close_ button.

- _setOpen_: When _Controlled_, the _setOpen_ prop passed will be used by us to handle that button's ability to close the modal, as well as during actions such as _onEscapeKeyDown_, _onPointerDownOutside_, _onInteractOutside_.
- _open_: Optional _Controlled_ prop to control if modal is open or not.

```js
import React, { useState } from 'react';
import StatusModal from '@axa-japan/design-system-react/StatusModal';
import Button from '@axa-japan/design-system-react/Button';

function ModalExample() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);

  return (
    <StatusModal
      id="status-modal"
      open={open}
      setOpen={setOpen}
      title="モーダルタイトル"
      content="インターネット接続がないため、書類をダウンロードすることができませんでした。接続環境を確認してもう一度お試しください。"
      buttons={[
        {
          text: 'ボタン',
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Modal Trigger
      </Button>
    </StatusModal>
  );
}

<ModalExample />;
```

### Status Modal (Uncontrolled):

- _defaultOpen_: Optional _Uncontrolled_ prop to control if modal is open or not.
- _closeModalOnClick_ (button prop): When _Uncontrolled_, you do not handle any state, therefore, _closeModalOnClick_ should be passed if you want the modal to close _onClick_.

```js
import React, { useState } from 'react';
import StatusModal from '@axa-japan/design-system-react/StatusModal';
import Button from '@axa-japan/design-system-react/Button';

<StatusModal
  id="status-modal"
  title="モーダルタイトル"
  content="インターネット接続がないため、書類をダウンロードすることができませんでした。接続環境を確認してもう一度お試しください。"
  buttons={[
    {
      text: 'ボタン',
    },
  ]}
>
  <Button variant="filled" color="blue">
    Modal Trigger
  </Button>
</StatusModal>;
```

### Status Modal with Two Buttons:

```js
import React, { useState } from 'react';
import Modal from '@axa-japan/design-system-react/StatusModal';
import Button from '@axa-japan/design-system-react/Button';

function ModalExample() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);

  return (
    <Modal
      id="status-modal-with-two-button"
      open={open}
      setOpen={setOpen}
      title="モーダルタイトル"
      content="インターネット接続がないため、書類をダウンロードすることができませんでした。接続環境を確認してもう一度お試しください。"
      buttons={[
        {
          text: 'ボタン',
        },
        {
          text: 'ボタン',
          onClick: onClick,
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Modal Trigger
      </Button>
    </Modal>
  );
}

<ModalExample />;
```

### Default Text:

```js
import Text from '@axa-japan/design-system-react/Text';

<Text>This is the default text style</Text>;
```

### With Color:

```js
import Text from '@axa-japan/design-system-react/Text';

<div>
  <Text color="primary">Text with primary color</Text>
  <Text color="primary-white">Text with primary white color</Text>
  <Text color="secondary">Text with secondary color</Text>
  <Text color="secondary-white">Text with secondary white color</Text>
  <Text color="accent">Text with accent color</Text>
  <Text color="success">Text with success color</Text>
  <Text color="danger">Text with danger color</Text>
</div>;
```

### With Size:

```js
import Text from '@axa-japan/design-system-react/Text';

<div>
  <Text size="md">Default size</Text>
  <Text size="sm">Small size</Text>
  <Text size="lg">Large size</Text>
</div>;
```

### With Weight:

```js
import Text from '@axa-japan/design-system-react/Text';

<div>
  <Text weight="regular">Regular weight</Text>
  <Text weight="bold">Bold weight</Text>
</div>;
```

### With As:

```js
import Text from '@axa-japan/design-system-react/Text';

<div>
  <Text as="p">Paragraph text</Text>
  <Text as="span">Span text</Text>
  <Text as="div">Div text</Text>
</div>;
```

### With Truncate:

```js
import Text from '@axa-japan/design-system-react/Text';

<div style={{ width: '200px' }}>
  <Text truncate>This text is truncated with an ellipsis</Text>
</div>;
```

### With Align:

```js
import Text from '@axa-japan/design-system-react/Text';

<div style={{ width: '200px' }}>
  <Text align="start">Start align</Text>
  <Text align="end">End align</Text>
  <Text align="center">Center align</Text>
</div>;
```

### With Wrap:

```js
import Text from '@axa-japan/design-system-react/Text';

<div style={{ width: '300px' }}>
  <Text weight="bold">Wrap:</Text>
  <Text wrap="wrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
  <Text weight="bold">Nowrap:</Text>
  <Text wrap="nowrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
  <Text weight="bold">Balance:</Text>
  <Text wrap="balance">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
  <Text weight="bold">Pretty:</Text>
  <Text wrap="pretty">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
</div>;
```

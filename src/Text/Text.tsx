import React from 'react';
import { MarginSxPropType } from '../sx';
import { TextBase, useSx } from './TextBase';

export type TextProps = {
  /** Type of tag to use as the container */
  as?: 'p' | 'span' | 'div';
  size?: 'lg' | 'md' | 'sm';
  color?: 'primary' | 'primary-white' | 'secondary' | 'secondary-white' | 'accent' | 'success' | 'danger';
  weight?: 'regular' | 'bold';
  /** Shows ellipsis */
  truncate?: boolean;
  /** Sets the text-align property */
  align?: 'start' | 'end' | 'center';
  /** Sets the text-wrap property */
  wrap?: 'wrap' | 'nowrap' | 'balance' | 'pretty';
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'p' | 'span'>;

const Text: React.FC<TextProps> = ({
  children,
  as = 'p',
  size = 'md',
  color = 'primary',
  weight = 'regular',
  align = 'start',
  truncate = false,
  // Not setting any default here as if its set, it will override the truncate behavior
  wrap,
  sx,
  ...rest
}) => {
  const sxOverrides = useSx(sx, { size, color, weight, truncate: String(truncate), align });
  const cleansedAs = ['p', 'span', 'div'].includes(as) ? as : 'p';

  let style = sxOverrides;
  // Handle wrap separately as it is using same rule as truncate, which creates conflict in sx variants
  const wrapValues = ['wrap', 'nowrap', 'balance', 'pretty'];
  if (wrap && wrapValues.includes(wrap)) {
    style = { ...sxOverrides, textWrap: wrap };
  }
  return (
    <TextBase {...rest} as={cleansedAs} style={style}>
      {children}
    </TextBase>
  );
};

export default Text;

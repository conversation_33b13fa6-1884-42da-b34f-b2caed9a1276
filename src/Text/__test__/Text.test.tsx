import React from 'react';
import { render } from '../../utils/testUtils';
import Text from '../Text';

describe('Text', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Text>text-component</Text>);
    const testElement = getByText('text-component');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
  ['p', 'span'].forEach((tag) => {
    test(`renders with tag ${tag} without crashing`, () => {
      const { getByText, container } = render(<Text as={tag as 'p' | 'span'}>Text</Text>);
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['md', 'sm', 'lg'].forEach((size) => {
    test(`renders with size ${size} without crashing`, () => {
      const { getByText, container } = render(<Text size={size as 'md' | 'sm' | 'lg'}>Text</Text>);
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['regular', 'bold'].forEach((weight) => {
    test(`renders with weight ${weight} without crashing`, () => {
      const { getByText, container } = render(<Text weight={weight as 'regular' | 'bold'}>Text</Text>);
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['primary', 'primary-white', 'secondary', 'secondary-white', 'accent', 'success', 'danger'].forEach((color) => {
    test(`renders with color ${color} without crashing`, () => {
      const { getByText, container } = render(
        <Text color={color as 'primary' | 'primary-white' | 'secondary' | 'secondary-white' | 'accent' | 'success' | 'danger'}>Text</Text>,
      );
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  [false, true].forEach((truncate) => {
    test(`renders with truncate ${truncate} without crashing`, () => {
      const { getByText, container } = render(<Text truncate={truncate}>Text</Text>);
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
  ['wrap', 'nowrap', 'balance', 'pretty'].forEach((wrap) => {
    test(`renders with wrap ${wrap} without crashing`, () => {
      const { getByText, container } = render(<Text wrap={wrap as 'wrap' | 'nowrap' | 'balance' | 'pretty'}>Text</Text>);
      const testElement = getByText(/Text/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

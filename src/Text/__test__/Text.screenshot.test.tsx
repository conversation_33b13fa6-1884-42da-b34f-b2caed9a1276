import React from 'react';
import Text from '../Text';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Text', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Text>This is default color text</Text>, task);
  });

  test('PrimaryWhite', async ({ task }) => {
    await takeScreenshot(<Text color="primary-white">This is primary white color text</Text>, task);
  });

  test('Secondary', async ({ task }) => {
    await takeScreenshot(<Text color="secondary">This is secondary color text</Text>, task);
  });

  test('SecondaryWhite', async ({ task }) => {
    await takeScreenshot(<Text color="secondary-white">This is secondary white color text</Text>, task);
  });

  test('Accent', async ({ task }) => {
    await takeScreenshot(<Text color="accent">This is accent color text</Text>, task);
  });

  test('Success', async ({ task }) => {
    await takeScreenshot(<Text color="success">This is success color text</Text>, task);
  });

  test('Danger', async ({ task }) => {
    await takeScreenshot(<Text color="danger">This is danger color text</Text>, task);
  });

  test('Span', async ({ task }) => {
    await takeScreenshot(<Text as="span">This is a span text</Text>, task);
  });

  test('Div', async ({ task }) => {
    await takeScreenshot(<Text as="div">This is a div text</Text>, task);
  });

  test('Small', async ({ task }) => {
    await takeScreenshot(<Text size="sm">This is a small text</Text>, task);
  });

  test('Large', async ({ task }) => {
    await takeScreenshot(<Text size="lg">This is a large text</Text>, task);
  });

  test('Bold', async ({ task }) => {
    await takeScreenshot(<Text weight="bold">This is a semibold text</Text>, task);
  });

  test('Truncated', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '200px' }}>
        <Text truncate>This text is truncated with an ellipsis</Text>
      </div>,
      task,
    );
  });

  test('Aligned', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '200px' }}>
        <Text align="start">Start align</Text>
        <Text align="end">End align</Text>
        <Text align="center">Center align</Text>
      </div>,
      task,
    );
  });

  test('Wrapped', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '300px' }}>
        <Text weight="bold">Wrap:</Text>
        <Text wrap="wrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
        <Text weight="bold">Nowrap:</Text>
        <Text wrap="nowrap">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
        <Text weight="bold">Balance:</Text>
        <Text wrap="balance">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
        <Text weight="bold">Pretty:</Text>
        <Text wrap="pretty">Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem aut cum eum id quos est.</Text>
      </div>,
      task,
    );
  });
});

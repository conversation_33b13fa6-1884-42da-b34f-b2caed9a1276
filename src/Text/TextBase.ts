import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getTypographyVar } from '../typography';

const variants: VariantsType<'size' | 'color' | 'weight' | 'truncate' | 'align'> = {
  size: {
    md: {
      'font-size': getTypographyVar('defaultFontSize'),
    },
    sm: {
      'font-size': getTypographyVar('smFontSize'),
    },
    lg: {
      'font-size': getTypographyVar('lgFontSize'),
    },
  },
  color: {
    primary: {
      color: getColorVar('characterPrimary'),
    },
    'primary-white': {
      color: getColorVar('characterPrimaryWhite'),
    },
    secondary: {
      color: getColorVar('characterSecondary'),
    },
    'secondary-white': {
      color: getColorVar('characterSecondaryWhite'),
    },
    accent: {
      color: getColorVar('statusImportant'),
    },
    success: {
      color: getColorVar('statusSuccess'),
    },
    danger: {
      color: getColorVar('statusDanger'),
    },
  },
  weight: {
    regular: {
      'font-weight': getTypographyVar('defaultFontWeight'),
    },
    bold: {
      'font-weight': getTypographyVar('boldFontWeight'),
    },
  },
  truncate: {
    false: {
      overflow: 'visible',
      'white-space': 'normal',
      'text-overflow': 'clip',
    },
    true: {
      overflow: 'hidden',
      'white-space': 'nowrap',
      'text-overflow': 'ellipsis',
    },
  },
  align: {
    start: {
      'text-align': 'start',
    },
    end: {
      'text-align': 'end',
    },
    center: {
      'text-align': 'center',
    },
  },
};

const { useSx, getSxStyleRules } = sx('Text', ['margin'], variants);

export { useSx, variants };

export const TextBase = styled.p`
  line-height: ${getTypographyVar('lineHeight')};
  ${getSxStyleRules()}
`;

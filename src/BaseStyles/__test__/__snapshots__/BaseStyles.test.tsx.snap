// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BaseStyles renders without crashing 1`] = `
.c0 {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  color: #333333;
  font-family: 'Source Sans Pro',<PERSON><PERSON>,'メイリオ','Hiragino Kaku Gothic ProN W3','ヒラギノ角ゴ ProN W3',sans-serif;
  font-size: 16px;
  font-style: normal;
  line-height: 1.15;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

.c0::before,
.c0::after {
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  box-sizing: border-box;
}

.c1 {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  color: #333333;
  font-family: 'Source Sans Pro',<PERSON><PERSON>,'メイリオ','<PERSON><PERSON><PERSON> Gothic ProN W3','ヒラギノ角ゴ ProN W3',sans-serif;
  font-size: 16px;
  font-style: normal;
  line-height: 1.15;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: inherit;
  margin: 0;
}

.c1::before,
.c1::after {
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  box-sizing: border-box;
}

.c2 {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  color: #333333;
  font-family: 'Source Sans Pro',Meiryo,'メイリオ','Hiragino Kaku Gothic ProN W3','ヒラギノ角ゴ ProN W3',sans-serif;
  font-size: 16px;
  font-style: normal;
  line-height: 1.15;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: inherit;
  margin: 0;
}

.c2::before,
.c2::after {
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  box-sizing: border-box;
}

@media (max-width:767px) {
  .c1 {
    font-size: 30px;
    -webkit-letter-spacing: -0.01em;
    -moz-letter-spacing: -0.01em;
    -ms-letter-spacing: -0.01em;
    letter-spacing: -0.01em;
    font-weight: 600;
    line-height: 34px;
    padding-bottom: 13px;
  }
}

@media (min-width:768px) {
  .c1 {
    font-size: 48px;
    -webkit-letter-spacing: -0.01em;
    -moz-letter-spacing: -0.01em;
    -ms-letter-spacing: -0.01em;
    letter-spacing: -0.01em;
    font-weight: 600;
    line-height: 54px;
    padding-bottom: 18px;
  }
}

@media (max-width:767px) {
  .c2 {
    font-size: 12px;
    -webkit-letter-spacing: 0.01em;
    -moz-letter-spacing: 0.01em;
    -ms-letter-spacing: 0.01em;
    letter-spacing: 0.01em;
    font-weight: 400;
    line-height: 18px;
    padding-bottom: 10px;
  }
}

@media (min-width:768px) {
  .c2 {
    font-size: 16px;
    -webkit-letter-spacing: 0.01em;
    -moz-letter-spacing: 0.01em;
    -ms-letter-spacing: 0.01em;
    letter-spacing: 0.01em;
    font-weight: 400;
    line-height: 24px;
    padding-bottom: 11px;
  }
}

<div>
  <main
    class="c0"
  >
    <p
      class="c1"
      size="16"
    >
      Some title
    </p>
    <p
      class="c2"
      size="16"
    >
      Some text some text some text some text some text some text some text some text some text
    </p>
  </main>
</div>
`;

// import React from 'react';
// import { render } from '@testing-library/react';
// import BaseStyles from '../BaseStyles';
// import Main from '../../Main';
// import Text from '../../Text';

describe('BaseStyles', () => {
  test.skip('renders without crashing', () => {
    // TODO: re-implement some tests
    // const { getByText, container } = render(
    //   <Main>
    //     <BaseStyles />
    //     <Text size="48">Some title</Text>
    //     <Text>Some text some text some text some text some text some text some text some text some text</Text>
    //   </Main>,
    // );
    // const testElement = getByText('Some title');
    // expect(testElement).toBeInTheDocument();
    // expect(container).toMatchSnapshot();
  });
});

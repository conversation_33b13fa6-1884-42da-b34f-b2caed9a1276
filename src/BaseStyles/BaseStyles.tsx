import React from 'react';
import { createStandaloneToast } from '@chakra-ui/toast';
import { Base } from './BaseStyles.styles';
import { cssColorVarMap } from '../colors';
import { cssSpacingVarMap } from '../spacing';
import { cssTypographyVarMap } from '../typography';
import { cssEffectVarMap } from '../css-effects';
import { cssShadowVarMap } from '../shadows';
import { cssZIndexVarMap } from '../zIndex';
import { cssRadiusVarMap } from '../radius';

const cssColorVarMapKeys = Object.keys(cssColorVarMap) as Array<keyof typeof cssColorVarMap>;
const cssSpacingVarMapKeys = Object.keys(cssSpacingVarMap) as Array<keyof typeof cssSpacingVarMap>;
const cssTypographyVarMapKeys = Object.keys(cssTypographyVarMap) as Array<keyof typeof cssTypographyVarMap>;
const cssEffectVarMapKeys = Object.keys(cssEffectVarMap) as Array<keyof typeof cssEffectVarMap>;
const cssShadowVarMapKeys = Object.keys(cssShadowVarMap) as Array<keyof typeof cssShadowVarMap>;
const cssZIndexVarMapKeys = Object.keys(cssZIndexVarMap) as Array<keyof typeof cssZIndexVarMap>;
const cssRadiusVarMapKeys = Object.keys(cssRadiusVarMap) as Array<keyof typeof cssRadiusVarMap>;

const BaseStyles: React.FC<React.PropsWithChildren> = ({ children }) => {
  const cssVars = `
    ${cssColorVarMapKeys.reduce((acc, key) => `${acc}${cssColorVarMap[key].name}: ${cssColorVarMap[key].value};`, '')}
    ${cssSpacingVarMapKeys.reduce((acc, key) => `${acc}${cssSpacingVarMap[key].name}: ${cssSpacingVarMap[key].value};`, '')}
    ${cssTypographyVarMapKeys.reduce((acc, key) => `${acc}${cssTypographyVarMap[key].name}: ${cssTypographyVarMap[key].value};`, '')}
    ${cssEffectVarMapKeys.reduce((acc, key) => `${acc}${cssEffectVarMap[key].name}: ${cssEffectVarMap[key].value};`, '')}
    ${cssShadowVarMapKeys.reduce((acc, key) => `${acc}${cssShadowVarMap[key].name}: ${cssShadowVarMap[key].value};`, '')}
    ${cssZIndexVarMapKeys.reduce((acc, key) => `${acc}${cssZIndexVarMap[key].name}: ${cssZIndexVarMap[key].value};`, '')}
    ${cssRadiusVarMapKeys.reduce((acc, key) => `${acc}${cssRadiusVarMap[key].name}: ${cssRadiusVarMap[key].value};`, '')}
    `;

  const { ToastContainer } = createStandaloneToast();

  return (
    <>
      <Base cssVars={cssVars} />
      <ToastContainer />
      {children}
    </>
  );
};

export default BaseStyles;

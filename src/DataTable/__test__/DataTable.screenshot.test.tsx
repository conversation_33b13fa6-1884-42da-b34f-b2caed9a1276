import React from 'react';
import DataTable from '../DataTable';
import { takeScreenshot } from '../../utils/screenshotTestUtils';
import { data, allColumns, cellStyleColumns, cellWidthColumns, checkboxColumns, radioColumns, groupedColumns } from './testData';

describe('DataTable', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={allColumns} />, task);
  });

  test('WithDenseCells', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellStyleColumns} density="compact" />, task);
  });

  test('WithHorizontalCenter', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellStyleColumns} horizontalAlign="center" />, task);
  });

  test('WithHorizontalRight', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellStyleColumns} horizontalAlign="right" />, task);
  });

  test('WithVerticalTop', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellStyleColumns} verticalAlign="top" />, task);
  });

  test('WithVerticalBottom', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellStyleColumns} verticalAlign="bottom" />, task);
  });

  test('WithColumnWidths', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={cellWidthColumns} />, task);
  });

  test('WithCheckboxColumn', async ({ task }) => {
    await takeScreenshot(
      <DataTable
        required
        fieldTitle="Form table"
        data={data}
        columns={checkboxColumns}
        checkboxConfig={{
          id: 'checkbox-row-select-col',
          headerConfig: () => {
            return { label: '全て選択', value: 'data-table-checkbox-select-all' };
          },
          cellConfig: (row) => {
            return { label: row.id, value: row.id };
          },
        }}
      />,
      task,
    );
  });

  test('WithRadioColumn', async ({ task }) => {
    await takeScreenshot(
      <DataTable
        required
        fieldTitle="Form table"
        data={data}
        columns={radioColumns}
        radioConfig={{
          id: 'radio-row-select-col',
          cellConfig: (row) => {
            return { label: '', value: row.id };
          },
        }}
      />,
      task,
    );
  });

  test('WithHeaderGroups', async ({ task }) => {
    await takeScreenshot(<DataTable data={data} columns={groupedColumns} />, task);
  });
});

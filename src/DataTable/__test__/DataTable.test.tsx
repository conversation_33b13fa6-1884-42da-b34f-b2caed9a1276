import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import DataTable from '../DataTable';
import { data, cellStyleColumns, checkboxColumns, radioColumns, groupedColumns } from './testData';
import { ColumnWidthOptionState, DensityState, HorizontalAlignState, VerticalAlignState } from '../../Table/types';

describe('DataTable', () => {
  beforeAll(() => {
    (window as any).PointerEvent = MouseEvent;
  });
  it('renders without crashing', () => {
    const { getAllByRole, container } = render(<DataTable data={data} columns={cellStyleColumns} />);
    const columnHeaders = getAllByRole('columnheader');
    expect(columnHeaders[0].textContent).toEqual('firstName');
    const cells = getAllByRole('cell');
    expect(cells.length).toEqual(6);
    const sortButtons = getAllByRole('button');
    expect(sortButtons.length).toEqual(2);
    expect(container).toMatchSnapshot();
  });
  it.each(['default', 'compact'] as DensityState[])('renders with cell density - %s ', (prop) => {
    const { container } = render(<DataTable data={data} columns={cellStyleColumns} density={prop} />);
    expect(container).toMatchSnapshot();
  });
  it.each(['left', 'center', 'right'] as HorizontalAlignState[])('renders with cell horizontalAlign - %s ', (prop) => {
    const { container } = render(<DataTable data={data} columns={cellStyleColumns} horizontalAlign={prop} />);
    expect(container).toMatchSnapshot();
  });
  it.each(['top', 'center', 'bottom'] as VerticalAlignState[])('renders with cell verticalAlign - %s ', (prop) => {
    const { container } = render(<DataTable data={data} columns={cellStyleColumns} verticalAlign={prop} />);
    expect(container).toMatchSnapshot();
  });
  it.each(['fit', 'fixed', 'fill'] as ColumnWidthOptionState[])('renders with cell columnWidthOption - %s ', (prop) => {
    const { container } = render(
      <DataTable data={data} columns={cellStyleColumns} columnWidthOption={prop} width={prop === 'fixed' ? 64 : undefined} />,
    );
    expect(container).toMatchSnapshot();
  });
  it('renders with stickyHeaders', () => {
    const { container } = render(<DataTable data={data} columns={cellStyleColumns} stickyHeader />);
    expect(container).toMatchSnapshot();
  });
  it('renders with checkbox column and controlled state, and check header checkbox function', async () => {
    const Wrapper = () => {
      const [rowSelection, setRowSelection] = useState({});
      return (
        <DataTable
          required
          fieldTitle="Form table"
          data={data}
          columns={checkboxColumns}
          state={{ rowSelection }}
          onRowSelectionChange={setRowSelection}
          checkboxConfig={{
            id: 'checkbox-row-select-col',
            headerConfig: () => {
              return { label: '', value: 'data-table-checkbox-select-all' };
            },
            cellConfig: (row) => {
              return { label: row.id, value: row.id };
            },
          }}
        />
      );
    };
    const { container, queryAllByText } = render(<Wrapper />);
    const label = queryAllByText('Form table');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);
    let checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
    expect(checkboxCells.length).toEqual(4);
    await waitFor(() => {
      checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('unchecked'));
    });
    // check that clicking the header checkbox checks all the checkboxes
    await userEvent.click(checkboxCells[0]);
    checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
    await waitFor(() => {
      checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('checked'));
    });
    // click the first checkbox, and expect it to become unchecked
    await userEvent.click(checkboxCells[1]);
    checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
    await waitFor(() => {
      expect(checkboxCells[1].getAttribute('data-state')).toEqual('unchecked');
    });
    // click the header checkbox again, and expect all checkboxes to be checked
    await userEvent.click(checkboxCells[0]);
    checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
    await waitFor(() => {
      checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('checked'));
    });
    expect(container).toMatchSnapshot();
  });
  it('renders with radio column', () => {
    const { container } = render(
      <DataTable
        required
        fieldTitle="Form table"
        data={data}
        columns={radioColumns}
        radioConfig={{
          id: 'radio-row-select-col',
          cellConfig: (row) => {
            return { label: '', value: row.id };
          },
        }}
      />,
    );
    expect(container).toMatchSnapshot();
  });
  it('renders with grouped columns', () => {
    const { container } = render(<DataTable data={data} columns={groupedColumns} />);
    expect(container).toMatchSnapshot();
  });
  it('renders with pinned columns', () => {
    const { container } = render(<DataTable data={data} columns={cellStyleColumns} pinnedColumns={['firstName', 'lastName']} />);
    expect(container).toMatchSnapshot();
  });
  it('test sort button functionality', async () => {
    const { getAllByRole, container } = render(<DataTable data={data} columns={cellStyleColumns} />);
    let cells = getAllByRole('cell');
    expect(cells.length).toEqual(6);
    expect(cells[0].textContent).toEqual('Jon');
    expect(cells[2].textContent).toEqual('Kyla');
    expect(cells[4].textContent).toEqual('Britney');
    const sortButtons = getAllByRole('button');
    // click sort button, first, sort by ascending by default
    await userEvent.click(sortButtons[0]);
    cells = getAllByRole('cell');
    await waitFor(() => {
      expect(cells[0].textContent).toEqual('Britney');
      expect(cells[2].textContent).toEqual('Jon');
      expect(cells[4].textContent).toEqual('Kyla');
    });
    // click sort button, again, sort by descending next
    await userEvent.click(sortButtons[0]);
    cells = getAllByRole('cell');
    await waitFor(() => {
      expect(cells[0].textContent).toEqual('Kyla');
      expect(cells[2].textContent).toEqual('Jon');
      expect(cells[4].textContent).toEqual('Britney');
    });
    // click sort button, again, return to original sort
    await userEvent.click(sortButtons[0]);
    cells = getAllByRole('cell');
    await waitFor(() => {
      expect(cells[0].textContent).toEqual('Jon');
      expect(cells[2].textContent).toEqual('Kyla');
      expect(cells[4].textContent).toEqual('Britney');
    });
    expect(container).toMatchSnapshot();
  });
  it('test initial sort functionality', async () => {
    const { getAllByRole, container } = render(
      <DataTable
        data={data}
        columns={cellStyleColumns}
        initialState={{
          sorting: [
            {
              id: 'firstName',
              desc: true,
            },
          ],
        }}
      />,
    );
    const cells = getAllByRole('cell');
    expect(cells.length).toEqual(6);
    expect(cells[0].textContent).toEqual('Kyla');
    expect(cells[2].textContent).toEqual('Jon');
    expect(cells[4].textContent).toEqual('Britney');
    expect(container).toMatchSnapshot();
  });
});

import React from 'react';
import Badge from '../../Badge';
import <PERSON> from '../../Link';
import Button from '../../Button';
import IconButton from '../../IconButton';
import FieldError from '../../FieldError';
import { AddIcon } from '../../Icons';
import { createColumnHelper, type DataTableColumnDef } from '../types';

export type Person = {
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  status: string;
  progress: number;
};

const columnHelper = createColumnHelper<Person>();

export const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    createdAt: new Date('2024-04-20T08:40:28'),
    status: 'relationship',
    rank: 64,
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    createdAt: new Date('2024-10-14T12:16:20'),
    status: 'single',
    rank: 35,
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    visits: 523,
    progress: 9,
    createdAt: new Date('2023-09-19T19:35:16'),
    status: 'complicated',
    rank: 1,
  },
];

export const allColumns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('age', {
    id: 'Link',
    cell: ({ getValue }) => <Link>{getValue().toString()}</Link>,
    header: () => 'Link',
  }),
  columnHelper.accessor('status', {
    id: 'Button',
    cell: ({ getValue }) => <Button>{getValue()}</Button>,
    header: () => <span>Button</span>,
  }),
  columnHelper.display({
    id: 'IconButton',
    cell: () => <IconButton icon={<AddIcon />} />,
    header: 'Icon Button',
  }),
  columnHelper.display({
    id: 'Error',
    cell: () => <FieldError hasError={true} errorMessage="Error" />,
    header: 'Error',
  }),
  columnHelper.display({
    id: 'Freeform',
    cell: ({ row }) => (
      <ul>
        {row.getAllCells().map((cell) => {
          const value = cell.getValue() as string;
          return value ? <li key={cell.id}>{value}</li> : null;
        })}
      </ul>
    ),
    header: 'Free form',
  }),
];

export const cellStyleColumns = [
  columnHelper.accessor('firstName', {
    id: 'firstName',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('lastName', {
    id: 'lastName',
    cell: (info) => info.getValue(),
  }),
];

export const cellWidthColumns: DataTableColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'fit',
    cell: (info) => info.getValue(),
    header: 'fit (default)',
  }),
  columnHelper.accessor('firstName', {
    id: 'fixed',
    cell: (info) => info.getValue(),
    header: 'fixed',
    meta: { columnWidthOption: 'fixed', width: 80 },
  }),
  columnHelper.accessor('firstName', {
    id: 'fill',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
];

export const checkboxColumns: DataTableColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
];

export const radioColumns: DataTableColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'radioExample',
    cell: (info) => info.getValue(),
    header: 'Radio example',
  }),
];

export const groupedColumns: DataTableColumnDef<Person, string>[] = [
  columnHelper.group({
    header: 'Name',
    id: 'Name',
    columns: [
      columnHelper.accessor('firstName', {
        id: 'firstName',
        cell: (info) => info.getValue(),
      }),
      columnHelper.accessor('lastName', {
        id: 'lastName',
        cell: (info) => info.getValue(),
        header: () => <span>Last Name</span>,
      }),
    ],
  }),
  columnHelper.group({
    header: 'Info',
    id: 'info',
    columns: [
      columnHelper.accessor('age', {
        id: 'age',
        header: () => 'Age',
      }),
      columnHelper.group({
        header: 'More Info',
        id: 'more Info',
        meta: { horizontalAlign: 'center' },
        columns: [
          columnHelper.accessor('visits', {
            id: 'visits',
            header: () => <span>Visits</span>,
          }),
          columnHelper.accessor('status', {
            id: 'status',
            header: 'Status',
          }),
          columnHelper.accessor('progress', {
            id: 'progress',
            header: 'Profile Progress',
          }),
        ],
      }),
    ],
  }),
];

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DataTable > renders with cell columnWidthOption - fill  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            style="width: 50%;"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            style="width: 50%;"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fill"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 50%;"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell columnWidthOption - fit  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell columnWidthOption - fixed  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fixed"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
            style="width: 150px; min-width: 150px; max-width: 150px;"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell density - compact  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="compact"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell density - default  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell horizontalAlign - center  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell horizontalAlign - left  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell horizontalAlign - right  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="right"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell verticalAlign - bottom  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="bottom"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell verticalAlign - center  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with cell verticalAlign - top  1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="top"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with checkbox column and controlled state, and check header checkbox function 1`] = `
.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c5 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c7 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c7 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c7[data-horizontal-align='center'] {
  text-align: center;
}

.c7[data-horizontal-align='right'] {
  text-align: right;
}

.c7[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c7[data-vertical-align='top'] {
  vertical-align: top;
}

.c7[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c7[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c7[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c7[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c17 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c17 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c17[data-horizontal-align='center'] {
  text-align: center;
}

.c17[data-horizontal-align='right'] {
  text-align: right;
}

.c17[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c17[data-vertical-align='top'] {
  vertical-align: top;
}

.c17[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c17[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c17[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c17[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c6 th,
.c6 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c6[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c14 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c14 svg {
  color: currentColor;
}

.c14:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c14:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c15 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c15 svg {
  color: currentColor;
}

.c15[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c16 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c13 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c10:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c10[data-disabled] {
  cursor: not-allowed;
}

.c10[data-disabled]:hover {
  background-color: initial;
}

.c10:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c10[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c12[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c12[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c12[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c12[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c12[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c12[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c12[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c9:hover > .c11[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c18 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c18[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c18[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

@media (hover:hover) {
  .c15:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c15:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <fieldset
    class="c0"
    style="overflow: auto;"
  >
    <legend
      class="c1"
      id="field-legend-:re:"
    >
      Form table
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <table
        class="c5"
      >
        <thead>
          <tr
            class="c6"
          >
            <th
              class="c7"
              colspan="1"
              data-checkbox-header="true"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
            >
              <div
                class="c8"
              >
                <label
                  aria-checked="true"
                  class="c9 c10"
                  data-part="root"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  for="checkbox::rr::input"
                  id="checkbox::rr:"
                >
                  <div
                    aria-hidden="true"
                    class="c11 c12"
                    data-part="control"
                    data-scope="checkbox"
                    data-state="checked"
                    dir="ltr"
                    id="checkbox::rr::control"
                  >
                    <svg
                      class="c13"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                      />
                    </svg>
                  </div>
                  <input
                    aria-invalid="false"
                    aria-labelledby="checkbox::rr::label"
                    checked=""
                    id="checkbox::rr::input"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="checkbox"
                    value="data-table-checkbox-select-all"
                  />
                </label>
              </div>
            </th>
            <th
              class="c7"
              colspan="1"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
            >
              <div
                class="c8"
              >
                Checkbox example
                <button
                  aria-label="Sort ascending"
                  class="c14"
                  style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                  type="button"
                >
                  <div
                    class="c15"
                    style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                  >
                    <span
                      class="c16"
                    >
                      <svg
                        class="c13"
                        fill="none"
                        focusable="false"
                        style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          clip-path="url(#clip0_2806_7818)"
                        >
                          <path
                            d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                          />
                        </g>
                        <defs>
                          <clippath
                            id="clip0_2806_7818"
                          >
                            <rect
                              fill="white"
                              height="24"
                              transform="translate(0 0.000732422)"
                              width="24"
                            />
                          </clippath>
                        </defs>
                      </svg>
                    </span>
                  </div>
                </button>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            class="c6"
          >
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              <label
                aria-checked="true"
                class="c9 c10"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::rs::input"
                id="checkbox::rs:"
              >
                <div
                  aria-hidden="true"
                  class="c11 c12"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rs::control"
                >
                  <svg
                    class="c13"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <span
                  class="c18"
                  data-part="label"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rs::label"
                >
                  0
                </span>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::rs::label"
                  checked=""
                  id="checkbox::rs::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="0"
                />
              </label>
            </td>
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              Jon
            </td>
          </tr>
          <tr
            class="c6"
          >
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              <label
                aria-checked="true"
                class="c9 c10"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::rt::input"
                id="checkbox::rt:"
              >
                <div
                  aria-hidden="true"
                  class="c11 c12"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rt::control"
                >
                  <svg
                    class="c13"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <span
                  class="c18"
                  data-part="label"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rt::label"
                >
                  1
                </span>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::rt::label"
                  checked=""
                  id="checkbox::rt::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="1"
                />
              </label>
            </td>
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              Kyla
            </td>
          </tr>
          <tr
            class="c6"
          >
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              <label
                aria-checked="true"
                class="c9 c10"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::ru::input"
                id="checkbox::ru:"
              >
                <div
                  aria-hidden="true"
                  class="c11 c12"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::ru::control"
                >
                  <svg
                    class="c13"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <span
                  class="c18"
                  data-part="label"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::ru::label"
                >
                  2
                </span>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::ru::label"
                  checked=""
                  id="checkbox::ru::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="2"
                />
              </label>
            </td>
            <td
              class="c17"
              data-col-width-option="fit"
              data-column-pin="false"
              data-density="default"
              data-horizontal-align="left"
              data-last-left-pinned-column="false"
              data-vertical-align="center"
            >
              Britney
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </fieldset>
</div>
`;

exports[`DataTable > renders with grouped columns 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="2"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              Name
            </div>
          </th>
          <th
            class="c2"
            colspan="4"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              Info
            </div>
          </th>
        </tr>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          />
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          />
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          />
          <th
            class="c2"
            colspan="3"
            data-density="default"
            data-horizontal-align="center"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              More Info
            </div>
          </th>
        </tr>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              <span>
                Last Name
              </span>
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              Age
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              <span>
                Visits
              </span>
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              Status
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              Profile Progress
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            15
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            156
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            relationship
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            2
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            0
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          />
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            single
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            28
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            0
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            523
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            complicated
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            9
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with pinned columns 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-left="0px"
            style="left: 0px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="true"
            data-left="150px"
            style="left: 150px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-left="0px"
            data-vertical-align="center"
            style="left: 0px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="true"
            data-left="150px"
            data-vertical-align="center"
            style="left: 150px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-left="0px"
            data-vertical-align="center"
            style="left: 0px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="true"
            data-left="150px"
            data-vertical-align="center"
            style="left: 150px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-left="0px"
            data-vertical-align="center"
            style="left: 0px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="left"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="true"
            data-left="150px"
            data-vertical-align="center"
            style="left: 150px; width: 150px; min-width: 150px; max-width: 150px;"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders with radio column 1`] = `
.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c6 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c14 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c14 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c14[data-horizontal-align='center'] {
  text-align: center;
}

.c14[data-horizontal-align='right'] {
  text-align: right;
}

.c14[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c14[data-vertical-align='top'] {
  vertical-align: top;
}

.c14[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c14[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c14[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c14[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c7 th,
.c7 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c7[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c10 svg {
  color: currentColor;
}

.c10:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c10:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c11 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c11 svg {
  color: currentColor;
}

.c11[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c12 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c13 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
}

.c16:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c16[data-disabled] {
  cursor: not-allowed;
}

.c16[data-disabled]:hover {
  background-color: initial;
}

.c16:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c16[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c16[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c18 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c18[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c18[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c18[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c18[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c18[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c18[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c18[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c18[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c15:hover > .c17[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c5[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c5[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

@media (hover:hover) {
  .c11:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c11:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <fieldset
    class="c0"
    style="overflow: auto;"
  >
    <legend
      class="c1"
      id="field-legend-:r10:"
    >
      Form table
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:data-table-id-:rv::id:label"
        aria-orientation="vertical"
        class="c5"
        data-orientation="vertical"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:data-table-id-:rv::id"
        role="radiogroup"
        style="position: relative;"
      >
        <table
          class="c6"
        >
          <thead>
            <tr
              class="c7"
            >
              <th
                class="c8"
                colspan="1"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
              >
                <div
                  class="c9"
                />
              </th>
              <th
                class="c8"
                colspan="1"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
              >
                <div
                  class="c9"
                >
                  Radio example
                  <button
                    aria-label="Sort ascending"
                    class="c10"
                    style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                    type="button"
                  >
                    <div
                      class="c11"
                      style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                    >
                      <span
                        class="c12"
                      >
                        <svg
                          class="c13"
                          fill="none"
                          focusable="false"
                          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            clip-path="url(#clip0_2806_7818)"
                          >
                            <path
                              d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                            />
                          </g>
                          <defs>
                            <clippath
                              id="clip0_2806_7818"
                            >
                              <rect
                                fill="white"
                                height="24"
                                transform="translate(0 0.000732422)"
                                width="24"
                              />
                            </clippath>
                          </defs>
                        </svg>
                      </span>
                    </div>
                  </button>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c7"
            >
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                <label
                  class="c15 c16"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-table-id-:rv::id:radio:input:0"
                  id="radio-group:data-table-id-:rv::id:radio:0"
                >
                  <div
                    aria-hidden="true"
                    class="c17 c18"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-table-id-:rv::id:radio:control:0"
                  >
                    <svg
                      class="c13"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-table-id-:rv::id"
                    id="radio-group:data-table-id-:rv::id:radio:input:0"
                    name="data-table-id-:rv::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="0"
                  />
                </label>
              </td>
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                Jon
              </td>
            </tr>
            <tr
              class="c7"
            >
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                <label
                  class="c15 c16"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-table-id-:rv::id:radio:input:1"
                  id="radio-group:data-table-id-:rv::id:radio:1"
                >
                  <div
                    aria-hidden="true"
                    class="c17 c18"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-table-id-:rv::id:radio:control:1"
                  >
                    <svg
                      class="c13"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-table-id-:rv::id"
                    id="radio-group:data-table-id-:rv::id:radio:input:1"
                    name="data-table-id-:rv::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="1"
                  />
                </label>
              </td>
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                Kyla
              </td>
            </tr>
            <tr
              class="c7"
            >
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                <label
                  class="c15 c16"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-table-id-:rv::id:radio:input:2"
                  id="radio-group:data-table-id-:rv::id:radio:2"
                >
                  <div
                    aria-hidden="true"
                    class="c17 c18"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-table-id-:rv::id:radio:control:2"
                  >
                    <svg
                      class="c13"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-table-id-:rv::id"
                    id="radio-group:data-table-id-:rv::id:radio:input:2"
                    name="data-table-id-:rv::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="2"
                  />
                </label>
              </td>
              <td
                class="c14"
                data-col-width-option="fit"
                data-column-pin="false"
                data-density="default"
                data-horizontal-align="left"
                data-last-left-pinned-column="false"
                data-vertical-align="center"
              >
                Britney
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`DataTable > renders with stickyHeaders 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
          data-pin="true"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > renders without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > test initial sort functionality 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort descending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
                      />
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`DataTable > test sort button functionality 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c2 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-light);
}

.c2 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c2[data-horizontal-align='center'] {
  text-align: center;
}

.c2[data-horizontal-align='right'] {
  text-align: right;
}

.c2[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c2[data-vertical-align='top'] {
  vertical-align: top;
}

.c2[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c2[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c2[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c2[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c8 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-4) var(--ajds-spacing-8);
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: var(--ajds-spacing-20);
  background-color: var(--ajds-color-utility-background-white);
}

.c8 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8[data-horizontal-align='center'] {
  text-align: center;
}

.c8[data-horizontal-align='right'] {
  text-align: right;
}

.c8[data-density='compact'] {
  padding: var(--ajds-spacing-2);
  height: var(--ajds-spacing-12);
}

.c8[data-vertical-align='top'] {
  vertical-align: top;
}

.c8[data-vertical-align='bottom'] {
  vertical-align: bottom;
}

.c8[data-column-pin='left'] {
  position: -webkit-sticky;
  position: sticky;
  z-index: var(--ajds-z-index-above);
}

.c8[data-column-pin='left'][data-last-left-pinned-column='true'] {
  border-right: 1.5px solid var(--ajds-color-utility-stroke-light);
}

.c8[data-col-width-option='fit'] {
  white-space: nowrap;
}

.c1 th,
.c1 td {
  border: 0.5px solid var(--ajds-color-utility-stroke-light);
}

.c1[data-pin] {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: var(--ajds-z-index-sticky);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    style="overflow: auto;"
  >
    <table
      class="c0"
    >
      <thead>
        <tr
          class="c1"
        >
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              firstName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
          <th
            class="c2"
            colspan="1"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
          >
            <div
              class="c3"
            >
              lastName
              <button
                aria-label="Sort ascending"
                class="c4"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c5"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        clip-path="url(#clip0_2806_7818)"
                      >
                        <path
                          d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
                        />
                      </g>
                      <defs>
                        <clippath
                          id="clip0_2806_7818"
                        >
                          <rect
                            fill="white"
                            height="24"
                            transform="translate(0 0.000732422)"
                            width="24"
                          />
                        </clippath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Jon
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Schaefer
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Kyla
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Hayes-Deckow
          </td>
        </tr>
        <tr
          class="c1"
        >
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Britney
          </td>
          <td
            class="c8"
            data-col-width-option="fit"
            data-column-pin="false"
            data-density="default"
            data-horizontal-align="left"
            data-last-left-pinned-column="false"
            data-vertical-align="center"
          >
            Harber
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

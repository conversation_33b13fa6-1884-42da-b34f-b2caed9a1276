import React, { forwardRef, useId } from 'react';
import { flexRender, getCoreRowModel, getSortedRowModel, useReactTable, Header } from '@tanstack/react-table';
import DataTableHeaderCellContainer from './DataTableHeaderCellContainer';
import { CustomFeatures, getCellStyles, getPinnedColumnsProps } from '../Table/utils';
import DataTableContainer from './DataTableContainer';
import DataTableHeaderCell from './DataTableHeaderCell';
import DataTableBodyCell from './DataTableBodyCell';
import DataTableRow from './DataTableRow';
import IconButton from '../IconButton';
import { ArrowIcon, DoubleArrowIcon } from '../Icons';
import { SpacingKeysType } from '../spacing';
import FieldBase from '../FieldBase/FieldBase';
import checkHasError from '../utils/checkHasError';
import { DataTableProps } from './types';
import getFormColumnConfig from '../Table/FormColumn';
import RadioRoot from '../Radio/RadioRoot';

const DataTable = forwardRef<HTMLTableElement, DataTableProps>(
  (
    {
      data,
      columns,
      pinnedColumns,
      fieldTitle,
      invalid,
      errorText,
      required = false,
      showRequiredIndicator,
      id,
      stickyHeader,
      columnWidthOption,
      width,
      density,
      horizontalAlign,
      verticalAlign,
      initialState,
      state,
      checkboxConfig,
      radioConfig,
      getRowId,
      onRowSelectionChange,
    },
    ref,
  ) => {
    const hasError = checkHasError(invalid, errorText);
    const fallbackId = `data-table-id-${useId()}`;
    const controlledRowSelectionProps = state?.rowSelection
      ? {
          state: { rowSelection: state?.rowSelection },
          onRowSelectionChange,
        }
      : undefined;
    const formColumn = getFormColumnConfig({ checkboxConfig, radioConfig });
    const table = useReactTable({
      _features: [CustomFeatures], // pass our custom feature to the table to be instantiated upon creation
      columns: formColumn ? [formColumn, ...columns] : columns,
      data,
      stickyHeader,
      columnWidthOption,
      width,
      density,
      horizontalAlign,
      verticalAlign,
      enableMultiRowSelection: !radioConfig,
      getRowId,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(), // client-side sorting
      enableMultiSort: false, // Don't allow shift key to sort multiple columns - default on/true
      initialState: { columnPinning: { left: pinnedColumns }, sorting: initialState?.sorting },
      ...controlledRowSelectionProps,
    });
    const renderHeader = (header: Header<unknown, unknown>) => {
      const sortingHandler = header.column.getToggleSortingHandler();
      return header.isPlaceholder ? null : (
        <DataTableHeaderCellContainer>
          {flexRender(header.column.columnDef.header, header.getContext())}
          {{
            asc: <IconButton icon={<ArrowIcon rotation="270" />} aria-label="Sort ascending" onClick={sortingHandler} />,
            desc: <IconButton icon={<ArrowIcon rotation="90" />} aria-label="Sort descending" onClick={sortingHandler} />,
            false: header.column.getCanSort() ? <IconButton icon={<DoubleArrowIcon />} aria-label="Sort ascending" onClick={sortingHandler} /> : null,
          }[header.column.getIsSorted() as string] ?? null}
        </DataTableHeaderCellContainer>
      );
    };
    const renderTable = (hasCheckboxHeader?: boolean) => (
      <DataTableContainer id={id} ref={ref}>
        <thead>
          {table.getHeaderGroups().map((headerGroup, headerGroupIdx) => (
            <DataTableRow key={headerGroup.id} data-pin={table.getStickyHeader() || null}>
              {headerGroup.headers.map((header, headerIdx) => {
                const { column } = header;
                const { isLastLeftPinnedColumn, left } = getPinnedColumnsProps(column, header);
                // To check if a particular header cell is pinned, we check that cell, and all of its sub-headers as well
                const isPinned = header.getLeafHeaders().every((leaf) => leaf.column.getIsPinned() !== false);
                const pinnedPosition = isPinned ? column.getIsPinned() : undefined;
                const isHeaderColumn = headerGroupIdx === 0 && headerIdx === 0;
                const isCheckboxHeader = hasCheckboxHeader && isHeaderColumn ? true : undefined;
                return (
                  <DataTableHeaderCell
                    style={getCellStyles({
                      colWidthOption: column.getColumnWidthOption(),
                      colWidth: column.getSize() as SpacingKeysType,
                      columnPinningPosition: pinnedPosition,
                      left,
                      table,
                    })}
                    key={header.id}
                    colSpan={header.colSpan}
                    data-density={column.getDensity()}
                    data-horizontal-align={column.getHorizontalAlign()}
                    data-column-pin={pinnedPosition}
                    data-last-left-pinned-column={isLastLeftPinnedColumn}
                    data-left={left}
                    data-checkbox-header={isCheckboxHeader}
                  >
                    {renderHeader(header)}
                  </DataTableHeaderCell>
                );
              })}
            </DataTableRow>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => {
            return (
              <DataTableRow key={row.id}>
                {row.getVisibleCells().map((cell) => {
                  const { column } = cell;
                  const { isLastLeftPinnedColumn, left } = getPinnedColumnsProps(column);
                  return (
                    <DataTableBodyCell
                      data-col-width-option={column.getColumnWidthOption()}
                      style={getCellStyles({
                        colWidthOption: column.getColumnWidthOption(),
                        colWidth: column.getSize() as SpacingKeysType,
                        columnPinningPosition: column.getIsPinned(),
                        left,
                        table,
                      })}
                      key={cell.id}
                      data-density={column.getDensity()}
                      data-horizontal-align={column.getHorizontalAlign()}
                      data-vertical-align={column.getVerticalAlign()}
                      data-column-pin={column.getIsPinned()}
                      data-last-left-pinned-column={isLastLeftPinnedColumn}
                      data-left={left}
                    >
                      {flexRender(column.columnDef.cell, cell.getContext())}
                    </DataTableBodyCell>
                  );
                })}
              </DataTableRow>
            );
          })}
        </tbody>
      </DataTableContainer>
    );
    if (checkboxConfig) {
      return (
        <FieldBase
          style={{ overflow: 'auto' }}
          useFieldsetWrapper
          id={id || fallbackId}
          label={fieldTitle}
          showError={hasError}
          errorMessage={errorText}
          required={required}
          showRequiredIndicator={showRequiredIndicator}
        >
          {renderTable(true)}
        </FieldBase>
      );
    }
    if (radioConfig) {
      return (
        <FieldBase
          style={{ overflow: 'auto' }}
          useFieldsetWrapper
          id={id || fallbackId}
          label={fieldTitle}
          showError={hasError}
          errorMessage={errorText}
          required={required}
          showRequiredIndicator={showRequiredIndicator}
        >
          <RadioRoot id={`${fallbackId}:id`} name={`${fallbackId}:name`}>
            {renderTable()}
          </RadioRoot>
        </FieldBase>
      );
    }
    return <div style={{ overflow: 'auto' }}>{renderTable()}</div>;
  },
);

DataTable.displayName = 'DataTable';

export default React.memo(DataTable);

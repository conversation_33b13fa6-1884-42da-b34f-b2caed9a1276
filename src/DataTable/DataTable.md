### Data Table Introduction:

The two most important props that will be used to generate the table are the _data_ and _columns_.

- [data](https://tanstack.com/table/latest/docs/guide/data): The core data array you provide the table
- [columns](https://tanstack.com/table/latest/docs/guide/column-defs): The column definitions are the single most important part of building a table. They are responsible for building and formatting the underlying data model into what will be displayed in the table. Creating header groups, headers, etc.
  - Use the [createColumnHelper](https://tanstack.com/table/latest/docs/guide/column-defs#column-helpers) helper function which, when called with a row type, returns a utility for creating different column definition types with the highest type-safety possible. Here are examples of the declarations.

### How to type the data and columns:

```js
import { createColumnHelper } from '@axa-japan/design-system-react/DataTable';
// Example on how to type:
// type Person = {
//   firstName?: string;
//   lastName?: string;
//   age?: number;
//   visits?: number;
//   progress?: number;
//   status?: 'relationship' | 'complicated' | 'single';
//   rank?: number;
//   createdAt?: Date;
// };
// export const data: Person[] = [
// export const columns: DataTableColumnDef<Person, string>[] = [
// const columnHelper = createColumnHelper<Person>();

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    createdAt: new Date('2024-04-20T08:40:28'),
    status: 'relationship',
    rank: 64,
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    createdAt: new Date('2024-10-14T12:16:20'),
    status: 'single',
    rank: 35,
  },
];

// define columns with the helper
const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
];
```

### Table cell contents:

Here are the various ways that are recommended to render cell contents:

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';
import Badge from '@axa-japan/design-system-react/Badge';
import Link from '@axa-japan/design-system-react/Link';
import Button from '@axa-japan/design-system-react/Button';
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';
import FieldError from '@axa-japan/design-system-react/FieldError';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('age', {
    id: 'Link',
    cell: ({ getValue }) => <Link>{getValue().toString()}</Link>,
    header: () => 'Link',
  }),
  columnHelper.accessor('status', {
    id: 'Button',
    cell: ({ getValue }) => <Button>{getValue()}</Button>,
    header: () => <span>Button</span>,
  }),
  columnHelper.display({
    id: 'IconButton',
    cell: () => <IconButton icon={<AddIcon />} />,
    header: 'Icon Button',
  }),
  columnHelper.display({
    id: 'Error',
    cell: () => <FieldError hasError={true} errorMessage="Error" />,
    header: 'Error',
  }),
  columnHelper.display({
    id: 'Freeform',
    cell: ({ row }) => (
      <ul>
        {row.getAllCells().map((cell) => {
          const value = cell.getValue();
          return value ? <li key={cell.id}>{cell.getValue()}</li> : null;
        })}
      </ul>
    ),
    header: 'Free form',
  }),
];

<DataTable data={data} columns={columns} />;
```

### Table cell styles:

Here are the various ways you can style the cells. For _horizontalAlign_, it can be set on a _table_ or _column_ level, where _column_ has a higher priority:

- horizontalAlign: _left_ or _center_ or _right_. Defaults to _left_.
- verticalAlign: _top_ or _center_ or _bottom_. Defaults to _center_. (Only table level)
- density: _default_ or _compact_. Compact cells have less padding. (Only table level)

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';
import Badge from '@axa-japan/design-system-react/Badge';
import Link from '@axa-japan/design-system-react/Link';
import Button from '@axa-japan/design-system-react/Button';
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';
import FieldError from '@axa-japan/design-system-react/FieldError';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
    meta: { horizontalAlign: 'center' },
  }),
  columnHelper.accessor('age', {
    id: 'Link',
    cell: ({ getValue }) => <Link>{getValue().toString()}</Link>,
    header: () => 'Link',
  }),
  columnHelper.accessor('status', {
    id: 'Button',
    cell: ({ getValue }) => <Button>{getValue()}</Button>,
    header: () => <span>Button</span>,
  }),
  columnHelper.display({
    id: 'IconButton',
    cell: () => <IconButton icon={<AddIcon />} />,
    header: 'Icon Button',
    meta: { horizontalAlign: 'center' },
  }),
  columnHelper.display({
    id: 'Error',
    cell: () => <FieldError hasError={true} errorMessage="Error" />,
    header: 'Error',
  }),
  columnHelper.display({
    id: 'Freeform',
    cell: ({ row }) => (
      <ul style={{ paddingLeft: '20px' }}>
        {row.getAllCells().map((cell) => {
          const value = cell.getValue();
          return value ? <li key={cell.id}>{cell.getValue()}</li> : null;
        })}
      </ul>
    ),
    header: 'Free form',
    meta: { horizontalAlign: 'left' },
  }),
];

<DataTable data={data} columns={columns} density="compact" horizontalAlign="right" verticalAlign="bottom" />;
```

### Sticky headers:

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const Render = () => {
  const data = [
    {
      firstName: 'Jon',
      lastName: 'Schaefer',
      age: 15,
      visits: 156,
      progress: 2,
      status: 'relationship',
      rank: 64,
    },
    {
      firstName: 'Kyla',
      lastName: 'Hayes-Deckow',
      age: 0,
      progress: 28,
      status: 'single',
      rank: 35,
    },
    {
      firstName: 'Britney',
      lastName: 'Harber',
      age: 0,
      visits: 523,
      progress: 9,
      status: 'complicated',
      rank: 1,
    },
  ];

  const columnHelper = createColumnHelper();
  const columns = [
    columnHelper.accessor('firstName', {
      id: 'firstName',
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor('lastName', {
      id: 'lastName',
      cell: (info) => info.getValue(),
      header: () => <span>Last Name</span>,
    }),
    columnHelper.accessor('age', {
      id: 'age',
      header: () => 'Age',
    }),
    columnHelper.accessor('visits', {
      id: 'visits',
      header: () => <span>Visits</span>,
    }),
    columnHelper.accessor('status', {
      id: 'status',
      header: 'status',
    }),
    columnHelper.accessor('progress', {
      id: 'progress',
      header: 'Profile Progress',
    }),
    columnHelper.accessor('rank', {
      id: 'rank',
      header: 'Rank',
    }),
  ];
  return (
    <div style={{ display: 'flex', height: '200px' }}>
      <DataTable data={data} columns={columns} stickyHeader />
    </div>
  );
};

<Render />;
```

### Column width:

There are 3 ways to set column width: _fit_, _fill_, _fixed_. Default is _fit_. Set via the _columnWidthOption_ prop which can be table or column level. If _fixed_ is chosen, the _width_ prop should be provided as well, which a width value must be picked from our _Spacing_ values.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'fit',
    cell: (info) => info.getValue(),
    header: 'fit (default)',
  }),
  columnHelper.accessor('firstName', {
    id: 'fixed',
    cell: (info) => info.getValue(),
    header: 'fixed',
    meta: { columnWidthOption: 'fixed', width: 80 },
  }),
  columnHelper.accessor('firstName', {
    id: 'fill',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
];

<DataTable data={data} columns={columns} />;
```

### Sorting:

Sorting is enabled by default on columns that are rendered with data. Sorting order goes by unsorted -> ascending -> descending -> unsorted. There are a few sorting related options that can be provided on a column level that changes sorting behavior:

- [enableSorting](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting): Enables/Disables sorting for this column.
- [sortdescfirst](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst): Set to true for sorting toggles on this column to start in the descending direction.
- [sortingFn](https://tanstack.com/table/v8/docs/api/features/sorting#sorting-functions): The sorting function to use with this column. It can be a string referencing a built-in sorting function or a custom sorting function.
- [sortUndefined](https://tanstack.com/table/v8/docs/api/features/sorting#sortundefined): The priority of undefined values when sorting this column.
- [invertSorting](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting): Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    createdAt: new Date('2024-04-20T08:40:28'),
    status: 'relationship',
    rank: 64,
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    createdAt: new Date('2024-10-14T12:16:20'),
    status: 'single',
    rank: 35,
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    visits: 523,
    progress: 9,
    createdAt: new Date('2023-09-19T19:35:16'),
    status: 'complicated',
    rank: 1,
  },
];

// Custom sorting function to sort the rows according to the the status tuple.
// const sortStatusFn: SortingFn<Person> = (rowA, rowB) => {
const sortStatusFn = (rowA, rowB) => {
  const statusA = rowA.original.status;
  const statusB = rowB.original.status;
  const statusOrder = ['single', 'complicated', 'relationship'];
  return statusOrder.indexOf(statusA) - statusOrder.indexOf(statusB);
};

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'firstName',
    cell: (info) => info.getValue(),
    // this column will sort in ascending order by default since it is a string column
  }),
  columnHelper.accessor('lastName', {
    id: 'lastName',
    cell: (info) => info.getValue(),
    header: () => <span>Last Name</span>,
    sortUndefined: 'last', // force undefined values to the end
    sortDescFirst: false, // first sort order will be ascending (nullable values can mess up auto detection of sort order)
  }),
  columnHelper.accessor('age', {
    id: 'age',
    header: () => 'Age',
    // this column will sort in descending order by default since it is a number column
  }),
  // The initial sorting of this sorting is set on the table level below
  columnHelper.accessor('visits', {
    id: 'visits',
    header: () => <span>Visits</span>,
    sortUndefined: 'last', // force undefined values to the end
  }),
  columnHelper.accessor('status', {
    id: 'status',
    header: 'Status',
    sortingFn: sortStatusFn, // use our custom sorting function for this enum column
  }),
  columnHelper.accessor('progress', {
    id: 'progress',
    header: 'Profile Progress',
    enableSorting: false, // disable sorting for this column
  }),
  columnHelper.accessor('rank', {
    id: 'rank',
    header: 'Rank',
    invertSorting: true, // invert the sorting order (golf score-like where smaller is better)
  }),
  columnHelper.accessor('createdAt', {
    id: 'createdAt',
    header: 'Created At',
    sortingFn: 'datetime', // make sure table knows this is a datetime column (usually can detect if no null values)
  }),
];

<DataTable
  data={data}
  columns={columns}
  // Sorts the "visits" with descending on first render
  initialState={{
    sorting: [
      {
        id: 'visits',
        desc: true,
      },
    ],
  }}
/>;
```

### Row selection (Checkbox):

_checkboxConfig_ prop can be added to the table to create a form / editable table. It'll automatically be inserted in the first column of the table. It contains:

- id - id of the form column.
- headerConfig - Callback prop to return the configuration for the _select all_ checkbox in the table header. _table_ which contains data on the table configuration is available as the callback prop.
- cellConfig - Callback prop to return the configuration for the checkboxes in each row. _row_ which contains data on the row configuration is available as the callback prop.

The whole component will be wrapped in our field wrapper. _label_ should be passed for accessibility reasons. Other props for our field wrapper such as _required_ are also available.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
];

<DataTable
  required
  fieldTitle="Form table"
  data={data}
  columns={columns}
  checkboxConfig={{
    id: 'checkbox-row-select-col',
    headerConfig: () => {
      return { label: '', value: 'data-table-checkbox-select-all' };
    },
    cellConfig: (row) => {
      return { label: row.id, value: row.id };
    },
  }}
/>;
```

### Row selection (Radio):

Radio can be added to the table to create a form / editable table via the _radioConfig_ prop. It'll automatically be inserted in the first column of the table. It contains:

- id - id of the form column.
- cellConfig - Callback prop to return the configuration for the radios in each row. _row_ which contains data on the row configuration is available as the callback prop.

The whole component will be wrapped in our field wrapper. _label_ should be passed for accessibility reasons. Other props for our field wrapper such as _required_ are also available.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'radioExample',
    cell: (info) => info.getValue(),
    header: 'Radio example',
  }),
];

<DataTable
  required
  fieldTitle="Form table"
  data={data}
  columns={columns}
  radioConfig={{
    id: 'radio-row-select-col',
    cellConfig: (row) => {
      return { label: '', value: row.id };
    },
  }}
/>;
```

### Managed Row Selection State:

Even though we will already manage the row selection state for you, it is usually more convenient to manage the state yourself in order to have easy access to the selected row ids that you can use to make API calls or other actions.

Pass the _rowSelection_ state and _onRowSelectionChange_ table option to hoist up the row selection state to your own scope.

By default, the row selection state uses the index of each row as the row identifiers. Row selection state can instead be tracked with a custom unique row id by passing in a custom _getRowId_ function to the the table.

Default selected rows can be done via managed state.

```js
import React, { useState } from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const [rowSelection, setRowSelection] = useState({ 1: true });
const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
];

<DataTable
  fieldTitle="Form table"
  data={data}
  columns={columns}
  state={{ rowSelection }}
  onRowSelectionChange={setRowSelection}
  checkboxConfig={{
    id: 'checkbox-row-select-col-controlled',
    headerConfig: () => {
      return { label: '', value: 'data-table-checkbox-select-all-controlled' };
    },
    cellConfig: (row) => {
      return { label: row.id, value: `${row.id}-controlled` };
    },
  }}
/>;
```

### Header groups:

You can group headers together into header groups by providing nested _columns_ props to the _columns_ prop.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    visits: 523,
    progress: 9,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.group({
    header: 'Name',
    id: 'Name',
    columns: [
      // Accessor Column
      columnHelper.accessor('firstName', {
        id: 'firstName',
        cell: (info) => info.getValue(),
      }),
      // Accessor Column
      columnHelper.accessor('lastName', {
        id: 'lastName',
        cell: (info) => info.getValue(),
        header: () => <span>Last Name</span>,
      }),
    ],
  }),
  columnHelper.group({
    header: 'Info',
    id: 'info',
    columns: [
      // Accessor Column
      columnHelper.accessor('age', {
        id: 'age',
        header: () => 'Age',
      }),
      columnHelper.group({
        header: 'More Info',
        id: 'more Info',
        meta: { horizontalAlign: 'center' },
        columns: [
          // Accessor Column
          columnHelper.accessor('visits', {
            id: 'visits',
            header: () => <span>Visits</span>,
          }),
          // Accessor Column
          columnHelper.accessor('status', {
            id: 'status',
            header: 'Status',
          }),
          // Accessor Column
          columnHelper.accessor('progress', {
            id: 'progress',
            header: 'Profile Progress',
          }),
        ],
      }),
    ],
  }),
];

<DataTable data={data} columns={columns} />;
```

### Pinned columns:

You can pin columns left by providing the _id_ of the column.

```js
import React from 'react';
import DataTable, { createColumnHelper } from '@axa-japan/design-system-react/DataTable';
import Badge from '@axa-japan/design-system-react/Badge';
import Link from '@axa-japan/design-system-react/Link';
import Button from '@axa-japan/design-system-react/Button';
import IconButton from '@axa-japan/design-system-react/IconButton';
import { AddIcon } from '@axa-japan/design-system-react/Icons';
import FieldError from '@axa-japan/design-system-react/FieldError';

const Render = () => {
  const data = [
    {
      firstName: 'Jon',
      lastName: 'Schaefer',
      age: 15,
      status: 'relationship',
    },
    {
      firstName: 'Kyla',
      lastName: 'Hayes-Deckow',
      age: 0,
      status: 'single',
    },
    {
      firstName: 'Britney',
      lastName: 'Harber',
      age: 0,
      status: 'complicated',
    },
  ];

  const columnHelper = createColumnHelper();
  const columns = [
    columnHelper.accessor('firstName', {
      id: 'default',
      cell: (info) => info.getValue(),
      header: 'default',
    }),
    columnHelper.accessor('lastName', {
      id: 'Badge',
      cell: (info) => <Badge text={info.column.id} variant="info" />,
      header: () => <span>Badge</span>,
    }),
    columnHelper.accessor('age', {
      id: 'Link',
      cell: ({ getValue }) => <Link>{getValue().toString()}</Link>,
      header: () => 'Link',
    }),
    columnHelper.accessor('status', {
      id: 'Button',
      cell: ({ getValue }) => <Button>{getValue()}</Button>,
      header: () => <span>Button</span>,
    }),
    columnHelper.display({
      id: 'IconButton',
      cell: () => <IconButton icon={<AddIcon />} />,
      header: 'Icon Button',
    }),
    columnHelper.display({
      id: 'Error',
      cell: () => <FieldError hasError={true} errorMessage="Error" />,
      header: 'Error',
    }),
    columnHelper.display({
      id: 'Freeform',
      cell: ({ row }) => (
        <ul>
          {row.getAllCells().map((cell) => {
            const value = cell.getValue();
            return value ? <li key={cell.id}>{cell.getValue()}</li> : null;
          })}
        </ul>
      ),
      header: 'Free form',
    }),
  ];
  return (
    <div style={{ display: 'flex', width: '800px' }}>
      <DataTable data={data} columns={columns} pinnedColumns={['Badge', 'Link']} />
    </div>
  );
};

<Render />;
```

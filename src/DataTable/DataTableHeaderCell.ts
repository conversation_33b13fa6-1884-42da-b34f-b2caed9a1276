import styled, { css } from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getZIndexVar } from '../zIndex';
import table from '../styles/table';
import { getTypographyVar } from '../typography';

export const CellStyles = css`
  ${table.cell}

  padding: ${getSpacingVar(4)} ${getSpacingVar(8)};
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: ${getSpacingVar(20)};

  &[data-density='compact'] {
    padding: ${getSpacingVar(2)};
    height: ${getSpacingVar(12)};
  }

  &[data-vertical-align='top'] {
    vertical-align: top;
  }

  &[data-vertical-align='bottom'] {
    vertical-align: bottom;
  }

  /* Styles if the column is pinned left, we have to set the width and min-width to align multiple pinned columns properly. */
  &[data-column-pin='left'] {
    position: sticky;
    z-index: ${getZIndexVar('above')};

    &[data-last-left-pinned-column='true'] {
      border-right: 1.5px solid ${getColorVar('utilityStrokeLight')};
    }
  }
`;

const DataTableHeaderCell = styled.th`
  ${CellStyles}

  background-color: ${getColorVar('utilityBackgroundLight')};

  &[data-checkbox-header='true'] {
    font-weight: ${getTypographyVar('defaultFontWeight')};
  }
`;

export default DataTableHeaderCell;

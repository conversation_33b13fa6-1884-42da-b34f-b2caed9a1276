/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AccessorFn,
  AccessorFnColumnDef,
  RowData,
  DisplayColumnDef,
  GroupColumnDef,
  IdentifiedColumnDef,
  AccessorColumnDef,
  DeepKeys,
  DeepValue,
  TableOptions,
  TableState,
  createColumnHelper as DataTableCreateColumnHelper,
} from '@tanstack/react-table';
import { CommonColumnDefProps, CustomTableState, TableFieldProps } from '../Table/types';

type OptionalType = 'cell' | 'header' | 'meta' | 'enableSorting';
type OptionalTypeWithSort = 'cell' | 'header' | 'meta' | 'enableSorting' | 'sortDescFirst' | 'sortUndefined' | 'sortingFn' | 'invertSorting';
type RequiredType = 'id';

type DataTableDisplayColumnDef<TData extends RowData, TValue = unknown> = Pick<DisplayColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<DisplayColumnDef<TData, TValue>, RequiredType>>;

type DataTableGroupColumnDef<TData extends RowData, TValue = unknown> = Pick<GroupColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<GroupColumnDef<TData, TValue>, RequiredType>> & {
    columns?: CustomColumnDef<TData, any>[];
  };

type DataTableIdentifiedColumnDef<TData extends RowData, TValue = unknown> = Pick<IdentifiedColumnDef<TData, TValue>, OptionalTypeWithSort> &
  Required<Pick<IdentifiedColumnDef<TData, TValue>, RequiredType>>;

type DataTableAccessorColumnDef<TData extends RowData, TValue = unknown> = Pick<AccessorColumnDef<TData, TValue>, OptionalTypeWithSort> &
  Required<Pick<AccessorColumnDef<TData, TValue>, RequiredType>>;

// Create our own ColumnDef by removing unneeded props
type CustomColumnDef<TData extends RowData, TValue = unknown> =
  | DataTableDisplayColumnDef<TData, TValue>
  | DataTableGroupColumnDef<TData, TValue>
  | DataTableAccessorColumnDef<TData, TValue>;

/** Column definitions type, which is responsible for:
   * 1. Building the underlying data model that will be used for everything including sorting, grouping, etc.
     2. Formatting the data model into what will be displayed in the table
     3. Creating header groups, headers
   */
type DataTableColumnDef<TData extends RowData, TValue = unknown> = CustomColumnDef<TData, TValue> & CommonColumnDefProps<TData, TValue>;

type CustomColumnHelper<TData extends RowData> = {
  accessor: <
    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,
    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>
      ? TReturn
      : TAccessor extends DeepKeys<TData>
        ? DeepValue<TData, TAccessor>
        : never,
  >(
    accessor: TAccessor,
    column: TAccessor extends AccessorFn<TData> ? DataTableDisplayColumnDef<TData, TValue> : DataTableIdentifiedColumnDef<TData, TValue>,
  ) => TAccessor extends AccessorFn<TData> ? AccessorFnColumnDef<TData, TValue> : DataTableAccessorColumnDef<TData, TValue>;
  display: (column: DataTableDisplayColumnDef<TData>) => DataTableDisplayColumnDef<TData>;
  group: (column: DataTableGroupColumnDef<TData>) => DataTableGroupColumnDef<TData>;
};

type DataTableProps = {
  /** The array of column definitions to use for the table. */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: DataTableColumnDef<any, any>[];
  /** Array of column IDs to pin left */
  pinnedColumns?: string[];
  /** Use this option to optionally pass initial state to the table.
   * Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.
   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable. */
  initialState?: Pick<TableState, 'sorting'>;
  /** The "state" option can be used to optionally _control_ part of the table state.
   * The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table.  */
  state?: Pick<TableState, 'rowSelection'>;
} & TableFieldProps &
  Pick<React.ComponentPropsWithoutRef<'table'>, 'id'> &
  Pick<TableOptions<RowData>, 'data' | 'getRowId' | 'onRowSelectionChange'> &
  CustomTableState;

// Use declaration merging to add our new feature APIs and state types to TanStack Table's existing types.
declare module '@tanstack/react-table' {
  // Override createColumnHelper's return value with our custom ColumnHelper
  function createColumnHelper<TData extends RowData>(): CustomColumnHelper<TData>;
}

// Export for the consumer to use
export { DataTableColumnDef, CustomColumnHelper as DataTableColumnHelper, DataTableProps, DataTableCreateColumnHelper as createColumnHelper };

import styled from 'styled-components';
import { getSpacingVar } from '../spacing';

export const ButtonIcon = styled.span`
  display: flex;
  align-items: center;

  /* Necessary to make iconPosition left buttons vertically aligned with other buttons */
  vertical-align: middle;

  svg {
    height: ${getSpacingVar(6)};
    width: ${getSpacingVar(6)};
  }
`;

export const ButtonLoadingIcon = styled.span`
  display: flex;
  align-items: center;

  /* Necessary to make iconPosition left buttons vertically aligned with other buttons */
  vertical-align: middle;

  svg {
    stroke: currentColor;
  }
`;

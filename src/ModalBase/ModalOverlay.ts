import styled, { keyframes } from 'styled-components';
import { Overlay } from '@radix-ui/react-dialog';

import { getZIndexVar } from '../zIndex';
import { getColorVar } from '../colors';

const fadeIn = keyframes`
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
`;

const fadeOut = keyframes`
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
`;

const ModalOverlay = styled(Overlay)`
  background-color: ${getColorVar('utilityOverlay')};
  left: 0;
  position: fixed;
  top: 0;

  &[data-state='open'] {
    height: 100%;
    width: 100%;
    animation: ${fadeIn} 0.6s ease;
    z-index: ${getZIndexVar('overlay')};
    pointer-events: none;
  }

  &[data-state='closed'] {
    animation: ${fadeOut} 0.6s ease;
  }
`;

export default ModalOverlay;

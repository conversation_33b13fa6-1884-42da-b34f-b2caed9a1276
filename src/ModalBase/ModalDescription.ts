import styled from 'styled-components';

import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';

const ModalDescription = styled.div`
  background: ${getColorVar('utilityBackgroundWhite')};
  padding: ${getSpacingVar(2)} ${getSpacingVar(6)};
  overflow-y: auto;
  line-height: 1.6rem;
  overflow-wrap: anywhere;
  white-space: break-spaces;
`;

export default ModalDescription;

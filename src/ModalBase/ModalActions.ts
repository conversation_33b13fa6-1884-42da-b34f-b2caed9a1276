import styled from 'styled-components';
import media from '../Breakpoints/Breakpoints';
import { getSpacingVar } from '../spacing';

const ModalActions = styled.div`
  display: flex;
  padding: ${getSpacingVar(4)} ${getSpacingVar(6)} ${getSpacingVar(6)} ${getSpacingVar(6)};
  flex-direction: row;
  justify-content: flex-end;
  gap: ${getSpacingVar(4)};

  ${media.extraSmallOnly} {
    flex-direction: column;
    justify-content: flex-end;
    gap: ${getSpacingVar(4)};
  }
`;

export default ModalActions;

import styled, { keyframes } from 'styled-components';
import { Content } from '@radix-ui/react-dialog';
import media from '../Breakpoints/Breakpoints';
import { getSpacingVar } from '../spacing';
import { getZIndexVar } from '../zIndex';

const fadeIn = keyframes`
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
`;

const fadeOut = keyframes`
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
`;

const ModalPositioner = styled(Content)`
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 552px;
  display: flex;
  flex-direction: column;
  max-height: 100%;
  padding: ${getSpacingVar(20)} 0;
  z-index: ${getZIndexVar('overlay')};

  ${media.extraSmallOnly} {
    width: 100%;
    padding: ${getSpacingVar(10)} ${getSpacingVar(4)};
  }

  &[data-state='open'] {
    & > div {
      animation: ${fadeIn} 0.6s ease;
      display: flex;
      flex-direction: column;
    }
  }

  &[data-state='closed'] {
    & > div {
      animation: ${fadeOut} 0.6s ease;
    }
  }
`;

export default ModalPositioner;

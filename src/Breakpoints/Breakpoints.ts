import styled from 'styled-components';

const minimumSupported = 332;
const extraSmallMax = 599;
const smallMin = 600;
const smallMax = 899;
const mediumMin = 900;
const mediumMax = 1279;
const largeMin = 1280;
const largeMax = 1535;
const extraLargeMin = 1536;
export const extraSmallMaxPx = `${extraSmallMax}px`;
const minimumSupportedPx = `${minimumSupported}px`;
const smallMinPx = `${smallMin}px`;
const smallMaxPx = `${smallMax}px`;
const mediumMinPx = `${mediumMin}px`;
const mediumMaxPx = `${mediumMax}px`;
const largeMinPx = `${largeMin}px`;
const largeMaxPx = `${largeMax}px`;
const extraLargeMinPx = `${extraLargeMin}px`;

export const SpHide = styled.div`
  /* stylelint-disable-next-line media-query-no-invalid */
  @media (max-width: ${smallMaxPx}) {
    display: none;
  }
`;
export const PcHide = styled.div`
  /* stylelint-disable-next-line media-query-no-invalid */
  @media (min-width: ${mediumMinPx}) {
    display: none;
  }
`;

const media = {
  minimumSupportedUp: `@media (min-width: ${minimumSupportedPx})`,
  extraSmallOnly: `@media (max-width: ${extraSmallMaxPx})`,
  smallUp: `@media (min-width: ${smallMinPx})`,
  smallOnly: `@media (max-width: ${smallMaxPx})`,
  mediumDown: `@media (max-width: ${mediumMaxPx})`,
  mediumOnly: `@media (min-width: ${mediumMinPx}) and (max-width: ${mediumMaxPx})`,
  mediumUp: `@media (min-width: ${mediumMinPx})`,
  largeOnly: `@media (min-width: ${largeMinPx}) and (max-width: ${largeMaxPx})`,
  largeDown: `@media (max-width: ${largeMaxPx})`,
  largeUp: `@media (min-width: ${largeMinPx})`,
  extraLargeOnly: `@media (min-width: ${extraLargeMinPx})`,
};

export const mediaExported = {
  extraSmallOnly: `@media (max-width: ${extraSmallMaxPx})`,
  smallOnly: `@media (min-width: ${smallMinPx}) and (max-width: ${smallMaxPx})`,
  mediumOnly: `@media (min-width: ${mediumMinPx}) and (max-width: ${mediumMaxPx})`,
  largeOnly: `@media (min-width: ${largeMinPx}) and (max-width: ${largeMaxPx})`,
  extraLargeOnly: `@media (min-width: ${extraLargeMinPx})`,
};

export default media;

export const mediaQueries = [`(max-width: ${smallMaxPx})`, `(min-width: ${mediumMinPx})`, `(min-width: ${largeMinPx})`];

const base = 0;
const above = 1;
const overlay = 1100;
const sticky = above + above;
const header = overlay - above;

const zIndexes = {
  base,
  above,
  overlay,
  sticky,
  header,
};

// ajds for AXA Japan design system
export const cssZIndexVarMap = {
  base: { name: '--ajds-z-index-base', value: base },
  above: { name: '--ajds-z-index-above', value: above },
  overlay: { name: '--ajds-z-index-overlay', value: overlay },
  sticky: { name: '--ajds-z-index-sticky', value: sticky },
  header: { name: '--ajds-z-index-header', value: header },
} as const;

export function getZIndexVar(key: keyof typeof cssZIndexVarMap): string {
  return `var(${(cssZIndexVarMap[key] || {}).name})`;
}

export default zIndexes;

import { getEffectVar } from '../css-effects';

describe('getEffectVar', () => {
  test('it will return the value for a valid effect', () => {
    expect(getEffectVar('transition')).toEqual('var(--ajds-transition)');
  });

  test('it will return the undefined for an invalid effect', () => {
    // @ts-ignore 'not-valid' is not valid
    expect(getEffectVar('not-valid')).toEqual('var(undefined)');
  });
});

import { getTypographyVar } from '../typography';

describe('getColorVar', () => {
  test('it will return the value for a valid color', () => {
    expect(getTypographyVar('defaultFontSize')).toEqual('var(--ajds-font-size-default)');
  });

  test('it will return the undefined for an invalid color', () => {
    // @ts-ignore 'not-valid' is not valid
    expect(getTypographyVar('not-valid')).toEqual('var(undefined)');
  });
});

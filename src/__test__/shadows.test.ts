import { getShadowVar } from '../shadows';

describe('getShadowVar', () => {
  test('it will return the value for a valid shadow', () => {
    expect(getShadowVar('smallShadow')).toEqual('var(--ajds-shadow-small)');
  });

  test('it will return the undefined for an invalid shadow', () => {
    // @ts-ignore 'not-valid' is not valid
    expect(getShadowVar('not-valid')).toEqual('var(undefined)');
  });
});

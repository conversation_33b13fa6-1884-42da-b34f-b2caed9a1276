import styled from 'styled-components';
import { RadioGroup } from '@ark-ui/react';

import { getColorVar } from '../colors';
import input from '../styles/input';

const RadioItem = styled(RadioGroup.Item)`
  ${input.item}

  &:has(:focus-visible) {
    box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
  }

  &[data-disabled] {
    &:has(:focus-visible) {
      box-shadow: initial;
    }
  }

  &[data-invalid] {
    &:has(:focus-visible) {
      box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
    }
  }
`;

export default RadioItem;

import React, { forwardRef } from 'react';
import { RadioGroup } from '@ark-ui/react';
import RadioItem from './RadioItem';
import RadioItemControl from './RadioItemControl';
import RadioItemLabel from './RadioItemLabel';
import EllipseIcon from '../Icons/EllipseIcon';
import pickDataProps, { DataAttributes } from '../utils/pickDataProps';

export type RadioOptions = {
  /** Label text */
  label: string;
} & Pick<React.ComponentPropsWithRef<'input'>, 'disabled' | 'ref' | 'onChange' | 'onBlur' | 'name'> &
  Required<Pick<React.ComponentPropsWithoutRef<'input'>, 'value'>> &
  DataAttributes;

type RadioCommonProps = {
  /** Shows error state. Note: should be passed down from Radio component */
  hasError?: boolean;
} & Pick<React.ComponentPropsWithoutRef<'input'>, 'form'>;

const Radio = forwardRef<HTMLInputElement, RadioOptions & RadioCommonProps>(
  ({ label, disabled = false, hasError = false, form, value, onChange, onBlur, name, ...rest }, ref) => {
    return (
      <RadioItem disabled={disabled} form={form} value={value as string} invalid={hasError} {...pickDataProps(rest)}>
        <RadioItemControl>
          <EllipseIcon size="small" />
        </RadioItemControl>
        {label !== '' && <RadioItemLabel>{label}</RadioItemLabel>}
        <RadioGroup.ItemHiddenInput ref={ref} onChange={onChange} onBlur={onBlur} name={name} />
      </RadioItem>
    );
  },
);

Radio.displayName = 'Radio';

export default React.memo(Radio);

import styled from 'styled-components';
import { RadioGroup } from '@ark-ui/react';
import { getColorVar } from '../colors';
import RadioItem from './RadioItem';
import radio from '../styles/radio';

const RadioItemControl = styled(RadioGroup.ItemControl)`
  ${radio.control}

  &[data-state='checked'] {
    ${RadioItem}:hover > &:not([data-invalid], [data-disabled]) {
      border: 2px solid ${getColorVar('interactiveHoverPrimary')};
      color: ${getColorVar('interactiveHoverPrimary')};
    }
  }
`;

export default RadioItemControl;

import styled from 'styled-components';
import { RadioGroup } from '@ark-ui/react';
import { getColorVar } from '../colors';
import RadioTileItem from './RadioTileItem';
import RadioTileContent from './RadioTileContent';
import radio from '../styles/radio';

const RadioTileControl = styled(RadioGroup.ItemControl)`
  ${radio.control}

  &[data-state='checked'] {
    ${RadioTileItem}:hover > ${RadioTileContent} > &:not([data-invalid], [data-disabled]) {
      border: 2px solid ${getColorVar('interactiveHoverPrimary')};
      color: ${getColorVar('interactiveHoverPrimary')};
    }
  }
`;

export default RadioTileControl;

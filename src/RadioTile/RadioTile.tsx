import React, { forwardRef } from 'react';
import { RadioGroup } from '@ark-ui/react';
import RadioTileItem from './RadioTileItem';
import RadioTileControl from './RadioTileControl';
import RadioTileTitle from './RadioTileTitle';
import RadioTileDescription from './RadioTileDescription';
import EllipseIcon from '../Icons/EllipseIcon';
import RadioTileTextContent from './RadioTileTextContent';
import RadioTileContent from './RadioTileContent';
import pickDataProps, { DataAttributes } from '../utils/pickDataProps';

export type RadioTileOptions = {
  /** title text */
  title: string;
  /** optional description text */
  description?: string;
} & Pick<React.ComponentPropsWithRef<'input'>, 'disabled' | 'ref' | 'onChange' | 'onBlur' | 'name'> &
  Required<Pick<React.ComponentPropsWithoutRef<'input'>, 'value'>> &
  DataAttributes;

type RadioTileCommonProps = {
  /** Shows error state. Note: should be passed down from RadioTileGroup component */
  hasError?: boolean;
} & Pick<React.ComponentPropsWithoutRef<'input'>, 'form'>;

const RadioTile = forwardRef<HTMLInputElement, RadioTileOptions & RadioTileCommonProps>(
  ({ title, description, disabled = false, hasError = false, form, value, onChange, onBlur, name, ...rest }, ref) => {
    return (
      <RadioTileItem disabled={disabled} form={form} value={value as string} invalid={hasError} {...pickDataProps(rest)}>
        <RadioTileContent>
          <RadioTileControl>
            <EllipseIcon size="small" />
          </RadioTileControl>
          <RadioTileTextContent>
            <RadioTileTitle data-part="item-title">{title}</RadioTileTitle>
            {description && <RadioTileDescription data-part="item-description">{description}</RadioTileDescription>}
          </RadioTileTextContent>
          <RadioGroup.ItemHiddenInput ref={ref} onChange={onChange} onBlur={onBlur} name={name} />
        </RadioTileContent>
      </RadioTileItem>
    );
  },
);

RadioTile.displayName = 'RadioTile';

export default React.memo(RadioTile);

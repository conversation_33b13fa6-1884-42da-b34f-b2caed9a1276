import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import sx, { VariantsType } from '../sx';

const variants: VariantsType<'isOpen'> = {
  isOpen: {
    true: {
      transform: 'rotateX(180deg)',
    },
  },
};

const { getSxStyleRules, useSx } = sx('AccordionHeaderIcon', [], variants);

export { useSx };

const AccordionHeaderIcon = styled.span`
  align-items: center;
  display: flex;
  justify-content: center;
  margin-left: ${getSpacingVar(2)};
  margin-top: 0;

  ${getSxStyleRules()}
`;

export default AccordionHeaderIcon;

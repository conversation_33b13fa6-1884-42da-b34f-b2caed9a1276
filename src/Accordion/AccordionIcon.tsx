import React from 'react';
import styled from 'styled-components';

const AccordionSvg = styled.svg`
  min-width: 12px;
  path {
    fill: currentColor;
  }
`;

const AccordionIcon: React.FC<React.ComponentPropsWithoutRef<'svg'>> = (props) => {
  return (
    <AccordionSvg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z" />
    </AccordionSvg>
  );
};

export default AccordionIcon;

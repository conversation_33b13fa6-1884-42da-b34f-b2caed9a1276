import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import buttonElBase from '../styles/buttonElBase';

import sx from '../sx';
import type { VariantsType } from '../sx';

const variants: VariantsType<'size'> = {
  size: {
    small: {
      padding: `${getSpacingVar(3)} ${getSpacingVar(4)}`,
      'font-weight': '400',
      'font-size': '16px',
    },
    large: {
      padding: `${getSpacingVar(5)} ${getSpacingVar(4)}`,
      'font-weight': '600',
      'font-size': '20px',
    },
  },
};

const { getSxStyleRules, useSx } = sx('AccordionHeader', [], variants);

export { useSx };

const AccordionHeader = styled.button`
  ${buttonElBase}

  align-items: center;
  background-color: ${getColorVar('utilityBackgroundWhite')};
  border: none;
  color: ${getColorVar('interactiveActivePrimary')};
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  line-height: 1;
  margin: ${getSpacingVar(0)} ${getSpacingVar('auto')};
  outline: none !important;
  width: 100%;

  @media (hover: hover) {
    &:hover {
      background-color: ${getColorVar('interactiveHoverGreyTransparent')};
      color: ${getColorVar('interactiveHoverPrimary')};
    }
  }

  :focus:not(:active) {
    box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
  }

  ${getSxStyleRules()}
`;

export default AccordionHeader;

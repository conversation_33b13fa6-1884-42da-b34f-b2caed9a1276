### Accordion example:

```js
import Accordion from '@axa-japan/design-system-react/Accordion';

<Accordion headerText="Accordion Header Text">
  <h2>ACCORDION!</h2>
  <p>
    text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
    text text text text text text text text text text text text text text text text text text text
  </p>
  <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
</Accordion>;
```

### Small size accordion example:

```js
import Accordion from '@axa-japan/design-system-react/Accordion';

<Accordion headerText="Accordion Header Text" size="small">
  <h2>ACCORDION!</h2>
  <p>
    text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
    text text text text text text text text text text text text text text text text text text text
  </p>
  <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
</Accordion>;
```

### Large size accordion example:

```js
import Accordion from '@axa-japan/design-system-react/Accordion';

<Accordion headerText="Accordion Header Text" size="large">
  <h2>ACCORDION!</h2>
  <p>Resize me!!!</p>
  <p>
    text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
    text text text text text text text text text text text text text text text text text text text
  </p>
  <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
</Accordion>;
```

### Accordion initially open:

```js
import Accordion from '@axa-japan/design-system-react/Accordion';

<Accordion headerText="Accordion Header Text" isOpenByDefault={true}>
  <h2>ACCORDION (open by default)!</h2>
  <p>
    text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
    text text text text text text text text text text text text text text text text text text text
  </p>
  <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
</Accordion>;
```

### Accordion with controlled open state:

```js
import { useState } from 'react';
import Accordion from '@axa-japan/design-system-react/Accordion';
import Button from '@axa-japan/design-system-react/Button';

const [open, setOpen] = useState(false);

<>
  <Accordion headerText="Accordion Header Text" open={open} onToggle={() => setOpen((current) => !current)}>
    <h2>ACCORDION!</h2>
    <p>
      text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
      text text text text text text text text text text text text text text text text text text text
    </p>
    <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
  </Accordion>
</>;
```

### Accordion with status text:

```js
import Accordion from '@axa-japan/design-system-react/Accordion';

<Accordion headerText="Accordion Header Text" displayStatusText>
  <h2>ACCORDION!</h2>
  <p>
    text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
    text text text text text text text text text text text text text text text text text text text
  </p>
  <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
</Accordion>;
```

### Accordion dynamically re-sizing contents:

```js
import React, { useState } from 'react';
import Accordion from '@axa-japan/design-system-react/Accordion';

const Component = () => {
  const [extraContent, setExtraContent] = useState(null);

  setTimeout(() => {
    setExtraContent(
      <>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </>,
    );
  }, 5000);

  return (
    <Accordion headerText="Accordion Header Text" isOpenByDefault={true}>
      <h2>ACCORDION!</h2>
      <p>
        text text text text text text text text text text text text text text text text text text text text text text text text text text text text
        text text text text text text text text text text text text text text text text text text text text
      </p>
      <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      <p>Wait 5 seconds for the extra content...</p>
      {extraContent}
    </Accordion>
  );
};

<Component />;
```

### How it works

Accordion uses `useAccordionEffect` hook to handle opening and closing.

The Accordion uses CSS transition on `grid-template-rows` property together with `display: grid`. This property, unlike `height` and `max-height`, allows for animated transition without manual height recalculation. Also works fine for dynamic content.

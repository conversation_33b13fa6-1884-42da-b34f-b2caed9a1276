// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Accordion > is closed by default when isOpenByDefault prop === false 1`] = `
.c4 {
  min-width: 12px;
}

.c4 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c3 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c5 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c7 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c7 > *:first-child {
  margin-top: 0;
}

.c7 > *:last-child {
  margin-bottom: 0;
}

.c6 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

<div>
  <div
    class="c0"
    data-testid="accordion-header"
  >
    <button
      aria-controls=":r7:"
      aria-expanded="false"
      class="c1"
      id=":r6:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
      >
        <svg
          class="c4"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c5"
      style="--ajds-AccordionBody-grid-template-rows: 0fr;"
    >
      <div
        class="c6"
      >
        <div
          aria-labelledby=":r6:"
          class="c7"
          id=":r7:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion > is open by default when isOpenByDefault prop === true 1`] = `
.c4 {
  min-width: 12px;
}

.c4 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c3 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c5 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c7 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c7 > *:first-child {
  margin-top: 0;
}

.c7 > *:last-child {
  margin-bottom: 0;
}

.c6 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

<div>
  <div
    class="c0"
    data-testid="accordion-header"
  >
    <button
      aria-controls=":r5:"
      aria-expanded="true"
      class="c1"
      id=":r4:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
        style="--ajds-AccordionHeaderIcon-transform: rotateX(180deg);"
      >
        <svg
          class="c4"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c5"
      style="--ajds-AccordionBody-grid-template-rows: 1fr;"
    >
      <div
        class="c6"
      >
        <div
          aria-labelledby=":r4:"
          class="c7"
          id=":r5:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion > it renders the helper text when displayStatusText is false 1`] = `
.c4 {
  min-width: 12px;
}

.c4 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c3 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c5 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c7 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c7 > *:first-child {
  margin-top: 0;
}

.c7 > *:last-child {
  margin-bottom: 0;
}

.c6 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

<div>
  <div
    class="c0"
  >
    <button
      aria-controls=":rd:"
      aria-expanded="false"
      class="c1"
      id=":rc:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
      >
        <svg
          class="c4"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c5"
      style="--ajds-AccordionBody-grid-template-rows: 0fr;"
    >
      <div
        class="c6"
      >
        <div
          aria-labelledby=":rc:"
          class="c7"
          id=":rd:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion > it renders the helper text when displayStatusText is true 1`] = `
.c5 {
  min-width: 12px;
}

.c5 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c4 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c6 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c8 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c8 > *:first-child {
  margin-top: 0;
}

.c8 > *:last-child {
  margin-bottom: 0;
}

.c3 {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  font-weight: 400;
  line-height: 22px;
  margin-left: var(--ajds-spacing-6);
}

.c7 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

@media (max-width:899px) {
  .c3 {
    font-size: 14px;
  }
}

@media (min-width:900px) {
  .c3 {
    font-size: 16px;
  }
}

<div>
  <div
    class="c0"
  >
    <button
      aria-controls=":rb:"
      aria-expanded="false"
      class="c1"
      id=":ra:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
      >
        開く
      </span>
      <span
        class="c4"
      >
        <svg
          class="c5"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c6"
      style="--ajds-AccordionBody-grid-template-rows: 0fr;"
    >
      <div
        class="c7"
      >
        <div
          aria-labelledby=":ra:"
          class="c8"
          id=":rb:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion > passes down default props for div 1`] = `
.c4 {
  min-width: 12px;
}

.c4 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c3 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c5 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c7 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c7 > *:first-child {
  margin-top: 0;
}

.c7 > *:last-child {
  margin-bottom: 0;
}

.c6 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

<div>
  <div
    class="c0"
    data-testid="TEST"
  >
    <button
      aria-controls=":r9:"
      aria-expanded="false"
      class="c1"
      id=":r8:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
      >
        <svg
          class="c4"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c5"
      style="--ajds-AccordionBody-grid-template-rows: 0fr;"
    >
      <div
        class="c6"
      >
        <div
          aria-labelledby=":r8:"
          class="c7"
          id=":r9:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion > renders without crashing 1`] = `
.c4 {
  min-width: 12px;
}

.c4 path {
  fill: currentColor;
}

.c0 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c1 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c1,
.c1 [type='button'],
.c1 [type='reset'],
.c1 [type='submit'] {
  -webkit-appearance: button;
}

.c1::-moz-focus-inner,
.c1 [type='button']::-moz-focus-inner,
.c1 [type='reset']::-moz-focus-inner,
.c1 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c1:active,
.c1:focus {
  outline: none;
}

.c1:-moz-focusring,
.c1 [type='button']:-moz-focusring,
.c1 [type='reset']:-moz-focusring,
.c1 [type='submit']:-moz-focusring {
  outline: none;
}

.c1:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c2 {
  text-align: left;
  width: 100%;
}

.c3 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c5 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c7 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c7 > *:first-child {
  margin-top: 0;
}

.c7 > *:last-child {
  margin-bottom: 0;
}

.c6 {
  overflow-wrap: break-word;
  overflow: hidden;
}

@media (hover:hover) {
  .c1:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

<div>
  <div
    class="c0"
  >
    <button
      aria-controls=":r1:"
      aria-expanded="false"
      class="c1"
      id=":r0:"
      style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
      type="button"
    >
      <span
        class="c2"
      >
        Accordion Header Text
      </span>
      <span
        class="c3"
      >
        <svg
          class="c4"
          fill="none"
          height="8"
          viewBox="0 0 12 8"
          width="12"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="c5"
      style="--ajds-AccordionBody-grid-template-rows: 0fr;"
    >
      <div
        class="c6"
      >
        <div
          aria-labelledby=":r0:"
          class="c7"
          id=":r1:"
          role="region"
          style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
        >
          <h2>
            ACCORDION!
          </h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

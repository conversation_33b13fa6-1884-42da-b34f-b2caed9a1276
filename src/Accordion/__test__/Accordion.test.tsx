/* eslint-disable class-methods-use-this */
import React, { useState } from 'react';
import { queryByText, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../utils/testUtils';
import Accordion from '../Accordion';

beforeAll(() => {
  global.ResizeObserver = class ResizeObserver {
    observe() {
      // do nothing
    }

    unobserve() {
      // do nothing
    }

    disconnect() {
      // do nothing
    }
  };
});

describe('Accordion', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(
      <Accordion headerText="Accordion Header Text">
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(getByText('Accordion Header Text')).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('opens and closes on click', async () => {
    const { getByTestId } = render(
      <Accordion headerText="Accordion Header Text" data-testid="accordion-header">
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'false');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 0fr;');
    await userEvent.click(getByTestId('accordion-header').children[0]);

    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'true');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 1fr;');

    userEvent.click(getByTestId('accordion-header').children[0]);

    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'false');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 0fr;');
  });

  test('is open by default when isOpenByDefault prop === true', () => {
    const { getByTestId, container } = render(
      <Accordion headerText="Accordion Header Text" data-testid="accordion-header" isOpenByDefault={true}>
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(container).toMatchSnapshot();
    expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'true');
    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 1fr;');
  });

  test('is closed by default when isOpenByDefault prop === false', async () => {
    const { getByTestId, container } = render(
      <Accordion headerText="Accordion Header Text" data-testid="accordion-header" isOpenByDefault={false}>
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(container).toMatchSnapshot();
    expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'false');
    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 0fr;');
  });

  test('passes down default props for div', async () => {
    // test that data-testid gets passed down
    const { getByTestId, container } = render(
      <Accordion headerText="Accordion Header Text" data-testid="TEST">
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(container).toMatchSnapshot();
    await waitFor(() => {
      expect(getByTestId('TEST')).toBeVisible();
    });
  });

  test('it renders the helper text when displayStatusText is true', () => {
    const { getByText, container } = render(
      <Accordion headerText="Accordion Header Text" displayStatusText={true}>
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(getByText('Accordion Header Text')).toBeInTheDocument();
    expect(getByText('開く')).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('it renders the helper text when displayStatusText is false', () => {
    const { getByText, container } = render(
      <Accordion headerText="Accordion Header Text" displayStatusText={false}>
        <h2>ACCORDION!</h2>
        <p>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </p>
        <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
      </Accordion>,
    );

    expect(getByText('Accordion Header Text')).toBeInTheDocument();
    expect(queryByText(container, '開く')).toBe(null);
    expect(container).toMatchSnapshot();
  });

  test('opens and closes on state change with open prop', async () => {
    const Component: React.FC = () => {
      const [open, setOpen] = useState(false);
      return (
        <Accordion headerText="Accordion Header Text" open={open} onToggle={() => setOpen((current) => !current)} data-testid="accordion-header">
          <h2>ACCORDION!</h2>
          <p>
            text text text text text text text text text text text text text text text text text text text text text text text text text text text
            text text text text text text text text text text text text text text text text text text text text text
          </p>
          <p>text text text text text text text text text text text text text text text text text text text text text text text text</p>
        </Accordion>
      );
    };
    const { getByTestId } = render(<Component />);
    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'false');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 0fr;');
    await userEvent.click(getByTestId('accordion-header').children[0]);

    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'true');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 1fr;');

    userEvent.click(getByTestId('accordion-header').children[0]);

    await waitFor(() => {
      expect(getByTestId('accordion-header').children[0]).toHaveAttribute('aria-expanded', 'false');
    });

    expect(getByTestId('accordion-header').children[1]).toHaveAttribute('style', '--ajds-AccordionBody-grid-template-rows: 0fr;');
  });
});

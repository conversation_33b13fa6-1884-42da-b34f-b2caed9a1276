import React from 'react';
import Accordion from '../Accordion';
import Text from '../../Text';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Accordion', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text">
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
    );
  });

  test('SmallAccordion', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text" size="small">
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
    );
  });

  test('SmallAccordionOpen', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text" size="small" isOpenByDefault={true}>
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
      { viewport: { height: 800 } },
    );
  });

  test('DefaultOpen', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text" isOpenByDefault={true}>
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
      { viewport: { height: 800 } },
    );
  });

  test('StatusTextClose', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text" displayStatusText>
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
    );
  });

  test('StatusTextOpen', async ({ task }) => {
    await takeScreenshot(
      <Accordion headerText="Accordion Header Text" displayStatusText isOpenByDefault>
        <Text size="lg">ACCORDION!</Text>
        <Text>
          text text text text text text text text text text text text text text text text text text text text text text text text text text text text
          text text text text text text text text text text text text text text text text text text text text
        </Text>
        <Text>text text text text text text text text text text text text text text text text text text text text text text text text</Text>
      </Accordion>,
      task,
      { viewport: { height: 800 } },
    );
  });
});

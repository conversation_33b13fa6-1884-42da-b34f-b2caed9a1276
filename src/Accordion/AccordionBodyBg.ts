import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import sx, { VariantsType } from '../sx';

const variants: VariantsType<'size'> = {
  size: {
    small: {
      padding: `${getSpacingVar(1)} ${getSpacingVar(4)} ${getSpacingVar(4)}`,
    },
    large: {
      padding: `${getSpacingVar(2)} ${getSpacingVar(4)} ${getSpacingVar(6)}`,
    },
  },
};

const { getSxStyleRules, useSx } = sx('AccordionBodyBg', [], variants);

export { useSx };

const AccordionBodyBg = styled.div`
  & > *:first-child {
    margin-top: 0;
  }

  & > *:last-child {
    margin-bottom: 0;
  }

  background-color: ${getColorVar('utilityBackgroundWhite')};

  ${getSxStyleRules()}
`;

export default AccordionBodyBg;

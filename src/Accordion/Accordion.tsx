import React, { useId } from 'react';
import AccordionIcon from './AccordionIcon';
import useAccordionEffect from '../hooks/useAccordionEffect';
import AccordionBase, { useSx as useBaseSx } from './AccordionBase';
import AccordionHeader, { useSx as useHeaderSx } from './AccordionHeader';
import AccordionHeaderTitle from './AccordionHeaderTitle';
import AccordionHeaderIcon, { useSx as useHeaderIconSx } from './AccordionHeaderIcon';
import AccordionBody, { useSx as useBodySx } from './AccordionBody';
import AccordionBodyBg, { useSx as useBodyBgSx } from './AccordionBodyBg';
import AccordionStatusText from './AccordionStatusText';
import type { MarginSxPropType } from '../sx';
import AccordionBodyWrapper from './AccordionBodyWrapper';

export type AccordionProps = {
  /** Text for the accordion header */
  headerText: string;
  /** Initial state of the accordion. Defaults to false */
  isOpenByDefault?: boolean;
  /** Whether the accordion is open (controlled) */
  open?: boolean;
  /** Function triggered when toggling accordion */
  onToggle?: () => void;
  /** Controls whether to display '閉じる' | '開く' next to the button */
  displayStatusText?: boolean;
  /** Sets the size of the accordion */
  size?: 'small' | 'large';
  /** Style overrides */
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'div'>;

const Accordion: React.FC<AccordionProps> = ({
  headerText,
  isOpenByDefault = false,
  open,
  onToggle,
  children,
  sx,
  displayStatusText = false,
  size = 'large',
  ...rest
}) => {
  const buttonId = useId();
  const bodyId = useId();
  const { isOpen, toggleOpen } = useAccordionEffect({
    isOpenByDefault,
    open,
    onToggle,
  });

  return (
    <AccordionBase {...rest} style={useBaseSx(sx)}>
      <AccordionHeader
        aria-expanded={isOpen}
        onClick={toggleOpen}
        type="button"
        aria-controls={bodyId}
        id={buttonId}
        style={useHeaderSx({}, { size })}
      >
        <AccordionHeaderTitle>{headerText}</AccordionHeaderTitle>
        {displayStatusText && <AccordionStatusText>{isOpen ? '閉じる' : '開く'}</AccordionStatusText>}
        <AccordionHeaderIcon style={useHeaderIconSx({}, { isOpen: String(isOpen) })}>
          <AccordionIcon />
        </AccordionHeaderIcon>
      </AccordionHeader>
      <AccordionBody style={useBodySx({}, { isOpen: String(isOpen) })}>
        <AccordionBodyWrapper>
          <AccordionBodyBg role="region" aria-labelledby={buttonId} id={bodyId} style={useBodyBgSx({}, { size })}>
            {children}
          </AccordionBodyBg>
        </AccordionBodyWrapper>
      </AccordionBody>
    </AccordionBase>
  );
};

export default Accordion;

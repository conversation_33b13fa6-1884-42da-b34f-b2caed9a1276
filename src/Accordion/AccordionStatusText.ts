import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import media from '../Breakpoints/Breakpoints';

const AccordionStatusText = styled.span`
  flex-shrink: 0;
  font-weight: 400;
  line-height: 22px;
  margin-left: ${getSpacingVar(6)};

  ${media.smallOnly} {
    font-size: 14px;
  }

  ${media.mediumUp} {
    font-size: 16px;
  }
`;

export default AccordionStatusText;

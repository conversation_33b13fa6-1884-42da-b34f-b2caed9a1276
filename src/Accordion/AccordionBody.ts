import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';

const variants: VariantsType<'isOpen'> = {
  isOpen: {
    true: {
      'grid-template-rows': '1fr',
    },
    false: {
      'grid-template-rows': '0fr',
    },
  },
};

const { getSxStyleRules, useSx } = sx('AccordionBody', [], variants);

export { useSx };

const AccordionBody = styled.div`
  display: grid;
  transition: grid-template-rows 0.3s ease-in-out;
  ${getSxStyleRules()};
`;

export default AccordionBody;

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FieldValidationError > renders without crashing 1`] = `
.c0 {
  -webkit-align-self: flex-start;
  -ms-flex-item-align: start;
  align-self: flex-start;
  color: var(--ajds-color-status-danger);
  font-size: 14px;
  margin-bottom: 0;
  white-space: break-spaces;
  width: 100%;
  max-width: calc(100% - 26px);
  margin-top: var(--ajds-FieldValidationErrorBase-margin-top);
  max-height: var(--ajds-FieldValidationErrorBase-max-height);
  opacity: var(--ajds-FieldValidationErrorBase-opacity);
}

<div>
  <p
    class="c0"
    role="alert"
    style="--ajds-FieldValidationErrorBase-margin-top: 5px; --ajds-FieldValidationErrorBase-max-height: 32px; --ajds-FieldValidationErrorBase-opacity: 1;"
  >
    Field Validation Error
  </p>
</div>
`;

exports[`FieldValidationError > renders without crashing with showError false 1`] = `
.c0 {
  -webkit-align-self: flex-start;
  -ms-flex-item-align: start;
  align-self: flex-start;
  color: var(--ajds-color-status-danger);
  font-size: 14px;
  margin-bottom: 0;
  white-space: break-spaces;
  width: 100%;
  max-width: calc(100% - 26px);
  margin-top: var(--ajds-FieldValidationErrorBase-margin-top);
  max-height: var(--ajds-FieldValidationErrorBase-max-height);
  opacity: var(--ajds-FieldValidationErrorBase-opacity);
}

<div>
  <p
    class="c0"
    role="alert"
    style="--ajds-FieldValidationErrorBase-margin-top: 0; --ajds-FieldValidationErrorBase-max-height: 0; --ajds-FieldValidationErrorBase-opacity: 0;"
  >
    Field Validation Error
  </p>
</div>
`;

exports[`FieldValidationError > renders without crashing with showError true 1`] = `
.c0 {
  -webkit-align-self: flex-start;
  -ms-flex-item-align: start;
  align-self: flex-start;
  color: var(--ajds-color-status-danger);
  font-size: 14px;
  margin-bottom: 0;
  white-space: break-spaces;
  width: 100%;
  max-width: calc(100% - 26px);
  margin-top: var(--ajds-FieldValidationErrorBase-margin-top);
  max-height: var(--ajds-FieldValidationErrorBase-max-height);
  opacity: var(--ajds-FieldValidationErrorBase-opacity);
}

<div>
  <p
    class="c0"
    role="alert"
    style="--ajds-FieldValidationErrorBase-margin-top: 5px; --ajds-FieldValidationErrorBase-max-height: 32px; --ajds-FieldValidationErrorBase-opacity: 1;"
  >
    Field Validation Error
  </p>
</div>
`;

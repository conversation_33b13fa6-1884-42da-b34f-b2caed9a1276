import React from 'react';
import { render } from '../../utils/testUtils';
import FieldValidationError from '../FieldValidationError';

describe('FieldValidationError', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<FieldValidationError hasError={true} errorMessage="Field Validation Error" />);
    const testElement = getByText('Field Validation Error');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with showError ${condition}`, () => {
      const { getByText, container } = render(
        <FieldValidationError errorMessage="Field Validation Error" hasError={condition}>
          Field Validation Error
        </FieldValidationError>,
      );
      const testElement = getByText('Field Validation Error');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

import styled from 'styled-components';
import sx, { VariantsType } from '../sx';
import { getColorVar } from '../colors';

const variants: VariantsType<'showError'> = {
  showError: {
    false: {
      'margin-top': '0',
      'max-height': '0',
      opacity: '0',
    },
    true: {
      'margin-top': '5px',
      'max-height': '32px',
      opacity: '1',
    },
  },
};

const { useSx, getSxStyleRules } = sx('FieldValidationErrorBase', [], variants);

export { useSx };

export const FieldValidationErrorBase = styled.p`
  align-self: flex-start;
  color: ${getColorVar('statusDanger')};
  font-size: 14px;
  margin-bottom: 0;
  white-space: break-spaces;
  width: 100%;
  max-width: calc(100% - 26px);

  ${getSxStyleRules()}
`;

export default FieldValidationErrorBase;

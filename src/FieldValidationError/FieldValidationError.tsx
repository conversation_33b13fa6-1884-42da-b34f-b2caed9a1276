import React from 'react';
import { FieldValidationErrorBase, useSx } from './FieldValidationErrorBase';

export type FieldValidationErrorProps = {
  id?: string;
  errorMessage?: string;
  hasError: boolean;
} & React.ComponentPropsWithoutRef<'p'>;

const FieldValidationError: React.FC<FieldValidationErrorProps> = ({ id, errorMessage, hasError, ...rest }) => {
  return (
    <FieldValidationErrorBase id={id} role="alert" {...rest} style={useSx({}, { showError: String(hasError) })}>
      {errorMessage || ''}
    </FieldValidationErrorBase>
  );
};

export default React.memo(FieldValidationError);

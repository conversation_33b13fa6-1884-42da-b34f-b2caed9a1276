import styled, { css } from 'styled-components';
import { Link } from 'react-router-dom';
import media from '../Breakpoints/Breakpoints';

const getHeaderLogoStyles = () => css`
  display: inline-block;
  flex-shrink: 0;
  text-decoration: none;
  width: auto;

  ${media.smallOnly} {
    height: 35px;
  }

  ${media.mediumOnly} {
    height: 45px;
  }

  ${media.largeUp} {
    height: 55px;
  }
`;

export const HeaderLogoAnchor = styled.a`
  ${getHeaderLogoStyles()}

  svg,
  img {
    ${getHeaderLogoStyles()}
  }
`;

export const HeaderLogoLink = styled(Link)`
  ${getHeaderLogoStyles()}

  svg,
  img {
    ${getHeaderLogoStyles()}
  }
`;

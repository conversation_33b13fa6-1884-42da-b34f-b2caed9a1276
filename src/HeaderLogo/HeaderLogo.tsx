import React from 'react';
import { LinkProps } from 'react-router-dom';
import A<PERSON><PERSON><PERSON> from './AxaLogo';
import { HeaderLogoAnchor, HeaderLogoLink } from './HeaderLogo.styles';
import LinkBase from '../OldLinkBase/OldLinkBase';

export type HeaderLogoProps = {
  /** URL for where you want the header logo link to go to */
  to: string;
  /** Custom image for header logo */
  logo?: React.ReactElement;
  /** Choose to use react router */
  useRouter?: boolean;
} & (React.ComponentPropsWithoutRef<'a'> | LinkProps);

const HeaderLogo: React.FC<React.PropsWithChildren<HeaderLogoProps>> = (props) => {
  const { logo, ...rest } = props;
  return (
    <LinkBase linkComponent={HeaderLogoLink} anchorComponent={HeaderLogoAnchor} {...rest}>
      {logo || <AxaLogo />}
    </LinkBase>
  );
};

export default HeaderLogo;

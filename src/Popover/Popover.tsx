import { Popover as ArkPopover, PopoverRootProps } from '@ark-ui/react';
import React, { forwardRef, useState } from 'react';
import {
  ArkPopoverArrow,
  ArkPopoverArrowTip,
  ArkPopoverContent,
  PopoverActionAreaContainer,
  PopoverBodyContainer,
  PopoverDescription,
  PopoverTextContainer,
  PopoverTitle,
} from './Popover.styles';
import CloseButton from '../CloseButton';
import Button from '../Button';

type PopoverButtonProps = {
  /** Button text to display */
  text: string;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color'>;

export type PopoverProps = {
  /** Popover can wrap any other component, even plaintext */
  children: React.ReactNode;
  /** Popover description */
  description: string;
  /** Popover optional title */
  title?: string;
  /** List of 0-2 popover button props */
  buttons?: [PopoverButtonProps] | [PopoverButtonProps, PopoverButtonProps];
  /** Control popover placement */
  placement?: 'top' | 'bottom';
} & Pick<PopoverRootProps, 'id' | 'onExitComplete' | 'onOpenChange' | 'open'> &
  React.RefAttributes<HTMLDivElement>;

const Popover = forwardRef<HTMLDivElement, PopoverProps>(
  ({ children, description, title, buttons = [], placement = 'top', open = false, ...rest }, ref) => {
    const [isOpen, setIsOpen] = useState(open);
    const showButton = Boolean(buttons.length);
    return (
      <ArkPopover.Root
        open={isOpen}
        positioning={{ placement }}
        onEscapeKeyDown={() => setIsOpen(false)}
        onFocusOutside={() => setIsOpen(false)}
        onInteractOutside={() => setIsOpen(false)}
        onPointerDownOutside={() => setIsOpen(false)}
        {...rest}
      >
        <ArkPopover.Trigger asChild onClick={() => setIsOpen((prev) => !prev)}>
          {children}
        </ArkPopover.Trigger>
        <ArkPopover.Positioner>
          <ArkPopoverContent data-testid="popover-content" ref={ref}>
            <ArkPopoverArrow data-testid="popover-arrow">
              <ArkPopoverArrowTip />
            </ArkPopoverArrow>
            <PopoverBodyContainer>
              <PopoverTextContainer>
                {title && <PopoverTitle data-testid="popover-title">{title}</PopoverTitle>}
                <PopoverDescription>{description}</PopoverDescription>
              </PopoverTextContainer>
              <CloseButton aria-label="close-button" data-testid="popover-close-button" color="white" onClick={() => setIsOpen(false)} />
            </PopoverBodyContainer>
            {showButton && (
              <PopoverActionAreaContainer data-testid="popover-button-wrapper">
                {buttons[0] && (
                  <Button variant="outlined" color="white" data-testid="popover-outline-button" {...buttons[0]}>
                    {buttons[0].text}
                  </Button>
                )}
                {buttons[1] && (
                  <Button variant="text" color="white" data-testid="popover-text-button" {...buttons[1]}>
                    {buttons[1].text}
                  </Button>
                )}
              </PopoverActionAreaContainer>
            )}
          </ArkPopoverContent>
        </ArkPopover.Positioner>
      </ArkPopover.Root>
    );
  },
);

Popover.displayName = 'Popover';

export default Popover;

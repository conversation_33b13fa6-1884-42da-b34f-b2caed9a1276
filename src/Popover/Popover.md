- Use popovers to provide more details, like describing the value of a feature
- Use popovers to introduce and explain new features on various parent elements.

### Default Popover:

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover description="This is the text of the popover">
  <Button>example</Button>
</Popover>;
```

### With bottom placement:

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover description="This is the text of the popover this is the text of the popover" placement={'bottom'}>
  <Button>example</Button>
</Popover>;
```

### With title:

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover title="Title of Popover" description="This is the text of the popover">
  <Button>example</Button>
</Popover>;
```

### With title and one button:

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover
  title="Title of Popover"
  description="This is the text of the popover"
  buttons={[{ text: 'Outline Btn', onClick: () => console.log('Outline button clicked') }]}
>
  <Button>example</Button>
</Popover>;
```

### With title and two buttons:

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover
  title="Title of Popover"
  description="This is the text of the popover"
  buttons={[
    { text: 'Outline Btn', onClick: () => console.log('Outline button clicked') },
    { text: 'Text Btn', onClick: () => console.log('Text button clicked') },
  ]}
>
  <Button>example</Button>
</Popover>;
```

### With popover opened by default

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover title="Title of Popover" description="This is the text of the popover" open={true}>
  <Button>example</Button>
</Popover>;
```

### Responding to events

```js
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

<Popover
  title="Title of Popover"
  description="This is the text of the popover"
  onExitComplete={() => console.log('exit!')}
  onOpenChange={(details) => console.log(details)}
>
  <Button>example</Button>
</Popover>;
```

### Exposing Ref

A ref to the Popover's contents is exposed.

```js
import React, { useRef } from 'react';
import Button from '@axa-japan/design-system-react/Button';
import Popover from '@axa-japan/design-system-react/Popover';

const ref = useRef(null);
<Popover
  ref={ref}
  title="Title of Popover"
  description="This is the text of the popover"
  onExitComplete={() => console.log('exit!')}
  onOpenChange={(details) => console.log(details)}
>
  <Button>example</Button>
</Popover>;
```

import React from 'react';
import Popover from '../Popover';
import Button from '../../Button';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Popover', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(
      <Popover description="Default Example Default Example Default Example Default Example Default Example Default Example Default Example Default Example ">
        <Button id="trigger">test</Button>
      </Popover>,
      task,
      {
        interactionSelector: '#trigger',
        interactionType: 'click',
      },
    );
  });

  test('WithTitle', async ({ task }) => {
    await takeScreenshot(
      <Popover description="example example example example" title="title only">
        <Button id="trigger">test</Button>
      </Popover>,
      task,
      {
        interactionSelector: '#trigger',
        interactionType: 'click',
      },
    );
  });

  test('WithTitleAndOneButton', async ({ task }) => {
    await takeScreenshot(
      <Popover description="example example example example" title="title and one button" buttons={[{ text: 'Button 1' }]}>
        <Button id="trigger">test</Button>
      </Popover>,
      task,
      {
        interactionSelector: '#trigger',
        interactionType: 'click',
      },
    );
  });

  test('WithTitleAndTwoButtons', async ({ task }) => {
    await takeScreenshot(
      <Popover description="example example example example" title="title and two buttons" buttons={[{ text: 'Button 1' }, { text: 'Button 2' }]}>
        <Button id="trigger">test</Button>
      </Popover>,
      task,
      {
        interactionSelector: '#trigger',
        interactionType: 'click',
      },
    );
  });
});

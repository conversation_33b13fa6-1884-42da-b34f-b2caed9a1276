import React from 'react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import Popover from '../Popover';
import Button from '../../Button';

describe('Popover', () => {
  // basic render test
  test('renders without crashing', async () => {
    const { queryByTestId, getByTestId, container } = render(
      <Popover description="text of popover">
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    expect(queryByTestId('popover-title')).not.toBeInTheDocument();
    expect(queryByTestId('popover-outline-button')).not.toBeInTheDocument();
    expect(queryByTestId('popover-text-button')).not.toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  // test for the title rendering
  test('title of popover is rendered properly', async () => {
    const { getByTestId, queryByTestId } = render(
      <Popover description="text of popover" title="title of popover">
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    const popoverTitle = getByTestId('popover-title');
    expect(popoverTitle).toBeInTheDocument();
    expect(popoverTitle).toHaveTextContent('title of popover');
    expect(queryByTestId('popover-outline-button')).not.toBeInTheDocument();
    expect(queryByTestId('popover-text-button')).not.toBeInTheDocument();
  });

  test('renders popover content when trigger is clicked', async () => {
    const { getByTestId, getByText } = render(
      <Popover description="text of popover">
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    const popoverContent = getByText(/text of popover/i);
    expect(popoverContent).toBeInTheDocument();
  });

  test('popover should be hidden after close button is clicked', async () => {
    const { getByTestId } = render(
      <Popover description="text of popover">
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    const closeButton = getByTestId('popover-close-button');
    expect(closeButton).toBeInTheDocument();
    await userEvent.click(closeButton);

    await waitFor(() => {
      const popoverContent = getByTestId('popover-content');
      expect(popoverContent).toBeInTheDocument();
      expect(popoverContent).toHaveAttribute('hidden');
    });
  });

  test('calls 1st button onclick when outline button is clicked', async () => {
    const mockOnClick = vi.fn();
    const { getByTestId, queryByTestId } = render(
      <Popover description="text of popover" buttons={[{ text: 'Outline Button', onClick: mockOnClick }]}>
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    const outlineButton = getByTestId('popover-outline-button');
    await userEvent.click(outlineButton);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
    expect(queryByTestId('popover-title')).not.toBeInTheDocument();
    expect(getByTestId('popover-outline-button')).toBeInTheDocument();
    expect(queryByTestId('popover-text-button')).not.toBeInTheDocument();
  });

  test('calls 2nd button onclick when text button is clicked', async () => {
    const mockOnClick = vi.fn();
    const { getByTestId, queryByTestId } = render(
      <Popover description="text of popover" buttons={[{ text: 'Outline Button' }, { text: 'Text Button', onClick: mockOnClick }]}>
        <Button data-testid="trigger">test</Button>
      </Popover>,
    );
    const triggerElement = getByTestId('trigger');
    expect(triggerElement).toBeInTheDocument();
    await userEvent.click(triggerElement);

    const textButton = getByTestId('popover-text-button');
    await userEvent.click(textButton);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
    expect(queryByTestId('popover-title')).not.toBeInTheDocument();
    expect(queryByTestId('popover-outline-button')).toBeInTheDocument();
    expect(getByTestId('popover-text-button')).toBeInTheDocument();
  });
});

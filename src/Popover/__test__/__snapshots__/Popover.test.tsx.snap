// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Popover > renders without crashing 1`] = `
.c2 {
  background-color: var(--ajds-color-utility-background-ocean);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-sm);
  padding-left: var(--ajds-spacing-4);
  max-width: min(var(--ajds-spacing-96),100vw - var(--ajds-spacing-4));
  outline: none;
  z-index: 50;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: var(--ajds-spacing-1);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-3) 0;
  gap: var(--ajds-spacing-1);
}

.c7 {
  overflow-wrap: 'anywhere';
  max-width: 100vw;
  word-break: break-all;
}

.c3 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c4 {
  --arrow-background: var(--ajds-color-utility-background-ocean);
}

.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-controls="popover::r0::content"
    aria-expanded="true"
    aria-haspopup="dialog"
    aria-label="test"
    class="c0"
    data-part="trigger"
    data-placement="top"
    data-scope="popover"
    data-state="open"
    data-testid="trigger"
    dir="ltr"
    id="popover::r0::trigger"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      test
    </span>
  </button>
  <div
    data-part="positioner"
    data-scope="popover"
    dir="ltr"
    id="popover::r0::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(var(--x), var(--y), 0); z-index: var(--z-index); --transform-origin: bottom center; --reference-width: 0px; --available-width: -16px; --available-height: -16px; --x: 0px; --y: -8px; --z-index: 50;"
  >
    <div
      aria-describedby="popover::r0::desc"
      class="c2"
      data-expanded=""
      data-part="content"
      data-placement="top"
      data-scope="popover"
      data-state="open"
      data-testid="popover-content"
      dir="ltr"
      id="popover::r0::content"
      role="dialog"
      tabindex="-1"
    >
      <div
        class="c3"
        data-part="arrow"
        data-scope="popover"
        data-testid="popover-arrow"
        dir="ltr"
        id="popover::r0::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1); left: 0px; top: calc(100% + var(--arrow-offset));"
      >
        <div
          class="c4"
          data-part="arrow-tip"
          data-scope="popover"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit; transform: rotate(225deg);"
        />
      </div>
      <div
        class="c5"
      >
        <div
          class="c6"
        >
          <div
            class="c7"
            data-part="description"
            data-scope="popover"
            dir="ltr"
            id="popover::r0::desc"
          >
            text of popover
          </div>
        </div>
        <button
          aria-label="close-button"
          class="c8"
          data-testid="popover-close-button"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c9"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c10"
            >
              <svg
                class="c11"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
                />
              </svg>
            </span>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>
`;

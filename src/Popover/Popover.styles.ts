import { Popover as ArkPopover, PopoverDescription as ArkDescription } from '@ark-ui/react';
import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

const backgroundColor = getColorVar('utilityBackgroundOcean');
const textColor = getColorVar('characterPrimaryWhite');

export const ArkPopoverContent = styled(ArkPopover.Content)`
  background-color: ${backgroundColor};
  color: ${textColor};
  border-radius: ${getRadiusVar('sm')};
  padding-left: ${getSpacingVar(4)};
  max-width: min(${getSpacingVar(96)}, 100vw - ${getSpacingVar(4)});
  outline: none;
  z-index: 50;
`;

export const PopoverBodyContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: ${getSpacingVar(1)};
`;

export const PopoverTextContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: ${getSpacingVar(3)} 0;
  gap: ${getSpacingVar(1)};
`;

export const PopoverTitle = styled.div`
  font-weight: ${getTypographyVar('boldFontWeight')};
  font-size: ${getTypographyVar('defaultFontSize')};
  line-height: ${getTypographyVar('lineHeight')};
  word-break: break-all;
`;

export const PopoverDescription = styled(ArkDescription)`
  overflow-wrap: 'anywhere';
  max-width: 100vw;
  word-break: break-all;
`;

export const PopoverActionAreaContainer = styled.div`
  padding: 0 ${getSpacingVar(4)} ${getSpacingVar(4)} 0;
  gap: ${getSpacingVar(2)};
  display: flex;
  flex-wrap: wrap;
`;

export const ArkPopoverArrow = styled(ArkPopover.Arrow)`
  /* stylelint-disable-next-line arrow-size-empty-line-before */
  --arrow-size: ${getSpacingVar(1.5)};
`;

export const ArkPopoverArrowTip = styled(ArkPopover.ArrowTip)`
  /* stylelint-disable-next-line arrow-background-empty-line-before */
  --arrow-background: ${backgroundColor};
`;

import styled from 'styled-components';
import { Checkbox } from '@ark-ui/react';
import { getColorVar } from '../colors';
import CheckboxTileRoot from './CheckboxTileRoot';
import CheckboxTileContent from './CheckboxTileContent';
import checkbox from '../styles/checkbox';

const CheckboxTileControl = styled(Checkbox.Control)`
  ${checkbox.control}

  &[data-state='checked'] {
    ${CheckboxTileRoot}:hover > ${CheckboxTileContent} > &:not([data-invalid], [data-disabled]) {
      border: 2px solid ${getColorVar('interactiveHoverPrimary')};
      background-color: ${getColorVar('interactiveHoverPrimary')};
    }
  }
`;

export default CheckboxTileControl;

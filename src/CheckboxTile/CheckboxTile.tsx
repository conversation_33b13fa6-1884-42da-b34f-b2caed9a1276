import React, { forwardRef } from 'react';
import { Checkbox, CheckboxRootBaseProps } from '@ark-ui/react';
import CheckboxTileRoot from './CheckboxTileRoot';
import CheckboxTileControl from './CheckboxTileControl';
import CheckboxTileTitle from './CheckboxTileTitle';
import CheckboxTileDescription from './CheckboxTileDescription';
import { CheckIcon } from '../Icons';
import CheckboxTileTextContent from './CheckboxTileTextContent';
import CheckboxTileContent from './CheckboxTileContent';

export type CheckboxTileOptions = {
  /** title text */
  title: string;
  /** optional description text */
  description?: string;
  /** For react testing library tests */
  'data-testid'?: string;
} & Pick<React.ComponentPropsWithRef<'input'>, 'id' | 'ref' | 'onChange' | 'onBlur' | 'name'> &
  Required<Pick<CheckboxRootBaseProps, 'value'>>;

type CheckboxTileCommonProps = {
  /** Shows error state. Note: should be passed down from CheckboxTileGroup component */
  hasError?: boolean;
} & Pick<CheckboxRootBaseProps, 'form' | 'checked' | 'defaultChecked' | 'onCheckedChange'>;

const CheckboxTile = forwardRef<HTMLInputElement, CheckboxTileOptions & CheckboxTileCommonProps>(
  ({ title, description, onChange, onBlur, name, hasError = false, ...rest }, ref) => {
    return (
      <CheckboxTileRoot invalid={hasError} {...rest}>
        <CheckboxTileContent>
          <CheckboxTileControl>
            <CheckIcon size="small" isStroke />
          </CheckboxTileControl>
          <CheckboxTileTextContent>
            <CheckboxTileTitle data-part="item-title">{title}</CheckboxTileTitle>
            {description && <CheckboxTileDescription data-part="item-description">{description}</CheckboxTileDescription>}
          </CheckboxTileTextContent>
          <Checkbox.HiddenInput ref={ref} onBlur={onBlur} onChange={onChange} name={name} />
        </CheckboxTileContent>
      </CheckboxTileRoot>
    );
  },
);

CheckboxTile.displayName = 'CheckboxTile';

export default React.memo(CheckboxTile);

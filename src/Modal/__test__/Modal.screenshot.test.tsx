import React from 'react';
import Modal from '../Modal';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Modal', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(
      <Modal
        id="default-modal"
        open={true}
        setOpen={() => {}}
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Close',
          },
        ]}
      />,
      task,
      {
        delay: true,
      },
    );
  });

  test('WithLongTitle', async ({ task }) => {
    await takeScreenshot(
      <Modal
        id="long-title-modal"
        open={true}
        setOpen={() => {}}
        title="long title long title long title long title long title"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Close',
          },
        ]}
      />,
      task,
      {
        delay: true,
      },
    );
  });

  test('WithTwoButtons', async ({ task }) => {
    await takeScreenshot(
      <Modal
        id="default-modal"
        open={true}
        setOpen={() => {}}
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Submit',
          },
          {
            text: 'Close',
          },
        ]}
      />,
      task,
      {
        delay: true,
      },
    );
  });

  test('NoShow', async ({ task }) => {
    await takeScreenshot(
      <Modal
        id="close-modal"
        open={false}
        setOpen={() => {}}
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Submit',
          },
          {
            text: 'Close',
          },
        ]}
      />,
      task,
    );
  });
});

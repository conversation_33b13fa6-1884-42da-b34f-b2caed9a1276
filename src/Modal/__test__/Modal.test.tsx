import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import Button from '../../Button';
import { render } from '../../utils/testUtils';
import Modal from '../Modal';

describe('Modal', () => {
  test('renders without crashing (controlled)', async () => {
    const Component: React.FC = () => {
      const [isOpen, setIsOpen] = useState(false);

      const onClick = () => setIsOpen((currentValue) => !currentValue);

      return (
        <Modal
          id="default-modal"
          open={isOpen}
          setOpen={setIsOpen}
          title="Default Modal"
          content="Content for the Modal"
          buttons={[
            {
              text: 'Close',
            },
          ]}
        >
          <Button variant="filled" color="blue" onClick={onClick}>
            Modal Trigger
          </Button>
        </Modal>
      );
    };

    const { getByText, getByTestId, baseElement } = render(<Component />);

    // open modal and test the close modal icon button
    await userEvent.click(getByText('Modal Trigger'));

    expect(getByText('Content for the Modal')).toBeInTheDocument();
    expect(getByText('Default Modal')).toBeInTheDocument();
    expect(getByText('Close')).toBeInTheDocument();
    expect(baseElement).toMatchSnapshot();

    const modal = getByTestId('modal-positioner');

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByTestId('close-modal-icon-button'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });
  });

  test('renders without crashing (uncontrolled)', async () => {
    const { getByText, getByTestId, baseElement } = render(
      <Modal
        id="default-modal"
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Close Button',
            closeModalOnClick: true,
          },
        ]}
      >
        <Button variant="filled" color="blue">
          Modal Trigger
        </Button>
      </Modal>,
    );

    // open modal and test the close modal icon button
    await userEvent.click(getByText('Modal Trigger'));

    let modal = getByTestId('modal-positioner');
    expect(getByText('Content for the Modal')).toBeInTheDocument();
    expect(getByText('Default Modal')).toBeInTheDocument();
    expect(getByText('Close Button')).toBeInTheDocument();
    expect(baseElement).toMatchSnapshot();

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByTestId('close-modal-icon-button'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });

    // open modal and test the closeModalOnClick prop on close action button
    await userEvent.click(getByText('Modal Trigger'));

    modal = getByTestId('modal-positioner');

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    await userEvent.click(getByText('Close Button'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });
  });

  test('renders with two buttons', async () => {
    const Component: React.FC = () => {
      const [isOpen, setIsOpen] = useState(false);

      const onClick = () => setIsOpen((currentValue) => !currentValue);

      return (
        <Modal
          id="modal-with-buttons"
          open={isOpen}
          setOpen={setIsOpen}
          title="Default Modal"
          content="Content for the Modal"
          buttons={[
            {
              text: 'Submit',
            },
            {
              text: 'Close',
            },
          ]}
        >
          <Button variant="filled" color="blue" onClick={onClick}>
            Modal Trigger
          </Button>
        </Modal>
      );
    };

    const { getByText, getByTestId, container } = render(<Component />);

    // Open modal by clicking trigger
    await userEvent.click(getByText('Modal Trigger'));

    expect(getByText('Content for the Modal')).toBeInTheDocument();
    expect(getByText('Default Modal')).toBeInTheDocument();
    expect(getByText('Close')).toBeInTheDocument();
    expect(getByText('Submit')).toBeInTheDocument();
    expect(container).toMatchSnapshot();

    const modal = getByTestId('modal-positioner');

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'open');
    });

    // Close modal by clicking close icon
    await userEvent.click(getByTestId('close-modal-icon-button'));

    await waitFor(() => {
      expect(modal).toHaveAttribute('data-state', 'closed');
    });
  });

  test('does not render when open is false', () => {
    const { queryByText, container } = render(
      <Modal
        id="close-modal"
        open={false}
        setOpen={() => {}}
        title="Default Modal"
        content="Content for the Modal"
        buttons={[
          {
            text: 'Submit',
          },
          {
            text: 'Close',
          },
        ]}
      />,
    );
    expect(queryByText('Content for the Modal')).not.toBeInTheDocument();
    expect(queryByText('Default Modal')).not.toBeInTheDocument();
    expect(queryByText('Close')).not.toBeInTheDocument();
    expect(queryByText('Submit')).not.toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

import styled from 'styled-components';
import { Title } from '@radix-ui/react-dialog';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';

const ModalTitle = styled(Title)`
  overflow-wrap: anywhere;
  padding: ${getSpacingVar(3)} ${getSpacingVar(0)} ${getSpacingVar(2)} ${getSpacingVar(0)};
  font-weight: ${getTypographyVar('boldFontWeight')};
  font-size: ${getTypographyVar('xlFontSize')};
`;

export default ModalTitle;

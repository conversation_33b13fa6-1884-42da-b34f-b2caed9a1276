Note: In order to use the Modal component, you need to create your own trigger to open the Modal, pass it as _children_ to the modal, and handle the logic yourself.

You can also use the modal without a passing any trigger to _children_. This is useful if you want to trigger the modal depending on specific conditions, like on page load.

You can also define 1-2 action buttons for the Modal and handle the logic yourself. And also add unique id to your all modal use in project.

Use modals sparingly and for critical tasks that require completion or acknowledgment, rather than passive notices or information.

We also have another modal component, the [StatusModal](https://axa-japan-design-system.axa.co.jp/#/Overlay/StatusModal), unlike general modals, are used for actions that may directly affect the user.

### Default Modal (Controlled):

- _setOpen_: When _Controlled_, the _setOpen_ prop passed will be used by us to handle the close icon button's ability to close the modal, as well as during actions such as _onEscapeKeyDown_,_onPointerDownOutside_, _onInteractOutside_.
- _open_: Optional _Controlled_ prop to control if modal is open or not.

```js
import React, { useState } from 'react';
import Modal from '@axa-japan/design-system-react/Modal';
import Button from '@axa-japan/design-system-react/Button';

function ModalExample() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);

  return (
    <Modal
      id="default-modal"
      open={open}
      setOpen={setOpen}
      title="モーダルタイトル"
      content="説明文説明文説明文"
      buttons={[
        {
          text: 'Close',
          onClick,
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Modal Trigger
      </Button>
    </Modal>
  );
}

<ModalExample />;
```

### Default Modal (Uncontrolled):

- _defaultOpen_: Optional _Uncontrolled_ prop to control if modal is open or not.
- _closeModalOnClick_ (button prop): When _Uncontrolled_, you do not handle any state, therefore, _closeModalOnClick_ should be passed if you want the modal to close _onClick_.

```js
import React, { useState } from 'react';
import Modal from '@axa-japan/design-system-react/Modal';
import Button from '@axa-japan/design-system-react/Button';

<Modal
  id="default-modal"
  title="モーダルタイトル"
  content="説明文説明文説明文"
  defaultOpen={false}
  buttons={[
    {
      text: 'Close',
      closeModalOnClick: true,
    },
  ]}
>
  <Button variant="filled" color="blue">
    Modal Trigger
  </Button>
</Modal>;
```

### Modal with two Buttons:

```js
import React, { useState } from 'react';
import Modal from '@axa-japan/design-system-react/Modal';
import Button from '@axa-japan/design-system-react/Button';

function ModalExample() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);

  return (
    <Modal
      id="modal-with-buttons"
      open={open}
      setOpen={setOpen}
      title="Two Buttons Modal"
      content="Content for the Modal"
      buttons={[
        {
          text: 'Submit',
        },
        {
          text: 'Close',
          onClick: onClick,
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Modal Trigger
      </Button>
    </Modal>
  );
}

<ModalExample />;
```

### Default Modal with scrollable content:

```js
import React, { useState } from 'react';
import Modal from '@axa-japan/design-system-react/Modal';
import Button from '@axa-japan/design-system-react/Button';

function ModalExample() {
  const [open, setOpen] = useState(false);

  const onClick = () => setOpen((currentValue) => !currentValue);

  return (
    <Modal
      id="modal-scrollable"
      open={open}
      setOpen={setOpen}
      title="ユーザーの追加"
      content="モーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。モーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。モーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。\n\nモーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。モーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。モーダルの説明はここに入ります。モーダルの説明はできるだけ簡潔に誤操作を招かないようにします。このモーダルが何を示しているのかはタイトルに簡潔に示し、説明文はあくまでも補足する内容にとどめましょう。また、モーダルの文章が長すぎたり複雑な行動を促すものは体験を損ねます。下のボタンは「はい」「いいえ」ではなく、明確な動詞等をを示しましょう。"
      buttons={[
        {
          text: 'ユーザーを追加する',
        },
        {
          text: 'キャンセル',
          onClick: onClick,
        },
      ]}
    >
      <Button variant="filled" color="blue" onClick={onClick}>
        Modal Trigger
      </Button>
    </Modal>
  );
}

<ModalExample />;
```

import React, { FC, useId } from 'react';
import * as Dialog from '@radix-ui/react-dialog';

import ModalOverlay from '../ModalBase/ModalOverlay';
import ModalContent from '../ModalBase/ModalContent';
import ModalDescription from '../ModalBase/ModalDescription';
import ModalPositioner from '../ModalBase/ModalPositioner';
import ModalActions from '../ModalBase/ModalActions';
import ModalTitleContainer from './ModalTitleContainer';
import ModalTitle from './ModalTitle';
import Button from '../Button';
import IconButton from '../IconButton';
import { CloseIcon } from '../Icons';

export type ModalButtonProps = {
  /** button text */
  text: string;
  /** pass true if modal should be closed after click (uncontrolled) */
  closeModalOnClick?: boolean;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color'>;

type ModalButtons = [ModalButtonProps] | [ModalButtonProps, ModalButtonProps];

const ModalActionArea: FC<{ modalButtonProps: ModalButtons; isControlled: boolean }> = ({ modalButtonProps, isControlled }) => {
  const firstButton = modalButtonProps[0] && (
    <Button variant="filled" color="blue" {...modalButtonProps[0]}>
      {modalButtonProps[0].text}
    </Button>
  );
  const secondButton = modalButtonProps[1] && (
    <Button variant="outlined" color="blue" {...modalButtonProps[1]}>
      {modalButtonProps[1].text}
    </Button>
  );
  // If the modal is uncontrolled and closeModalOnClick is specified, close modal when button is clicked
  if (!isControlled) {
    return (
      <>
        {modalButtonProps[1]?.closeModalOnClick ? <Dialog.Close asChild>{secondButton}</Dialog.Close> : secondButton}
        {modalButtonProps[0]?.closeModalOnClick ? <Dialog.Close asChild>{firstButton}</Dialog.Close> : firstButton}
      </>
    );
  }
  return (
    <>
      {secondButton}
      {firstButton}
    </>
  );
};

export type ModalProps = {
  /** Optional modal trigger */
  children?: React.ReactElement;
  /** Modal title text */
  title: string;
  /** Modal content */
  content: React.ReactNode;
  /** Function to update the modal status */
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  /** List of 1-2 modal button props */
  buttons: ModalButtons;
} & Pick<Dialog.DialogProps, 'defaultOpen' | 'open' | 'onOpenChange'> &
  Pick<React.ComponentPropsWithoutRef<'div'>, 'id'>;

const Modal: FC<ModalProps> = ({ id, title, buttons: modalButtonProps, open, setOpen, children, content, ...rest }) => {
  const randomId = useId();
  const isControlled = open !== undefined && setOpen !== undefined;
  return (
    <Dialog.Root open={isControlled ? open : undefined} {...rest}>
      {children && modalButtonProps.length > 0 ? <Dialog.Trigger asChild>{children}</Dialog.Trigger> : null}
      <Dialog.Portal>
        <ModalOverlay />
        <ModalPositioner
          data-testid="modal-positioner"
          id={`modal-id-${id || randomId}`}
          onEscapeKeyDown={isControlled ? () => setOpen(false) : undefined}
          onPointerDownOutside={isControlled ? () => setOpen(false) : undefined}
          onInteractOutside={isControlled ? () => setOpen(false) : undefined}
        >
          <ModalContent data-testid="modal-content">
            <ModalTitleContainer>
              <ModalTitle data-testid="modal-title">{title}</ModalTitle>
              <Dialog.Close asChild>
                <IconButton
                  aria-label="close-button"
                  color="grey"
                  icon={<CloseIcon />}
                  data-testid="close-modal-icon-button"
                  onClick={isControlled ? () => setOpen((current) => !current) : undefined}
                />
              </Dialog.Close>
            </ModalTitleContainer>
            <Dialog.Description asChild>
              <ModalDescription data-testid="modal-description">{content}</ModalDescription>
            </Dialog.Description>
            <ModalActions>
              <ModalActionArea isControlled={isControlled} modalButtonProps={modalButtonProps} />
            </ModalActions>
          </ModalContent>
        </ModalPositioner>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default Modal;

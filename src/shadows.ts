import { primitiveColors } from './colors';
import getRgba from './utils/getRgba';

const smallShadow = `0 1px 5px ${getRgba(primitiveColors.grey600, 25)}`;
const mediumShadow = `0 3px 10px ${getRgba(primitiveColors.grey600, 25)}`;

const shadows = {
  smallShadow,
  mediumShadow,
};

// ajds for AXA Japan design system
export const cssShadowVarMap = {
  smallShadow: { name: '--ajds-shadow-small', value: smallShadow },
  mediumShadow: { name: '--ajds-shadow-medium', value: mediumShadow },
} as const;

export function getShadowVar(key: keyof typeof cssShadowVarMap): string {
  return `var(${(cssShadowVarMap[key] || {}).name})`;
}

export default shadows;

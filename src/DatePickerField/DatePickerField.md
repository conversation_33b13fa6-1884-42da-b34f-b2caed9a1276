In addition to props above, &lt;DatePickerField&gt; accepts props for _React Aria Components'_ [DatePicker component](https://react-spectrum.adobe.com/react-aria/DatePicker.html#props).

### Example DatePickerField with required

A DatePicker has no selection by default. An initial, uncontrolled value can be provided to the DatePicker using the _defaultValue_ prop. Alternatively, a controlled value can be provided using the _value_ prop.

DatePicker supports values with both date and time components, but only allows users to modify the date. DatePicker'a _onChange_ event emits an &lt;input&gt; element's change event handler by default.

All objects and utility functions from _@internationalized/date_ are re-exported by _@axa-japan/design-system-react/DatePickerField_.

```js
import React, { useState } from 'react';
import DatePickerField, { CalendarDate } from '@axa-japan/design-system-react/DatePickerField';

const ControlledDatePickerField = () => {
  const [selectedDate, setSelectedDate] = useState(new CalendarDate(2023, 9, 28));
  const [isInvalid, setIsInvalid] = useState(false);
  return (
    <div style={{ width: '200px' }}>
      <DatePickerField
        label="Controlled"
        required={true}
        value={selectedDate}
        isInvalid={isInvalid}
        showError={isInvalid}
        errorMessage="Invalid input!"
        onChange={(e) => {
          const [year, month, day] = e.target.value.split('/');
          // You'll have to make sure that the year, month, day are constrained otherwise CalendarDate will handle it automatically. Example, setting month of 99 will automatically result in 12
          if (Number(year) && Number(month) && Number(day)) {
            setSelectedDate(new CalendarDate('AD', year, month, day));
            setIsInvalid(false);
          } else {
            setIsInvalid(true);
          }
        }}
      />
    </div>
  );
};
<div style={{ display: 'flex', gap: '8px' }}>
  <div style={{ width: '200px' }}>
    <DatePickerField label="Uncontrolled" required={true} defaultValue={new CalendarDate(2023, 9, 28)} />
  </div>
  <ControlledDatePickerField />
</div>;
```

### No Label

```js
import DatePickerField, { CalendarDate } from '@axa-japan/design-system-react/DatePickerField';

<div style={{ width: '200px' }}>
  <DatePickerField />
</div>;
```

### Highlighting holidays

The _holidays_ prop is a list of _DateValue_ which can be passed to highlight days as holidays. For Japanese holidays, it's available to download as a CSV file from [this page](https://www8.cao.go.jp/chosei/shukujitsu/gaiyou.html). This file is updated yearly on February with the holidays of the next year. The CSV file can then be parsed into inputs for this prop.

```js
import DatePickerField, { CalendarDate } from '@axa-japan/design-system-react/DatePickerField';

<div style={{ width: '200px' }}>
  <DatePickerField
    label="Holidays"
    holidays={['2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06']}
    defaultValue={new CalendarDate(2024, 5, 15)}
  />
</div>;
```

### Setting minimum and maximum selectable dates

The _minDate_ and _maxDate_ props can be used to prevent the user from selecting dates outside a certain range.

```js
import DatePickerField, { today } from '@axa-japan/design-system-react/DatePickerField';

<div style={{ width: '200px' }}>
  <DatePickerField label="With minimum and maximum dates" minValue={today()} maxValue={today().add({ days: 14 })} />
</div>;
```

### Disabled

```js
import DatePickerField from '@axa-japan/design-system-react/DatePickerField';

<div style={{ width: '200px' }}>
  <DatePickerField label="Disabled" isDisabled={true} />
</div>;
```

### Disabling dates

- Certain days of the week.
- Multiple date ranges, or even single dates.

```js
import DatePickerField, { CalendarDate, DateValue, getDayOfWeek } from '@axa-japan/design-system-react/DatePickerField';

<div style={{ display: 'flex', gap: '8px' }}>
  <div style={{ width: '200px' }}>
    <DatePickerField
      label="Disabling certain days"
      isDateUnavailable={(date) => {
        const LOCALE = 'ja-JP';
        const unavailableDays = [0, 3, 5];
        return unavailableDays.includes(getDayOfWeek(date, LOCALE));
      }}
    />
  </div>
  <div style={{ width: '200px' }}>
    <DatePickerField
      label="Disabling date ranges"
      isDateUnavailable={(date) => {
        const dateRange = [
          [new CalendarDate(2024, 4, 15), new CalendarDate(2024, 5, 5)],
          [new CalendarDate(2024, 5, 9), new CalendarDate(2024, 5, 9)],
          [new CalendarDate(2024, 5, 14), new CalendarDate(2024, 5, 24)],
          [new CalendarDate(2024, 5, 28), new CalendarDate(2024, 6, 17)],
        ];
        return dateRange.some((interval) => date.compare(interval[0]) >= 0 && date.compare(interval[1]) <= 0);
      }}
    />
  </div>
</div>;
```

### DatePickerField with error

```js
import DatePickerField from '@axa-japan/design-system-react/DatePickerField';

<div style={{ width: '200px' }}>
  <DatePickerField label="With error" errorMessage="error error error error" isInvalid={true} />
</div>;
```

### Exposing Ref

A _ref_ prop can be passed which attaches to the DatePickerField component.

```js
import React, { useRef } from 'react';
import DatePickerField from '@axa-japan/design-system-react/DatePickerField';

const ref = useRef(null);
<div style={{ width: '200px' }}>
  <DatePickerField ref={ref} />
</div>;
```

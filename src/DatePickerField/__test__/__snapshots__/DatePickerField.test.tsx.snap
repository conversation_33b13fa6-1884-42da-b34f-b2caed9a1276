// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DatePickerField > allow full-width number input, and update selected date in calendar 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        data-open="true"
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-4"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r1gv:"
          class="c2 c3"
          id="date-picker-field-id-:r1gv:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-4"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r1gv:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/12/07"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > allow half-width number input, and update selected date in calendar 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        data-open="true"
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-3"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r14q:"
          class="c2 c3"
          id="date-picker-field-id-:r14q:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-3"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r14q:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/12/07"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > open calendar on text field focus, move focus to calendar on ArrowUp key 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        data-open="true"
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-2"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:rol:"
          class="c2 c3"
          id="date-picker-field-id-:rol:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-2"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:rol:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/09/28"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > open calendar on textfield focus, check all cells rendered, and provided date is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        data-open="true"
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-0"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:rb:"
          class="c2 c3"
          id="date-picker-field-id-:rb:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-0"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:rb:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/09/28"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > open calendar when calendar button pressed, check all cells rendered, and provided date is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        data-open="true"
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-1"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:rcg:"
          class="c2 c3"
          id="date-picker-field-id-:rcg:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-1"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            id="date-picker-field-id-:rcg:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/09/28"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > renders without crashing with label and required 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c12 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c9 svg {
  color: currentColor;
}

.c9:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c9:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c10 svg {
  color: currentColor;
}

.c10[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c11 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c8 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c8::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c8:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c7:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c8:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c6:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c5:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c6:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c6[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c6:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c6[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c10:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c10:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="date-picker-field-id-:r0:"
      id="field-label-:r1:"
    >
      title
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </label>
    <div
      class="c4 field-input-wrap"
    >
      <div
        style="width: 100%;"
      >
        <div
          aria-invalid="false"
          aria-label="title"
          aria-labelledby="date-picker-field-id-:r0:"
          class="c5 c6"
          id="date-picker-field-id-:r0:group"
          role="group"
        >
          <input
            aria-haspopup="dialog"
            aria-label="title"
            class="c7 c8"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r0:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value=""
          />
          <button
            aria-label="calendar-trigger-button"
            class="c9"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c10"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c11"
              >
                <svg
                  class="c12"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > update text field after navigating to next month in calendar and date is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-8"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r299:"
          class="c2 c3"
          id="date-picker-field-id-:r299:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-8"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r299:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2024/01/05"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > update text field after navigating to previous month in calendar and date is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-10"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r2le:"
          class="c2 c3"
          id="date-picker-field-id-:r2le:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-10"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r2le:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/12/05"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > update text field when date in calendar is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-6"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r1t4:"
          class="c2 c3"
          id="date-picker-field-id-:r1t4:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-6"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r1t4:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2023/09/05"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DatePickerField > update text field when year and month dropdown is changed and date is selected 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  background-color: transparent;
  padding: var(--ajds-spacing-3) 0 var(--ajds-spacing-3) var(--ajds-spacing-3-5);
}

.c5::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c5:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c4:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c5:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([aria-disabled='true'],:has(button:focus)) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (min-width:332px) {

}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        style="width: 100%;"
      >
        <div
          aria-describedby="react-aria-description-12"
          aria-invalid="false"
          aria-label="date-picker"
          aria-labelledby="date-picker-field-id-:r31j:"
          class="c2 c3"
          id="date-picker-field-id-:r31j:group"
          role="group"
        >
          <input
            aria-describedby="react-aria-description-12"
            aria-haspopup="dialog"
            aria-label="date-picker"
            class="c4 c5"
            data-testid="test-date-picker"
            id="date-picker-field-id-:r31j:"
            inputmode="numeric"
            placeholder="yyyy/mm/dd"
            type="text"
            value="2025/02/05"
          />
          <button
            aria-label="calendar-trigger-button"
            class="c6"
            data-testid="calendar-trigger-button"
            style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
            tabindex="-1"
            type="button"
          >
            <div
              class="c7"
              data-disabled="false"
              style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
            >
              <span
                class="c8"
              >
                <svg
                  class="c9"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
                  />
                </svg>
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

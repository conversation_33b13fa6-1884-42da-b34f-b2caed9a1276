import React from 'react';
import { CalendarDate } from '@internationalized/date';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { render } from '../../utils/testUtils';
import DatePickerField from '../DatePickerField';
import {
  DATE_PICKER_ICON_BUTTON_LABEL,
  NEXT_MONTH_BUTTON_LABEL,
  PREVIOUS_MONTH_BUTTON_LABEL,
  CALENDAR_YEAR_SELECT_LABEL,
  CALENDAR_MONTH_SELECT_LABEL,
} from '../constants';

window.PointerEvent = class PointerEvent extends Event {};
window.HTMLElement.prototype.scrollIntoView = vi.fn();
window.HTMLElement.prototype.hasPointerCapture = vi.fn();
window.HTMLElement.prototype.releasePointerCapture = vi.fn();

describe('DatePickerField', () => {
  test('renders without crashing with label and required', async () => {
    const { getByTestId, queryAllByText, container } = render(
      <DatePickerField data-testid="test-date-picker" label="title" required={true} onFocus={() => {}} onChange={() => {}} onKeyDown={() => {}} />,
    );
    // Get the textfield and check it's initial input
    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    expect(inputField).toHaveValue('');
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);

    expect(container).toMatchSnapshot();
  }, 20000);

  test('open calendar on textfield focus, check all cells rendered, and provided date is selected', async () => {
    const { getByTestId, queryAllByRole, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2023, 9, 28)} />,
    );
    // Get the textfield and check it's initial input, and click it to open the calendar
    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    expect(inputField).toHaveValue('2023/09/28');
    await userEvent.click(inputField);
    // check that all calendar cells are rendered
    const dateElements = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(30);
    // check that a date is selected
    const selectedDate = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-selected') === 'true',
    );
    selectedDate.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(selectedDate).toHaveLength(1);

    expect(container).toMatchSnapshot();

    // Close calendar to prevent DOM cleanup issues
    await userEvent.keyboard('{Escape}');
  }, 80000);

  test('open calendar when calendar button pressed, check all cells rendered, and provided date is selected', async () => {
    const { queryAllByRole, getByRole, container } = render(<DatePickerField defaultValue={new CalendarDate(2023, 9, 28)} />);
    // Get the calendar button and click it to open the calendar
    const calendarButton = getByRole('button');
    expect(calendarButton).toBeInTheDocument();
    expect(calendarButton).toHaveAttribute('aria-label', DATE_PICKER_ICON_BUTTON_LABEL);
    await userEvent.click(calendarButton);
    // check that all calendar cells are rendered
    const dateElements = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(30);
    // check that a date is selected
    const selectedDate = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-selected') === 'true',
    );
    selectedDate.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(selectedDate).toHaveLength(1);

    expect(container).toMatchSnapshot();

    // Close calendar to prevent DOM cleanup issues
    await userEvent.keyboard('{Escape}');
  }, 80000);

  test('open calendar on text field focus, move focus to calendar on ArrowUp key', async () => {
    const { getByTestId, queryAllByRole, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2023, 9, 28)} />,
    );
    // Get the textfield and check it's initial input, and click it to open the calendar
    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    expect(inputField).toHaveValue('2023/09/28');
    await userEvent.click(inputField);
    // Press ArrowUp to move focus to the calendar
    await userEvent.keyboard('{ArrowUp}');

    // check that the initial focused element in the calendar is the initial selected date
    const focusedDate = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' &&
        element.getAttribute('data-outside-month') !== 'true' &&
        element.getAttribute('data-selected') === 'true' &&
        element.getAttribute('data-focused') === 'true',
    );
    focusedDate.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(focusedDate).toHaveLength(1);

    expect(container).toMatchSnapshot();

    // Close calendar to prevent DOM cleanup issues
    await userEvent.keyboard('{Escape}');
  }, 80000);

  test('allow half-width number input, and update selected date in calendar', async () => {
    const { queryAllByRole, getByTestId, container } = render(<DatePickerField data-testid="test-date-picker" />);
    // Get the textfield and check it's initial input, and click it to open the calendar
    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    await userEvent.click(inputField);
    // Type the follow text into the input, and check that it's properly passed
    await userEvent.type(inputField, '20231207', { initialSelectionStart: 0, initialSelectionEnd: 0 });
    expect(inputField).toHaveValue('2023/12/07');
    // check that typed date is selected in the calendar
    const selectedDate = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-selected') === 'true',
    );
    selectedDate.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(selectedDate).toHaveLength(1);

    expect(container).toMatchSnapshot();

    // Close calendar to prevent DOM cleanup issues
    await userEvent.keyboard('{Escape}');
    // Increasing timeout as github actions is slow
  }, 80000);

  test('allow full-width number input, and update selected date in calendar', async () => {
    const { getByTestId, queryAllByRole, container } = render(<DatePickerField data-testid="test-date-picker" />);

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    // open the calendar and move focus to the calendar
    await userEvent.click(inputField);
    await userEvent.type(inputField, '２０２３１２０７', { initialSelectionStart: 0, initialSelectionEnd: 0 });
    expect(inputField).toHaveValue('2023/12/07');
    // check that typed date is selected
    const selectedDate = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-selected') === 'true',
    );
    selectedDate.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(selectedDate).toHaveLength(1);

    expect(container).toMatchSnapshot();

    // Close calendar to prevent DOM cleanup issues
    await userEvent.keyboard('{Escape}');
    // Increasing timeout as github actions is slow
  }, 80000);

  test('update text field when date in calendar is selected', async () => {
    const { getByTestId, queryAllByRole, queryByRole, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2023, 9, 28)} />,
    );

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    // Date input before change
    expect(inputField).toHaveValue('2023/09/28');
    // open the calendar
    await userEvent.click(inputField);

    // Get the 5th calendar cell, 2023/09/05
    const elementToSelect = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    )[4];
    expect(elementToSelect).toBeInTheDocument();

    // Click the cell
    await userEvent.click(elementToSelect);

    // Now text field should be updated, and calendar should be closed after selection
    expect(inputField).toHaveValue('2023/09/05');
    expect(queryByRole('application')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  }, 80000);

  test('update text field after navigating to next month in calendar and date is selected', async () => {
    const { queryAllByRole, queryByRole, getByTestId, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2023, 12, 1)} />,
    );

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    // Date input before change
    expect(inputField).toHaveValue('2023/12/01');
    // open the calendar
    await userEvent.click(inputField);

    // Navigate to next month by pressing next month button
    const nextMonthButton = queryAllByRole('button').find(
      (element) => element.tagName === 'BUTTON' && element.getAttribute('name') === NEXT_MONTH_BUTTON_LABEL,
    );
    expect(nextMonthButton).toBeInTheDocument();
    await userEvent.click(nextMonthButton as HTMLButtonElement);

    // Check that year & month dropdown value updated
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();
    expect(yearSelect).toHaveTextContent('2024年');

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();
    expect(monthSelect).toHaveTextContent('1月');

    // Get the 5th calendar cell, 2024/01/05
    const elementToSelect = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    )[4];
    expect(elementToSelect).toBeInTheDocument();

    // Click the cell
    await userEvent.click(elementToSelect);

    // Now text field should be updated, and calendar should be closed after selection
    expect(inputField).toHaveValue('2024/01/05');
    expect(queryByRole('application')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  }, 80000);

  test('update text field after navigating to previous month in calendar and date is selected', async () => {
    const { queryAllByRole, queryByRole, getByTestId, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2024, 1, 1)} />,
    );

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    // Date input before change
    expect(inputField).toHaveValue('2024/01/01');
    // open the calendar
    await userEvent.click(inputField);

    // Navigate to previous month
    const previousMonthButton = queryAllByRole('button').find(
      (element) => element.tagName === 'BUTTON' && element.getAttribute('name') === PREVIOUS_MONTH_BUTTON_LABEL,
    );
    expect(previousMonthButton).toBeInTheDocument();
    await userEvent.click(previousMonthButton as HTMLButtonElement);

    // Check that year & month dropdown value updated
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();
    expect(yearSelect).toHaveTextContent('2023年');

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();
    expect(monthSelect).toHaveTextContent('12月');

    // Get the 5th calendar cell, 2023/12/05
    const elementToSelect = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    )[4];
    expect(elementToSelect).toBeInTheDocument();

    // Click the cell
    await userEvent.click(elementToSelect);

    // Now text field should be updated, and calendar should be closed after selection
    expect(inputField).toHaveValue('2023/12/05');
    expect(queryByRole('application')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  }, 80000);

  test('update text field when year and month dropdown is changed and date is selected', async () => {
    const { queryAllByRole, queryByRole, getByTestId, getAllByTestId, container } = render(
      <DatePickerField data-testid="test-date-picker" defaultValue={new CalendarDate(2024, 1, 1)} />,
    );

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    // Date input before change
    expect(inputField).toHaveValue('2024/01/01');
    // open the calendar
    await userEvent.click(inputField);

    // Check for the year & month dropdowns and their currently selected option
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();
    expect(yearSelect).toHaveTextContent('2024年');

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();
    expect(monthSelect).toHaveTextContent('1月');

    await userEvent.click(getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-trigger`));
    const yearOption = getAllByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-item-2025`);
    expect(yearOption[0]).toBeInTheDocument();

    await userEvent.click(yearOption[0]);

    await userEvent.click(getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-trigger`));
    const monthOption = getAllByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-item-2`);
    expect(monthOption[0]).toBeInTheDocument();

    await userEvent.click(monthOption[0]);

    // Get the 5th calendar cell, 2025/02/05
    const elementToSelect = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    )[4];
    expect(elementToSelect).toBeInTheDocument();

    // Click the cell
    await userEvent.click(elementToSelect);

    // Now text field should be updated, and calendar should be closed after selection
    expect(inputField).toHaveValue('2025/02/05');
    expect(queryByRole('application')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  }, 80000);

  test('renders disabled DatePicker', async () => {
    const { getByTestId } = render(<DatePickerField data-testid="test-date-picker" disabled />);

    const inputField = getByTestId('test-date-picker');
    expect(inputField).toBeInTheDocument();
    expect(inputField).toBeDisabled();
  });

  test('renders DatePicker with error', async () => {
    const { getByTestId, queryAllByText } = render(<DatePickerField data-testid="test-date-picker" errorMessage="error error error error" />);

    const inputField = getByTestId('test-date-picker');
    const errorText = queryAllByText('error error error error');
    expect(errorText).toHaveLength(1);
    expect(inputField).toBeInTheDocument();
  });
});

import React from 'react';
import { CalendarDate } from '@internationalized/date';
import DatePicker<PERSON>ield from '../DatePickerField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('DatePickerField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<DatePickerField />, task);
  });

  test('WithSelectedDate', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePickerField defaultValue={new CalendarDate(2023, 9, 28)} />
      </div>,
      task,
      {
        interactionSelector: 'input[aria-label="date-picker"]',
        interactionType: 'focus',
      },
    );
  });

  test('WithSelectedDateTriggeredWithButton', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePicker<PERSON>ield defaultValue={new CalendarDate(2023, 9, 28)} />
      </div>,
      task,
      {
        interactionSelector: 'button[aria-label="calendar-trigger-button"]',
        interactionType: 'click',
      },
    );
  });

  test('WithMinAndMaxDate', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePickerField
          defaultValue={new CalendarDate(2023, 9, 15)}
          minValue={new CalendarDate(2023, 9, 8)}
          maxValue={new CalendarDate(2023, 9, 20)}
        />
      </div>,
      task,
      {
        interactionSelector: 'input[aria-label="date-picker"]',
        interactionType: 'focus',
      },
    );
  });

  test('WithHolidays', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePickerField defaultValue={new CalendarDate(2024, 5, 1)} holidays={['2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06']} />
      </div>,
      task,
      {
        interactionSelector: 'input[aria-label="date-picker"]',
        interactionType: 'focus',
      },
    );
  });

  test('WithDisabled', async ({ task }) => {
    await takeScreenshot(<DatePickerField disabled />, task);
  });

  test('WithDisabledSpecificDates', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePickerField
          defaultValue={new CalendarDate(2023, 9, 28)}
          isDateUnavailable={(date) => {
            const dateRange = [
              [new CalendarDate(2023, 9, 1), new CalendarDate(2023, 9, 5)],
              [new CalendarDate(2023, 9, 8), new CalendarDate(2023, 9, 8)],
              [new CalendarDate(2023, 9, 14), new CalendarDate(2023, 9, 24)],
            ];
            return dateRange.some((interval) => date.compare(interval[0]) >= 0 && date.compare(interval[1]) <= 0);
          }}
        />
      </div>,
      task,
      {
        interactionSelector: 'input[aria-label="date-picker"]',
        interactionType: 'focus',
      },
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(
      <div style={{ height: '450px', padding: '8px' }}>
        <DatePickerField errorMessage="error error error error" defaultValue={new CalendarDate(2023, 9, 28)} />
      </div>,
      task,
      {
        interactionSelector: 'input[aria-label="date-picker"]',
        interactionType: 'focus',
      },
    );
  });
});

import React, { forwardRef, useId } from 'react';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import DatePicker, { DatePickerProps } from '../DatePicker/DatePicker';
import checkHasError from '../utils/checkHasError';

export type DatePickerFieldProps = CommonFieldBaseProps & DatePickerProps;

const DatePickerField = forwardRef<HTMLInputElement, DatePickerFieldProps>(
  ({ id, name, label, errorMessage, disabled = false, showError, required = false, showRequiredIndicator, sx, ...rest }, ref) => {
    const hasError = checkHasError(showError, errorMessage);
    const fallbackId = `date-picker-field-id-${useId()}`;

    return (
      <FieldBase
        id={id || name || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <DatePicker
          ref={ref}
          isInvalid={hasError}
          isDisabled={disabled}
          isRequired={required}
          id={id || name || fallbackId}
          name={name}
          aria-label={label ?? 'date-picker'}
          {...rest}
        />
      </FieldBase>
    );
  },
);

DatePickerField.displayName = 'DatePickerField';

export default DatePickerField;

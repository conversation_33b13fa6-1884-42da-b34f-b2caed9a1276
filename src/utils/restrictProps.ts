import { HTMLAttributes } from 'react';

// usage: to restrict modification of a prop(size)
function restrictProps<HTMLElementType = HTMLDivElement>(restrictedProps: string[]) {
  return (
    prop: 'ref' | 'key' | keyof HTMLAttributes<HTMLElementType>,
    defaultValidatorFn: (prop: 'ref' | 'key' | keyof HTMLAttributes<HTMLElementType>) => boolean,
  ) => !restrictedProps.includes(prop) && defaultValidatorFn(prop);
}

export default restrictProps;

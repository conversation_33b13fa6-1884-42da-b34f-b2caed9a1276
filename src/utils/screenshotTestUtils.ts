import { Test } from '@vitest/runner';
import { page } from '@vitest/browser/context';
import { render } from 'vitest-browser-react';
import { TestContext } from 'vitest';
import BaseStyles from '../BaseStyles';

const renderInBrowser = (component: React.JSX.Element) => {
  const wrapper = document.createElement('div');

  const { container } = render(component, {
    wrapper: BaseStyles,
  });
  wrapper.appendChild(container);

  return wrapper;
};

interface TakeScreenshotOptions {
  interactionSelector?: string;
  interactionType?: 'focus' | 'click';
  delay?: boolean;
  viewport?: { width?: number; height?: number };
}

const getDefaultViewportDimensions = (viewportType: string) => {
  switch (viewportType) {
    case 'mobile':
      return { width: 375, height: 667 };
    case 'tablet':
      return { width: 1024, height: 768 };
    case 'desktop':
      return { width: 1280, height: 720 };
    default:
      return { width: 375, height: 667 }; // Default to mobile
  }
};

const takeScreenshot = async (component: React.JSX.Element, task: Readonly<Test<TestContext>>, options: TakeScreenshotOptions = {}) => {
  const { interactionSelector, interactionType = 'focus', delay = false, viewport } = options;
  // Set viewport if provided
  if (viewport && (viewport.width || viewport.height)) {
    const currentViewport = window.__VIEWPORT__ || 'mobile';
    const defaults = getDefaultViewportDimensions(currentViewport);
    await page.viewport(viewport.width || defaults.width, viewport.height || defaults.height);
  }

  // Create a wrapper and render the component
  const container = renderInBrowser(component);
  document.body.appendChild(container);
  // Interact with element if selector is provided
  if (interactionSelector) {
    const element = container.querySelector(interactionSelector);
    if (element) {
      if (interactionType === 'click') {
        (element as HTMLElement).click();
      } else if (interactionType === 'focus') {
        (element as HTMLElement).focus();
      }
    }
  }

  // Add delay if requested
  if (delay) {
    await new Promise<void>((resolve) => {
      setTimeout(() => resolve(), 1500);
    });
  }

  // Take a screenshot and place image in the specified path
  const screenshot = await page.screenshot({
    path: `./__screenshots__/${window.__VIEWPORT__}/${task?.suite?.name}-${task.name}.png`,
  });
  // Check that screenshot has been taken
  expect(screenshot).toBeTruthy();
  // Remove the wrapper from the document
  document.body.removeChild(container);
};

export { takeScreenshot };

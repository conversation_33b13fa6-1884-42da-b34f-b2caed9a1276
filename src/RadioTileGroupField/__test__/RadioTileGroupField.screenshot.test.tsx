import React from 'react';
import RadioTileGroupField from '../RadioTileGroupField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('RadioTileGroupField', () => {
  test('DefaultWithRequired', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="Default with required"
        required={true}
        defaultValue="2"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="Focus"
        defaultValue="2"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
      task,
      {
        interactionSelector: 'label[data-part="item"]:nth-child(2)',
        interactionType: 'focus',
      },
    );
  });

  test('WithItemsPerRow', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '500px' }}>
        <RadioTileGroupField
          label="Items per row"
          defaultValue="2"
          itemsPerRow={4}
          options={[
            { title: 'radio', value: '1' },
            { title: 'radio', value: '2' },
            { title: 'radio', value: '3' },
            { title: 'radio', value: '4' },
            { title: 'radio', value: '5' },
            { title: 'radio', value: '6' },
          ]}
        />
      </div>,
      task,
    );
  });

  test('WithDescription', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="With description"
        defaultValue="2"
        options={[
          { title: 'radio', description: 'This is a description.', value: '1' },
          { title: 'radio', description: 'This is a description.\nThis is a description.', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="Disabled"
        defaultValue="2"
        disabled={true}
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="Error"
        defaultValue="2"
        errorMessage="error error error error"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasErrorFocus', async ({ task }) => {
    await takeScreenshot(
      <RadioTileGroupField
        label="Error focus"
        defaultValue="2"
        errorMessage="error error error error"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
      task,
      {
        interactionSelector: 'label[data-part="item"]:nth-child(2)',
        interactionType: 'focus',
      },
    );
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RadioTileGroupField > renders without crashing with disabled 1`] = `
.c10 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c3 {
  display: grid;
  grid-gap: var(--ajds-spacing-4);
  width: 100%;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--ajds-color-utility-background-white);
  border: 2px solid var(--ajds-color-utility-stroke-light);
  border-radius: var(--ajds-radius-sm);
  overflow: hidden;
  min-height: var(--ajds-spacing-21);
  -webkit-transition: 0.2s border ease;
  transition: 0.2s border ease;
  width: 100% !important;
}

.c5[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:hover[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
}

.c5:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid][data-state='checked'] {
  border: 2px solid var(--ajds-color-status-important);
}

.c5[data-disabled] {
  cursor: not-allowed;
  border: 2px solid var(--ajds-color-interactive-disabled-light);
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-disabled][data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-3-5) var(--ajds-spacing-4);
  height: 100%;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c9[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c9[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c9[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c9[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c9[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6 > .c8[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c12 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  overflow-wrap: anywhere;
  white-space: break-spaces;
  font-weight: var(--ajds-font-weight-bold);
}

.c12[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r7:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:id-:r6::label"
        aria-orientation="horizontal"
        class="c3"
        data-disabled=""
        data-orientation="horizontal"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:id-:r6:"
        role="radiogroup"
        style="position: relative; grid-template-columns: repeat(1, 1fr);"
      >
        <label
          class="c4 c5"
          data-disabled=""
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="checked"
          dir="ltr"
          for="radio-group:id-:r6::radio:input:1"
          id="radio-group:id-:r6::radio:1"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-disabled=""
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="checked"
              dir="ltr"
              id="radio-group:id-:r6::radio:control:1"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-disabled=""
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="checked"
                dir="ltr"
                id="radio-group:id-:r6::radio:label:1"
              >
                radio
              </span>
            </div>
            <input
              checked=""
              data-ownedby="radio-group:id-:r6:"
              disabled=""
              id="radio-group:id-:r6::radio:input:1"
              name="id-:r6:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="1"
            />
          </div>
        </label>
        <label
          class="c4 c5"
          data-disabled=""
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="unchecked"
          dir="ltr"
          for="radio-group:id-:r6::radio:input:2"
          id="radio-group:id-:r6::radio:2"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-disabled=""
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="unchecked"
              dir="ltr"
              id="radio-group:id-:r6::radio:control:2"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-disabled=""
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="unchecked"
                dir="ltr"
                id="radio-group:id-:r6::radio:label:2"
              >
                radio
              </span>
            </div>
            <input
              data-ownedby="radio-group:id-:r6:"
              disabled=""
              id="radio-group:id-:r6::radio:input:2"
              name="id-:r6:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="2"
            />
          </div>
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`RadioTileGroupField > renders without crashing with error 1`] = `
.c10 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c3 {
  display: grid;
  grid-gap: var(--ajds-spacing-4);
  width: 100%;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--ajds-color-utility-background-white);
  border: 2px solid var(--ajds-color-utility-stroke-light);
  border-radius: var(--ajds-radius-sm);
  overflow: hidden;
  min-height: var(--ajds-spacing-21);
  -webkit-transition: 0.2s border ease;
  transition: 0.2s border ease;
  width: 100% !important;
}

.c5[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:hover[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
}

.c5:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid][data-state='checked'] {
  border: 2px solid var(--ajds-color-status-important);
}

.c5[data-disabled] {
  cursor: not-allowed;
  border: 2px solid var(--ajds-color-interactive-disabled-light);
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-disabled][data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-3-5) var(--ajds-spacing-4);
  height: 100%;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c9[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c9[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c9[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c9[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c9[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6 > .c8[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c12 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  overflow-wrap: anywhere;
  white-space: break-spaces;
  font-weight: var(--ajds-font-weight-bold);
}

.c12[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:ra:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:id-:r9::label"
        aria-orientation="horizontal"
        class="c3"
        data-orientation="horizontal"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:id-:r9:"
        role="radiogroup"
        style="position: relative; grid-template-columns: repeat(1, 1fr);"
      >
        <label
          class="c4 c5"
          data-invalid=""
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="checked"
          dir="ltr"
          for="radio-group:id-:r9::radio:input:1"
          id="radio-group:id-:r9::radio:1"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-invalid=""
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="checked"
              dir="ltr"
              id="radio-group:id-:r9::radio:control:1"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-invalid=""
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="checked"
                dir="ltr"
                id="radio-group:id-:r9::radio:label:1"
              >
                radio
              </span>
            </div>
            <input
              checked=""
              data-ownedby="radio-group:id-:r9:"
              id="radio-group:id-:r9::radio:input:1"
              name="id-:r9:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="1"
            />
          </div>
        </label>
        <label
          class="c4 c5"
          data-invalid=""
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="unchecked"
          dir="ltr"
          for="radio-group:id-:r9::radio:input:2"
          id="radio-group:id-:r9::radio:2"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-invalid=""
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="unchecked"
              dir="ltr"
              id="radio-group:id-:r9::radio:control:2"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-invalid=""
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="unchecked"
                dir="ltr"
                id="radio-group:id-:r9::radio:label:2"
              >
                radio
              </span>
            </div>
            <input
              data-ownedby="radio-group:id-:r9:"
              id="radio-group:id-:r9::radio:input:2"
              name="id-:r9:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="2"
            />
          </div>
        </label>
      </div>
    </div>
    <div
      aria-live="polite"
      class="c13"
      role="alert"
    >
      <div
        class="c14"
      >
        <svg
          class="c10"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      error error error error
    </div>
  </fieldset>
</div>
`;

exports[`RadioTileGroupField > renders without crashing with itemsPerRow prop 1`] = `
.c10 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c3 {
  display: grid;
  grid-gap: var(--ajds-spacing-4);
  width: 100%;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--ajds-color-utility-background-white);
  border: 2px solid var(--ajds-color-utility-stroke-light);
  border-radius: var(--ajds-radius-sm);
  overflow: hidden;
  min-height: var(--ajds-spacing-21);
  -webkit-transition: 0.2s border ease;
  transition: 0.2s border ease;
  width: 100% !important;
}

.c5[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:hover[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
}

.c5:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c5[data-invalid][data-state='checked'] {
  border: 2px solid var(--ajds-color-status-important);
}

.c5[data-disabled] {
  cursor: not-allowed;
  border: 2px solid var(--ajds-color-interactive-disabled-light);
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-disabled][data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-3-5) var(--ajds-spacing-4);
  height: 100%;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c9[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c9[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c9[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c9[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c9[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c9[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6 > .c8[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c12 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  overflow-wrap: anywhere;
  white-space: break-spaces;
  font-weight: var(--ajds-font-weight-bold);
}

.c12[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r4:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:id-:r3::label"
        aria-orientation="horizontal"
        class="c3"
        data-orientation="horizontal"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:id-:r3:"
        role="radiogroup"
        style="position: relative; grid-template-columns: repeat(4, 1fr);"
      >
        <label
          class="c4 c5"
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="unchecked"
          dir="ltr"
          for="radio-group:id-:r3::radio:input:1"
          id="radio-group:id-:r3::radio:1"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="unchecked"
              dir="ltr"
              id="radio-group:id-:r3::radio:control:1"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="unchecked"
                dir="ltr"
                id="radio-group:id-:r3::radio:label:1"
              >
                radio
              </span>
            </div>
            <input
              data-ownedby="radio-group:id-:r3:"
              id="radio-group:id-:r3::radio:input:1"
              name="id-:r3:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="1"
            />
          </div>
        </label>
        <label
          class="c4 c5"
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="checked"
          dir="ltr"
          for="radio-group:id-:r3::radio:input:2"
          id="radio-group:id-:r3::radio:2"
        >
          <div
            class="c6 c7"
          >
            <div
              aria-hidden="true"
              class="c8 c9"
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="checked"
              dir="ltr"
              id="radio-group:id-:r3::radio:control:2"
            >
              <svg
                class="c10"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c11"
            >
              <span
                class="c12"
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="checked"
                dir="ltr"
                id="radio-group:id-:r3::radio:label:2"
              >
                radio
              </span>
            </div>
            <input
              checked=""
              data-ownedby="radio-group:id-:r3:"
              id="radio-group:id-:r3::radio:input:2"
              name="id-:r3:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="2"
            />
          </div>
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`RadioTileGroupField > renders without crashing with required, description, and check user interaction 1`] = `
.c12 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  display: grid;
  grid-gap: var(--ajds-spacing-4);
  width: 100%;
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--ajds-color-utility-background-white);
  border: 2px solid var(--ajds-color-utility-stroke-light);
  border-radius: var(--ajds-radius-sm);
  overflow: hidden;
  min-height: var(--ajds-spacing-21);
  -webkit-transition: 0.2s border ease;
  transition: 0.2s border ease;
  width: 100% !important;
}

.c7[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7:hover[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
}

.c7:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c7[data-invalid]:has(:focus-visible) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c7[data-invalid][data-state='checked'] {
  border: 2px solid var(--ajds-color-status-important);
}

.c7[data-disabled] {
  cursor: not-allowed;
  border: 2px solid var(--ajds-color-interactive-disabled-light);
}

.c7[data-disabled]:hover {
  background-color: initial;
}

.c7[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c7[data-disabled][data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-3-5) var(--ajds-spacing-4);
  height: 100%;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c11[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c11[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c11[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c11[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c11[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c11[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c11[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c11[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c6:hover > .c8 > .c10[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c14 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  overflow-wrap: anywhere;
  white-space: break-spaces;
  font-weight: var(--ajds-font-weight-bold);
}

.c14[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c14[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c15 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  overflow-wrap: anywhere;
  white-space: break-spaces;
}

.c15[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c15[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r1:"
    >
      title
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:id-:r0::label"
        aria-orientation="horizontal"
        class="c5"
        data-orientation="horizontal"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:id-:r0:"
        role="radiogroup"
        style="position: relative; grid-template-columns: repeat(1, 1fr);"
      >
        <label
          class="c6 c7"
          data-focus=""
          data-hover=""
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="checked"
          dir="ltr"
          for="radio-group:id-:r0::radio:input:1"
          id="radio-group:id-:r0::radio:1"
        >
          <div
            class="c8 c9"
          >
            <div
              aria-hidden="true"
              class="c10 c11"
              data-focus=""
              data-hover=""
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="checked"
              dir="ltr"
              id="radio-group:id-:r0::radio:control:1"
            >
              <svg
                class="c12"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c13"
            >
              <span
                class="c14"
                data-focus=""
                data-hover=""
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="checked"
                dir="ltr"
                id="radio-group:id-:r0::radio:label:1"
              >
                radio
              </span>
              <span
                class="c15"
                data-focus=""
                data-hover=""
                data-orientation="horizontal"
                data-part="item-description"
                data-scope="radio-group"
                data-state="checked"
                dir="ltr"
                id="radio-group:id-:r0::radio:label:1"
              >
                description
              </span>
            </div>
            <input
              checked=""
              data-ownedby="radio-group:id-:r0:"
              id="radio-group:id-:r0::radio:input:1"
              name="id-:r0:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="1"
            />
          </div>
        </label>
        <label
          class="c6 c7"
          data-orientation="horizontal"
          data-part="item"
          data-scope="radio-group"
          data-state="unchecked"
          dir="ltr"
          for="radio-group:id-:r0::radio:input:2"
          id="radio-group:id-:r0::radio:2"
        >
          <div
            class="c8 c9"
          >
            <div
              aria-hidden="true"
              class="c10 c11"
              data-orientation="horizontal"
              data-part="item-control"
              data-scope="radio-group"
              data-state="unchecked"
              dir="ltr"
              id="radio-group:id-:r0::radio:control:2"
            >
              <svg
                class="c12"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 12 12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="6"
                  cy="6"
                  r="6"
                />
              </svg>
            </div>
            <div
              class="c13"
            >
              <span
                class="c14"
                data-orientation="horizontal"
                data-part="item-title"
                data-scope="radio-group"
                data-state="unchecked"
                dir="ltr"
                id="radio-group:id-:r0::radio:label:2"
              >
                radio
              </span>
              <span
                class="c15"
                data-orientation="horizontal"
                data-part="item-description"
                data-scope="radio-group"
                data-state="unchecked"
                dir="ltr"
                id="radio-group:id-:r0::radio:label:2"
              >
                description
              </span>
            </div>
            <input
              data-ownedby="radio-group:id-:r0:"
              id="radio-group:id-:r0::radio:input:2"
              name="id-:r0:"
              style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
              type="radio"
              value="2"
            />
          </div>
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

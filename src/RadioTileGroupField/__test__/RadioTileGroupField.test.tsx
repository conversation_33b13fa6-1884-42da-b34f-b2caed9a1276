import React from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import RadioTileGroupField from '../RadioTileGroupField';

describe('RadioTileGroupField', () => {
  test('renders without crashing with required, description, and check user interaction', async () => {
    const { queryAllByText, getByRole, container } = render(
      <RadioTileGroupField
        label="title"
        required={true}
        defaultValue="2"
        options={[
          { title: 'radio', description: 'description', value: '1' },
          { title: 'radio', description: 'description', value: '2' },
        ]}
      />,
    );
    // check that elements exist in the DOM
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);
    const title = queryAllByText('radio');
    expect(title).toHaveLength(2);
    const description = queryAllByText('description');
    expect(description).toHaveLength(2);
    const radioGroup = getByRole('radiogroup');
    expect(radioGroup.getAttribute('data-orientation')).toBe('horizontal');
    // Check that interactions are working properly
    expect(title[0].getAttribute('data-state')).toBe('unchecked');
    expect(title[1].getAttribute('data-state')).toBe('checked');
    await userEvent.click(title[0]);
    await waitFor(() => {
      expect(title[0].getAttribute('data-state')).toBe('checked');
      expect(title[1].getAttribute('data-state')).toBe('unchecked');
    });
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with itemsPerRow prop', async () => {
    const { queryAllByText, getByRole, container } = render(
      <RadioTileGroupField
        label="title"
        itemsPerRow={4}
        defaultValue="2"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
    );
    // check that elements exist in the DOM
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const title = queryAllByText('radio');
    expect(title).toHaveLength(2);
    const radioGroup = getByRole('radiogroup');
    expect(radioGroup.getAttribute('data-orientation')).toBe('horizontal');
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled', () => {
    const { queryAllByText, getAllByRole, container } = render(
      <RadioTileGroupField
        label="title"
        defaultValue="1"
        disabled={true}
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('radio');
    expect(testElement).toHaveLength(2);
    const testItems = getAllByRole('radio');
    testItems.forEach((radio) => expect(radio).toBeDisabled());
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with error', () => {
    const { queryAllByText, container } = render(
      <RadioTileGroupField
        label="title"
        defaultValue="1"
        errorMessage="error error error error"
        options={[
          { title: 'radio', value: '1' },
          { title: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const errorText = queryAllByText('error error error error');
    expect(errorText).toHaveLength(1);
    const testItems = queryAllByText('radio');
    expect(testItems).toHaveLength(2);
    testItems.forEach((item) => expect(item.getAttribute('data-invalid')).not.toBeUndefined());
    expect(container).toMatchSnapshot();
  });
});

import React, { forwardRef, useId } from 'react';
import checkHasError from '../utils/checkHasError';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import RadioTileRoot from '../RadioTile/RadioTileRoot';
import RadioTile, { RadioTileOptions } from '../RadioTile/RadioTile';

export type RadioTileGroupFieldProps = {
  /** Set the maximum number of items per row, each item will take a fraction of the space available */
  itemsPerRow?: number;
  /** Props for Radio components, at least one must be provided */
  options: [RadioTileOptions, ...RadioTileOptions[]];
} & CommonFieldBaseProps &
  Pick<React.ComponentPropsWithoutRef<'input'>, 'defaultValue' | 'disabled' | 'form' | 'id' | 'name' | 'value' | 'onChange'>;

const RadioTileGroupField = forwardRef<HTMLDivElement, RadioTileGroupFieldProps>(
  (
    {
      id,
      name,
      form,
      label,
      onChange,
      showError,
      errorMessage,
      required = false,
      showRequiredIndicator,
      value,
      defaultValue,
      disabled = false,
      itemsPerRow = 1,
      options,
      sx,
    },
    ref,
  ) => {
    const hasError = checkHasError(showError, errorMessage);
    const fallbackId = `id-${useId()}`;

    return (
      <FieldBase
        useFieldsetWrapper
        id={`radio-group:${id || name || fallbackId}`}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <RadioTileRoot
          style={{ gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)` }}
          disabled={disabled}
          ref={ref}
          id={id || name || fallbackId}
          name={name || fallbackId}
          form={form}
          defaultValue={defaultValue?.toString()}
          value={value?.toString()}
          orientation="horizontal"
          onChange={onChange}
        >
          {options.map((option, idx) => {
            return <RadioTile key={`${fallbackId}:${idx}`} hasError={hasError} form={form} {...option} />;
          })}
        </RadioTileRoot>
      </FieldBase>
    );
  },
);

RadioTileGroupField.displayName = 'RadioTileGroupField';

export default RadioTileGroupField;

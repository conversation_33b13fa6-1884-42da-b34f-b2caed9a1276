### Example RadioTileGroupField - Uncontrolled with required

If you want to get the value of the option onChange, you can retrieve the option and its HTMLInputElement properties through an onChange handler:

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="Uncontrolled with required"
  required={true}
  defaultValue="2"
  options={[
    { title: 'radio', value: '1' },
    { title: 'radio', value: '2' },
  ]}
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
/>;
```

### Example RadioGroupField - Controlled

The component can be either controlled or uncontrolled by passing the _value_ or _defaultValue_ prop respectively.

```js
import React, { useState } from 'react';
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

const [value, setValue] = useState('2');

<RadioTileGroupField
  label="Controlled"
  value={value}
  options={[
    { title: 'radio', value: '1' },
    { title: 'radio', value: '2' },
  ]}
  onChange={(e) => {
    setValue(e.target.value);
  }}
/>;
```

### Example RadioTileGroupField - With description

An optional description can be added per option. Line-breaks can be inserted at any point of the text.

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="With description"
  defaultValue="2"
  options={[
    { title: 'radio', description: 'This is a description.', value: '1' },
    { title: 'radio', description: 'This is a description.\nThis is a description.', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example RadioTileGroupField - itemsPerRow

The _itemsPerRow_ prop can be used to set the maximum number of items per row, each item will take a fraction of the space available and adjust its width accordingly. Default is _1_ item per row.

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="Horizontal"
  itemsPerRow={4}
  defaultValue="6"
  options={[
    { title: 'radio', value: '1' },
    { title: 'radio', value: '2' },
    { title: 'radio', value: '3' },
    { title: 'radio', value: '4' },
    { title: 'radio', value: '5' },
    { title: 'radio', value: '6' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example RadioTileGroupField - Disabled

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="Disabled"
  disabled={true}
  defaultValue="2"
  options={[
    { title: 'radio', value: '1' },
    { title: 'radio', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example RadioTileGroupField - Partially Disabled

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="Partially Disabled"
  options={[
    { title: 'radio', value: '1', disabled: true },
    { title: 'radio', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example RadioTileGroupField - Error

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<RadioTileGroupField
  label="Error"
  errorMessage="error error error error"
  defaultValue="2"
  options={[
    { title: 'radio', value: '1' },
    { title: 'radio', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example RadioTileGroupField - Form Interaction

If you want to get the value of the option onChange through a &lt;Form&gt;, you can do so by wrapping &lt;RadioTileGroupField&gt; in a &lt;Form&gt;:

```js
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

<form
  id="form1"
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
>
  <RadioTileGroupField
    form="form1"
    label="Form integration"
    displayInline={true}
    defaultValue="3"
    options={[
      { title: 'radio1', value: '1' },
      { title: 'radio2', value: '2' },
      { title: 'radio3', value: '3' },
    ]}
    onChange={() => console.log('change')}
  />
</form>;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the &lt;div&gt; container of the &lt;RadioTileGroupField&gt;.

```js
import React, { useRef } from 'react';
import RadioTileGroupField from '@axa-japan/design-system-react/RadioTileGroupField';

const Render = () => {
  const ref = useRef(null);
  return (
    <RadioTileGroupField
      ref={ref}
      label="Ref"
      defaultValue="2"
      options={[
        { title: 'radio', value: '1' },
        { title: 'radio', value: '2' },
      ]}
      onChange={() => console.log('change')}
    />
  );
};

<Render />;
```

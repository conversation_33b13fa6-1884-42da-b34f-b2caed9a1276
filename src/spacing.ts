const spacing = {
  0: '0', // 0px
  px: '1px', // 1px
  0.5: '0.125rem', // 2px
  1: '0.25rem', // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem', // 8px
  3: '0.75rem', // 12px
  3.5: '0.875rem', // 14px
  4: '1rem', // 16px
  5: '1.25rem', // 20px
  6: '1.5rem', // 24px
  7: '1.75rem', // 28px
  8: '2rem', // 32px
  9: '2.25rem', // 36px
  10: '2.5rem', // 40px
  11: '2.75rem', // 44px
  12: '3rem', // 48px
  14: '3.5rem', // 56px
  16: '4rem', // 64px
  20: '5rem', // 80px
  21: '5.25rem', // 84px
  24: '6rem', // 96px
  32: '8rem', // 128px
  36: '9rem', // 144px
  40: '10rem', // 160px
  44: '11rem', // 176px
  48: '12rem', // 192px
  52: '13rem', // 208px
  56: '14rem', // 224px
  60: '15rem', // 240px
  64: '16rem', // 256px
  72: '18rem', // 288px
  80: '20rem', // 320px
  96: '24rem', // 384px
  112: '28rem', // 448px
  auto: 'auto',
} as const;

// Extract the keys of the spacing object while keeping their original types
export const spacingKeys = Object.keys(spacing).map((key) => (Number.isNaN(+key) ? key : +key)) as Array<keyof typeof spacing>;

type CssSpacingVarMapType = {
  [K in keyof typeof spacing]: {
    name: `--ajds-spacing-${K}`;
    value: string;
  };
};

const createCssVarName = (key: keyof typeof spacing): string => {
  return `--ajds-spacing-${String(key).replace('.', '-')}`;
};

export const cssSpacingVarMap = spacingKeys.reduce<CssSpacingVarMapType>(
  (acc, key) => ({
    ...acc,
    [key]: { name: createCssVarName(key), value: spacing[key] },
  }),
  {} as CssSpacingVarMapType,
);

export type SpacingKeysType = keyof typeof spacing;

export function getSpacingVar(key: SpacingKeysType): string {
  return `var(${(cssSpacingVarMap[String(key) as keyof typeof cssSpacingVarMap] || {}).name})`;
}

export default spacing;

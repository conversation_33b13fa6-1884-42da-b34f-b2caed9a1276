import getRgba from './utils/getRgba';

export const primitiveColors = {
  // Grey Scale
  white: '#ffffff',
  grey100: '#f7f7f7',
  grey200: '#f0f0f0',
  grey300: '#e5e5e5',
  grey400: '#cccccc',
  grey500: '#999999',
  grey600: '#757575',
  grey700: '#5f5f5f',
  grey800: '#343c3d',
  grey900: '#111b1d',

  // Axa Blue Scale
  axaBlue100: '#5c5cb7',
  axaBlue200: '#3d3daa',
  axaBlue300: '#1f1f9c',
  axaBlue400: '#00008f',
  axaBlue500: '#00006d',
  axaBlue600: '#010143',

  // Red Scale
  red50: '#fae7ea',
  red100: '#efb8c1',
  red200: '#dc667a',
  red300: '#c91432',
  red400: '#b10723',
  red500: '#8b0118',

  // Green Scale
  green50: '#e2f2ea',
  green100: '#99dfbe',
  green200: '#25b670',
  green300: '#008646',
  green400: '#006031',
  green500: '#004323',

  // Yellow Scale
  yellow50: '#fff2cf',
  yellow100: '#ffda7a',
  yellow200: '#ffbc11',
  yellow300: '#b28000',
  yellow400: '#996e00',
  yellow500: '#735300',

  // Sienna Scale
  sienna50: '#faede9',
  sienna100: '#e28972',
  sienna200: '#dd7358',
  sienna300: '#d75d3d',
  sienna400: '#d24723',
  sienna500: '#b03c1d',
  sienna600: '#8a290e',

  // Ocean Scale
  ocean50: '#e7effc',
  ocean100: '#7698cb',
  ocean200: '#4976ba',
  ocean300: '#3e66a3',
  ocean400: '#274168',
  ocean500: '#17273e',
} as const;

// Define object for semantic colors
const colors = {
  characterPrimary: primitiveColors.grey800,
  characterSecondary: primitiveColors.grey700,
  characterPrimaryWhite: primitiveColors.white,
  characterSecondaryWhite: primitiveColors.grey200,
  characterAccent: primitiveColors.sienna400,

  statusSuccess: primitiveColors.green300,
  statusSuccessLight: primitiveColors.green50,
  statusDanger: primitiveColors.red300,
  statusDangerLight: primitiveColors.red50,
  statusWarning: primitiveColors.yellow200,
  statusWarningLight: primitiveColors.yellow50,
  statusWarningDark: primitiveColors.yellow300,
  statusImportant: primitiveColors.sienna400,
  statusImportantLight: primitiveColors.sienna50,
  statusInformation: primitiveColors.ocean300,
  statusInformationLight: primitiveColors.ocean50,
  statusNeutral: primitiveColors.grey300,
  statusNeutralLight: primitiveColors.grey200,
  statusNeutralDark: primitiveColors.grey400,

  utilityBackgroundWhite: primitiveColors.white,
  utilityBackgroundLight: primitiveColors.grey100,
  utilityBackgroundOcean: primitiveColors.ocean300,
  utilityBackgroundGrey: primitiveColors.grey800,
  utilityStrokeLight: primitiveColors.grey400,
  utilityStrokeDark: primitiveColors.grey600,
  utilityStrokeWhite: primitiveColors.white,
  utilityOverlay: getRgba(primitiveColors.grey800, 72),

  interactiveActivePrimary: primitiveColors.axaBlue400,
  interactiveActiveWhite: primitiveColors.white,
  interactiveActiveGrey: primitiveColors.grey800,
  interactiveHoverPrimary: primitiveColors.axaBlue500,
  interactiveHoverPrimaryTransparent: getRgba(primitiveColors.axaBlue500, 10),
  interactiveHoverWhiteTransparent: getRgba(primitiveColors.white, 15),
  interactiveHoverGreyTransparent: getRgba(primitiveColors.grey800, 5),
  interactiveHoverGrey: primitiveColors.grey200,
  interactiveFocusPrimary: primitiveColors.axaBlue400,
  interactiveFocusWhite: primitiveColors.white,
  interactiveVisitedOcean: primitiveColors.ocean300,
  interactiveVisitedGrey: primitiveColors.grey300,
  interactiveDisabledDark: primitiveColors.grey500,
  interactiveDisabledLight: primitiveColors.grey200,
  interactivePlaceholder: primitiveColors.grey500,
} as const;

const cssPrimitiveColorVarMap = {
  white: { name: '--ajds-color-white', value: primitiveColors.white },
  grey100: { name: '--ajds-color-grey-100', value: primitiveColors.grey100 },
  grey200: { name: '--ajds-color-grey-200', value: primitiveColors.grey200 },
  grey300: { name: '--ajds-color-grey-300', value: primitiveColors.grey300 },
  grey400: { name: '--ajds-color-grey-400', value: primitiveColors.grey400 },
  grey500: { name: '--ajds-color-grey-500', value: primitiveColors.grey500 },
  grey600: { name: '--ajds-color-grey-600', value: primitiveColors.grey600 },
  grey700: { name: '--ajds-color-grey-700', value: primitiveColors.grey700 },
  grey800: { name: '--ajds-color-grey-800', value: primitiveColors.grey800 },
  grey900: { name: '--ajds-color-grey-900', value: primitiveColors.grey900 },
  axaBlue100: { name: '--ajds-color-axa-blue-100', value: primitiveColors.axaBlue100 },
  axaBlue200: { name: '--ajds-color-axa-blue-200', value: primitiveColors.axaBlue200 },
  axaBlue300: { name: '--ajds-color-axa-blue-300', value: primitiveColors.axaBlue300 },
  axaBlue400: { name: '--ajds-color-axa-blue-400', value: primitiveColors.axaBlue400 },
  axaBlue500: { name: '--ajds-color-axa-blue-500', value: primitiveColors.axaBlue500 },
  axaBlue600: { name: '--ajds-color-axa-blue-600', value: primitiveColors.axaBlue600 },
  red50: { name: '--ajds-color-red-50', value: primitiveColors.red50 },
  red100: { name: '--ajds-color-red-100', value: primitiveColors.red100 },
  red200: { name: '--ajds-color-red-200', value: primitiveColors.red200 },
  red300: { name: '--ajds-color-red-300', value: primitiveColors.red300 },
  red400: { name: '--ajds-color-red-400', value: primitiveColors.red400 },
  red500: { name: '--ajds-color-red-500', value: primitiveColors.red500 },
  green50: { name: '--ajds-color-green-50', value: primitiveColors.green50 },
  green100: { name: '--ajds-color-green-100', value: primitiveColors.green100 },
  green200: { name: '--ajds-color-green-200', value: primitiveColors.green200 },
  green300: { name: '--ajds-color-green-300', value: primitiveColors.green300 },
  green400: { name: '--ajds-color-green-400', value: primitiveColors.green400 },
  green500: { name: '--ajds-color-green-500', value: primitiveColors.green500 },
  yellow50: { name: '--ajds-color-yellow-50', value: primitiveColors.yellow50 },
  yellow100: { name: '--ajds-color-yellow-100', value: primitiveColors.yellow100 },
  yellow200: { name: '--ajds-color-yellow-200', value: primitiveColors.yellow200 },
  yellow300: { name: '--ajds-color-yellow-300', value: primitiveColors.yellow300 },
  yellow400: { name: '--ajds-color-yellow-400', value: primitiveColors.yellow400 },
  yellow500: { name: '--ajds-color-yellow-500', value: primitiveColors.yellow500 },
  sienna50: { name: '--ajds-color-sienna-50', value: primitiveColors.sienna50 },
  sienna100: { name: '--ajds-color-sienna-100', value: primitiveColors.sienna100 },
  sienna200: { name: '--ajds-color-sienna-200', value: primitiveColors.sienna200 },
  sienna300: { name: '--ajds-color-sienna-300', value: primitiveColors.sienna300 },
  sienna400: { name: '--ajds-color-sienna-400', value: primitiveColors.sienna400 },
  sienna500: { name: '--ajds-color-sienna-500', value: primitiveColors.sienna500 },
  sienna600: { name: '--ajds-color-sienna-600', value: primitiveColors.sienna600 },
  ocean50: { name: '--ajds-color-ocean-50', value: primitiveColors.ocean50 },
  ocean100: { name: '--ajds-color-ocean-100', value: primitiveColors.ocean100 },
  ocean200: { name: '--ajds-color-ocean-200', value: primitiveColors.ocean200 },
  ocean300: { name: '--ajds-color-ocean-300', value: primitiveColors.ocean300 },
  ocean400: { name: '--ajds-color-ocean-400', value: primitiveColors.ocean400 },
  ocean500: { name: '--ajds-color-ocean-500', value: primitiveColors.ocean500 },
} as const;

const cssSemanticColorVarMap = {
  characterPrimary: { name: '--ajds-color-character-primary', value: colors.characterPrimary },
  characterSecondary: { name: '--ajds-color-character-secondary', value: colors.characterSecondary },
  characterPrimaryWhite: { name: '--ajds-color-character-primary-white', value: colors.characterPrimaryWhite },
  characterSecondaryWhite: { name: '--ajds-color-character-secondary-white', value: colors.characterSecondaryWhite },
  characterAccent: { name: '--ajds-color-character-accent', value: colors.characterAccent },

  statusSuccess: { name: '--ajds-color-status-success', value: colors.statusSuccess },
  statusSuccessLight: { name: '--ajds-color-status-success-light', value: colors.statusSuccessLight },
  statusDanger: { name: '--ajds-color-status-danger', value: colors.statusDanger },
  statusDangerLight: { name: '--ajds-color-status-danger-light', value: colors.statusDangerLight },
  statusWarning: { name: '--ajds-color-status-warning', value: colors.statusWarning },
  statusWarningLight: { name: '--ajds-color-status-warning-light', value: colors.statusWarningLight },
  statusWarningDark: { name: '--ajds-color-status-warning-dark', value: colors.statusWarningDark },
  statusImportant: { name: '--ajds-color-status-important', value: colors.statusImportant },
  statusImportantLight: { name: '--ajds-color-status-important-light', value: colors.statusImportantLight },
  statusInformation: { name: '--ajds-color-status-information', value: colors.statusInformation },
  statusInformationLight: { name: '--ajds-color-status-information-light', value: colors.statusInformationLight },
  statusNeutral: { name: '--ajds-color-status-neutral', value: colors.statusNeutral },
  statusNeutralLight: { name: '--ajds-color-status-neutral-light', value: colors.statusNeutralLight },
  statusNeutralDark: { name: '--ajds-color-status-neutral-dark', value: colors.statusNeutralDark },

  utilityBackgroundWhite: { name: '--ajds-color-utility-background-white', value: colors.utilityBackgroundWhite },
  utilityBackgroundLight: { name: '--ajds-color-utility-background-light', value: colors.utilityBackgroundLight },
  utilityBackgroundOcean: { name: '--ajds-color-utility-background-ocean', value: colors.utilityBackgroundOcean },
  utilityBackgroundGrey: { name: '--ajds-color-utility-background-grey', value: colors.utilityBackgroundGrey },
  utilityStrokeLight: { name: '--ajds-color-utility-stroke-light', value: colors.utilityStrokeLight },
  utilityStrokeDark: { name: '--ajds-color-utility-stroke-dark', value: colors.utilityStrokeDark },
  utilityStrokeWhite: { name: '--ajds-color-utility-stroke-white', value: colors.utilityStrokeWhite },
  utilityOverlay: { name: '--ajds-color-utility-overlay', value: colors.utilityOverlay },

  interactiveActivePrimary: { name: '--ajds-color-interactive-active-primary', value: colors.interactiveActivePrimary },
  interactiveActiveWhite: { name: '--ajds-color-interactive-active-white', value: colors.interactiveActiveWhite },
  interactiveActiveGrey: { name: '--ajds-color-interactive-active-grey', value: colors.interactiveActiveGrey },
  interactiveHoverPrimary: { name: '--ajds-color-interactive-hover-primary', value: colors.interactiveHoverPrimary },
  interactiveHoverPrimaryTransparent: {
    name: '--ajds-color-interactive-hover-primary-transparent',
    value: colors.interactiveHoverPrimaryTransparent,
  },
  interactiveHoverWhiteTransparent: {
    name: '--ajds-color-interactive-hover-white-transparent',
    value: colors.interactiveHoverWhiteTransparent,
  },
  interactiveHoverGreyTransparent: { name: '--ajds-color-interactive-hover-grey-transparent', value: colors.interactiveHoverGreyTransparent },
  interactiveHoverGrey: { name: '--ajds-color-interactive-hover-grey', value: colors.interactiveHoverGrey },
  interactiveFocusPrimary: { name: '--ajds-color-interactive-focus-primary', value: colors.interactiveFocusPrimary },
  interactiveFocusWhite: { name: '--ajds-color-interactive-focus-white', value: colors.interactiveFocusWhite },
  interactiveVisitedOcean: { name: '--ajds-color-interactive-visited-ocean', value: colors.interactiveVisitedOcean },
  interactiveVisitedGrey: { name: '--ajds-color-interactive-visited-grey', value: colors.interactiveVisitedGrey },
  interactiveDisabledDark: { name: '--ajds-color-interactive-disabled-dark', value: colors.interactiveDisabledDark },
  interactiveDisabledLight: { name: '--ajds-color-interactive-disabled-light', value: colors.interactiveDisabledLight },
  interactivePlaceholder: { name: '--ajds-color-interactive-placeholder', value: colors.interactivePlaceholder },
} as const;

export const cssColorVarMap = { ...cssPrimitiveColorVarMap, ...cssSemanticColorVarMap };

export function getColorVar(key: keyof typeof cssColorVarMap): string {
  return `var(${(cssColorVarMap[key] || {}).name})`;
}

export default colors;

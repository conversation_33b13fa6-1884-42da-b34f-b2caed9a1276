// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FieldInputWrap > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c0 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c0 input,
.c0 select,
.c0 button,
.c0 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c0 input {
  overflow: visible;
}

.c0 select {
  text-transform: none;
}

.c0 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0 field-input-wrap"
  >
    Field Input Wrap
  </div>
</div>
`;

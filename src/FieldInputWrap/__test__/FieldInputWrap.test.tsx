import React from 'react';
import { render } from '../../utils/testUtils';
import FieldInputWrap from '../FieldInputWrap';

describe('FieldInputWrap', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<FieldInputWrap>Field Input Wrap</FieldInputWrap>);
    const testElement = getByText('Field Input Wrap');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

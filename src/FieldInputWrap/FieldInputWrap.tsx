import React from 'react';
import { FieldInputWrapBase } from './FieldInputWrapBase';

type FieldInputWrapProps = React.ComponentPropsWithoutRef<'div'>;

const FieldInputWrap: React.FC<FieldInputWrapProps> = ({ children, ...rest }) => {
  return (
    // This class is used for selection purposes, used in input.ts
    <FieldInputWrapBase className="field-input-wrap" {...rest}>
      {children}
    </FieldInputWrapBase>
  );
};

export default FieldInputWrap;

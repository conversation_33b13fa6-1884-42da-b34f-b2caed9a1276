import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';

export const FieldInputWrapBase = styled.div`
  display: flex;
  align-items: center;
  gap: ${getSpacingVar(2)};
  width: 100%;

  /**
    * Make container fit width of inputs.
    */
  label {
    width: fit-content;
    max-width: 100%;
  }

  input,
  select,
  button,
  textarea {
    font-family: inherit; /* 1 */
    font-size: 100%; /* 1 */
    line-height: 1.5; /* 1 */
    margin: 0; /* 2 */
    color: ${getColorVar('characterPrimary')};
  }

  /**
    * Show the overflow in IE.
    * 1. Show the overflow in Edge.
    */
  input {
    /* 1 */
    overflow: visible;
  }

  /**
    * Remove the inheritance of text transform in Edge, Firefox, and IE.
    * 1. Remove the inheritance of text transform in Firefox.
    */
  select {
    /* 1 */
    text-transform: none;
  }

  /**
    * Remove the default vertical scrollbar in IE 10+.
    */
  textarea {
    overflow: auto;
  }
`;

export default FieldInputWrapBase;

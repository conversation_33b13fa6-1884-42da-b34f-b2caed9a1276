import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type DoubleChevronIconProps = CommonIconProps & {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180' | '270';
};

const DoubleChevronIcon: React.FC<DoubleChevronIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      />
    </Icon>
  );
};

export default DoubleChevronIcon;

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type FacebookIconProps = CommonIconProps;

const FacebookIcon: React.FC<FacebookIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z" />
    </Icon>
  );
};

export default FacebookIcon;

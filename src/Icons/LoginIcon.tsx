import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type LoginIconProps = CommonIconProps;

const LoginIcon: React.FC<LoginIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M11.745 11.4503C10.5426 11.4503 9.55875 11.056 8.79356 10.2674C8.02837 9.47875 7.64578 8.46484 7.64578 7.22561C7.64578 5.98639 8.02837 4.97247 8.79356 4.18388C9.55875 3.39528 10.5426 3.00098 11.745 3.00098C12.9474 3.00098 13.9312 3.39528 14.6964 4.18388C15.4616 4.97247 15.8442 5.98639 15.8442 7.22561C15.8442 8.46484 15.4616 9.47875 14.6964 10.2674C13.9312 11.056 12.9474 11.4503 11.745 11.4503ZM3 20.491V17.8435C3 17.13 3.17308 16.5198 3.51923 16.0129C3.86539 15.5059 4.31175 15.121 4.85831 14.8581C6.07897 14.2948 7.24952 13.8724 8.36998 13.5907C9.49043 13.3091 10.6154 13.1683 11.745 13.1683C12.8746 13.1683 13.995 13.3138 15.1064 13.6048C16.2177 13.8958 17.3837 14.3136 18.6044 14.8581C19.1691 15.121 19.6246 15.5059 19.9708 16.0129C20.3169 16.5198 20.49 17.13 20.49 17.8435V20.491H3ZM4.63969 18.8011H18.8503V17.8435C18.8503 17.5431 18.7638 17.2568 18.5907 16.9845C18.4176 16.7123 18.2035 16.5104 17.9485 16.379C16.7825 15.7969 15.7167 15.3979 14.7511 15.182C13.7855 14.9661 12.7835 14.8581 11.745 14.8581C10.7065 14.8581 9.69539 14.9661 8.71158 15.182C7.72777 15.3979 6.66197 15.7969 5.51419 16.379C5.25912 16.5104 5.04961 16.7123 4.88564 16.9845C4.72167 17.2568 4.63969 17.5431 4.63969 17.8435V18.8011ZM11.745 9.7604C12.4555 9.7604 13.0431 9.521 13.5077 9.04221C13.9722 8.56342 14.2045 7.95789 14.2045 7.22561C14.2045 6.49334 13.9722 5.88781 13.5077 5.40902C13.0431 4.93023 12.4555 4.69083 11.745 4.69083C11.0345 4.69083 10.4469 4.93023 9.98234 5.40902C9.51776 5.88781 9.28547 6.49334 9.28547 7.22561C9.28547 7.95789 9.51776 8.56342 9.98234 9.04221C10.4469 9.521 11.0345 9.7604 11.745 9.7604Z" />
    </Icon>
  );
};

export default LoginIcon;

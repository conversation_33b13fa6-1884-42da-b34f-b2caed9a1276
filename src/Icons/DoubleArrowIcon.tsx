import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type DoubleArrowIconProps = CommonIconProps & {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180';
};

const DoubleArrowIcon: React.FC<DoubleArrowIconProps> = (props) => {
  return (
    <Icon {...props}>
      <g clipPath="url(#clip0_2806_7818)">
        <path d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z" />
      </g>
      <defs>
        <clipPath id="clip0_2806_7818">
          <rect width="24" height="24" fill="white" transform="translate(0 0.000732422)" />
        </clipPath>
      </defs>
    </Icon>
  );
};

export default DoubleArrowIcon;

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type InstagramIconProps = CommonIconProps;

const InstagramIcon: React.FC<InstagramIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z" />
    </Icon>
  );
};

export default InstagramIcon;

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type WorldIconProps = CommonIconProps;

const WorldIcon: React.FC<WorldIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M12 2.00098C6.48 2.00098 2 6.48098 2 12.001C2 17.521 6.48 22.001 12 22.001C17.52 22.001 22 17.521 22 12.001C22 6.48098 17.52 2.00098 12 2.00098ZM4 12.001C4 11.391 4.08 10.791 4.21 10.221L8.99 15.001V16.001C8.99 17.101 9.89 18.001 10.99 18.001V19.931C7.06 19.431 4 16.071 4 12.001ZM17.89 17.401C17.63 16.591 16.89 16.001 15.99 16.001H14.99V13.001C14.99 12.451 14.54 12.001 13.99 12.001H7.99V10.001H9.99C10.54 10.001 10.99 9.55098 10.99 9.00098V7.00098H12.99C14.09 7.00098 14.99 6.10098 14.99 5.00098V4.59098C17.92 5.77098 20 8.65098 20 12.001C20 14.081 19.19 15.981 17.89 17.401Z" />
    </Icon>
  );
};

export default WorldIcon;

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Icons > renders Icon with isOutline true without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with isOutline true without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with isOutline true without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with isStroke true without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
    />
  </svg>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 0 without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 0 without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 0 without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 0 without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 90 without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 90 without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 90 without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 90 without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 180 without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 180 without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 180 without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 180 without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 270 without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(270deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 270 without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(270deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 270 without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(270deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with rotation 270 without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(270deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 5`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C16.968 3.00073 21 7.03273 21 12.0007C20.9999 16.9686 16.9679 21.0007 12 21.0007C7.03207 21.0007 3.00012 16.9686 3 12.0007C3 7.03273 7.032 3.00073 12 3.00073ZM10.2324 13.6257L7.46289 10.8425L6.375 11.9392L10.2324 15.8259L17.1748 8.82983L16.0869 7.72534L10.2324 13.6257Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 6`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 7`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 8`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      clip-path="url(#clip0_2806_7818)"
    >
      <path
        d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
      />
    </g>
    <defs>
      <clippath
        id="clip0_2806_7818"
      >
        <rect
          fill="white"
          height="24"
          transform="translate(0 0.000732422)"
          width="24"
        />
      </clippath>
    </defs>
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 9`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 10`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 15.0007V18.0007H6V15.0007H4V18.0007C4 19.1007 4.9 20.0007 6 20.0007H18C19.1 20.0007 20 19.1007 20 18.0007V15.0007H18ZM17 11.0007L15.59 9.59073L13 12.1707V4.00073H11V12.1707L8.41 9.59073L7 11.0007L12 16.0007L17 11.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 11`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 12`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 13`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 14`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 18H16V16H8V18ZM8 14H16V12H8V14ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 15`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 16`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 17`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V10C4 9.45 4.19583 8.97917 4.5875 8.5875C4.97917 8.19583 5.45 8 6 8H7V6C7 4.61667 7.4875 3.4375 8.4625 2.4625C9.4375 1.4875 10.6167 1 12 1C13.3833 1 14.5625 1.4875 15.5375 2.4625C16.5125 3.4375 17 4.61667 17 6V8H18C18.55 8 19.0208 8.19583 19.4125 8.5875C19.8042 8.97917 20 9.45 20 10V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM6 20H18V10H6V20ZM12 17C12.55 17 13.0208 16.8042 13.4125 16.4125C13.8042 16.0208 14 15.55 14 15C14 14.45 13.8042 13.9792 13.4125 13.5875C13.0208 13.1958 12.55 13 12 13C11.45 13 10.9792 13.1958 10.5875 13.5875C10.1958 13.9792 10 14.45 10 15C10 15.55 10.1958 16.0208 10.5875 16.4125C10.9792 16.8042 11.45 17 12 17ZM9 8H15V6C15 5.16667 14.7083 4.45833 14.125 3.875C13.5417 3.29167 12.8333 3 12 3C11.1667 3 10.4583 3.29167 9.875 3.875C9.29167 4.45833 9 5.16667 9 6V8Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 18`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 19`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.745 11.4503C10.5426 11.4503 9.55875 11.056 8.79356 10.2674C8.02837 9.47875 7.64578 8.46484 7.64578 7.22561C7.64578 5.98639 8.02837 4.97247 8.79356 4.18388C9.55875 3.39528 10.5426 3.00098 11.745 3.00098C12.9474 3.00098 13.9312 3.39528 14.6964 4.18388C15.4616 4.97247 15.8442 5.98639 15.8442 7.22561C15.8442 8.46484 15.4616 9.47875 14.6964 10.2674C13.9312 11.056 12.9474 11.4503 11.745 11.4503ZM3 20.491V17.8435C3 17.13 3.17308 16.5198 3.51923 16.0129C3.86539 15.5059 4.31175 15.121 4.85831 14.8581C6.07897 14.2948 7.24952 13.8724 8.36998 13.5907C9.49043 13.3091 10.6154 13.1683 11.745 13.1683C12.8746 13.1683 13.995 13.3138 15.1064 13.6048C16.2177 13.8958 17.3837 14.3136 18.6044 14.8581C19.1691 15.121 19.6246 15.5059 19.9708 16.0129C20.3169 16.5198 20.49 17.13 20.49 17.8435V20.491H3ZM4.63969 18.8011H18.8503V17.8435C18.8503 17.5431 18.7638 17.2568 18.5907 16.9845C18.4176 16.7123 18.2035 16.5104 17.9485 16.379C16.7825 15.7969 15.7167 15.3979 14.7511 15.182C13.7855 14.9661 12.7835 14.8581 11.745 14.8581C10.7065 14.8581 9.69539 14.9661 8.71158 15.182C7.72777 15.3979 6.66197 15.7969 5.51419 16.379C5.25912 16.5104 5.04961 16.7123 4.88564 16.9845C4.72167 17.2568 4.63969 17.5431 4.63969 17.8435V18.8011ZM11.745 9.7604C12.4555 9.7604 13.0431 9.521 13.5077 9.04221C13.9722 8.56342 14.2045 7.95789 14.2045 7.22561C14.2045 6.49334 13.9722 5.88781 13.5077 5.40902C13.0431 4.93023 12.4555 4.69083 11.745 4.69083C11.0345 4.69083 10.4469 4.93023 9.98234 5.40902C9.51776 5.88781 9.28547 6.49334 9.28547 7.22561C9.28547 7.95789 9.51776 8.56342 9.98234 9.04221C10.4469 9.521 11.0345 9.7604 11.745 9.7604Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 20`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 6.00098C22 4.90098 21.1 4.00098 20 4.00098H4C2.9 4.00098 2 4.90098 2 6.00098V18.001C2 19.101 2.9 20.001 4 20.001H20C21.1 20.001 22 19.101 22 18.001V6.00098ZM20 6.00098L12 11.001L4 6.00098H20ZM20 18.001H4V8.00098L12 13.001L20 8.00098V18.001Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 21`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 18.001H21V16.001H3V18.001ZM3 13.001H21V11.001H3V13.001ZM3 6.00098V8.00098H21V6.00098H3Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 22`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.01 15.381C18.78 15.381 17.59 15.181 16.48 14.821C16.13 14.701 15.74 14.791 15.47 15.061L13.9 17.031C11.07 15.681 8.42 13.131 7.01 10.201L8.96 8.54098C9.23 8.26098 9.31 7.87098 9.2 7.52098C8.83 6.41098 8.64 5.22098 8.64 3.99098C8.64 3.45098 8.19 3.00098 7.65 3.00098H4.19C3.65 3.00098 3 3.24098 3 3.99098C3 13.281 10.73 21.001 20.01 21.001C20.72 21.001 21 20.371 21 19.821V16.371C21 15.831 20.55 15.381 20.01 15.381Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 23`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M14.71 14.001H15.5L20.49 19.001L19 20.491L14 15.501V14.711L13.73 14.431C12.59 15.411 11.11 16.001 9.5 16.001C5.91 16.001 3 13.091 3 9.50098C3 5.91098 5.91 3.00098 9.5 3.00098C13.09 3.00098 16 5.91098 16 9.50098C16 11.111 15.41 12.591 14.43 13.731L14.71 14.001ZM5 9.50098C5 11.991 7.01 14.001 9.5 14.001C11.99 14.001 14 11.991 14 9.50098C14 7.01098 11.99 5.00098 9.5 5.00098C7.01 5.00098 5 7.01098 5 9.50098Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 24`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 25`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 19H13V14.825L14.6 16.425L16 15L12 11L8 15L9.425 16.4L11 14.825V19ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 26`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 27`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 28`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2.00098C6.48 2.00098 2 6.48098 2 12.001C2 17.521 6.48 22.001 12 22.001C17.52 22.001 22 17.521 22 12.001C22 6.48098 17.52 2.00098 12 2.00098ZM4 12.001C4 11.391 4.08 10.791 4.21 10.221L8.99 15.001V16.001C8.99 17.101 9.89 18.001 10.99 18.001V19.931C7.06 19.431 4 16.071 4 12.001ZM17.89 17.401C17.63 16.591 16.89 16.001 15.99 16.001H14.99V13.001C14.99 12.451 14.54 12.001 13.99 12.001H7.99V10.001H9.99C10.54 10.001 10.99 9.55098 10.99 9.00098V7.00098H12.99C14.09 7.00098 14.99 6.10098 14.99 5.00098V4.59098C17.92 5.77098 20 8.65098 20 12.001C20 14.081 19.19 15.981 17.89 17.401Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 29`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 30`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.79412 17.4C9.30882 17.4 8.89338 17.2238 8.54779 16.8713C8.20221 16.5188 8.02941 16.095 8.02941 15.6V4.8C8.02941 4.305 8.20221 3.88125 8.54779 3.52875C8.89338 3.17625 9.30882 3 9.79412 3H17.7353C18.2206 3 18.636 3.17625 18.9816 3.52875C19.3272 3.88125 19.5 4.305 19.5 4.8V15.6C19.5 16.095 19.3272 16.5188 18.9816 16.8713C18.636 17.2238 18.2206 17.4 17.7353 17.4H9.79412ZM9.79412 15.6H17.7353V4.8H9.79412V15.6ZM6.26471 21C5.77941 21 5.36397 20.8238 5.01838 20.4713C4.67279 20.1188 4.5 19.695 4.5 19.2V6.6H6.26471V19.2H15.9706V21H6.26471Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 31`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 23C6.45 23 5.97917 22.8042 5.5875 22.4125C5.19583 22.0208 5 21.55 5 21V3C5 2.45 5.19583 1.97917 5.5875 1.5875C5.97917 1.19583 6.45 1 7 1H17C17.55 1 18.0208 1.19583 18.4125 1.5875C18.8042 1.97917 19 2.45 19 3V21C19 21.55 18.8042 22.0208 18.4125 22.4125C18.0208 22.8042 17.55 23 17 23H7ZM7 20V21H17V20H7ZM7 18H17V6H7V18ZM7 4H17V3H7V4Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 32`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19ZM6 17H18L14.25 12L11.25 16L9 13L6 17Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 33`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.95 18C12.3 18 12.5958 17.8792 12.8375 17.6375C13.0792 17.3958 13.2 17.1 13.2 16.75C13.2 16.4 13.0792 16.1042 12.8375 15.8625C12.5958 15.6208 12.3 15.5 11.95 15.5C11.6 15.5 11.3042 15.6208 11.0625 15.8625C10.8208 16.1042 10.7 16.4 10.7 16.75C10.7 17.1 10.8208 17.3958 11.0625 17.6375C11.3042 17.8792 11.6 18 11.95 18ZM11.05 14.15H12.9C12.9 13.6 12.9625 13.1667 13.0875 12.85C13.2125 12.5333 13.5667 12.1 14.15 11.55C14.5833 11.1167 14.925 10.7042 15.175 10.3125C15.425 9.92083 15.55 9.45 15.55 8.9C15.55 7.96667 15.2083 7.25 14.525 6.75C13.8417 6.25 13.0333 6 12.1 6C11.15 6 10.3792 6.25 9.7875 6.75C9.19583 7.25 8.78333 7.85 8.55 8.55L10.2 9.2C10.2833 8.9 10.4708 8.575 10.7625 8.225C11.0542 7.875 11.5 7.7 12.1 7.7C12.6333 7.7 13.0333 7.84583 13.3 8.1375C13.5667 8.42917 13.7 8.75 13.7 9.1C13.7 9.43333 13.6 9.74583 13.4 10.0375C13.2 10.3292 12.95 10.6 12.65 10.85C11.9167 11.5 11.4667 11.9917 11.3 12.325C11.1333 12.6583 11.05 13.2667 11.05 14.15ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size large without crashing 34`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-8); --ajds-IconBase-width: var(--ajds-spacing-8); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9 17C12.25 17 12.5458 16.8792 12.7875 16.6375C13.0292 16.3958 13.15 16.1 13.15 15.75C13.15 15.4 13.0292 15.1042 12.7875 14.8625C12.5458 14.6208 12.25 14.5 11.9 14.5C11.55 14.5 11.2542 14.6208 11.0125 14.8625C10.7708 15.1042 10.65 15.4 10.65 15.75C10.65 16.1 10.7708 16.3958 11.0125 16.6375C11.2542 16.8792 11.55 17 11.9 17ZM11 13.15H12.85C12.85 12.8667 12.8625 12.625 12.8875 12.425C12.9125 12.225 12.9667 12.0333 13.05 11.85C13.1333 11.6667 13.2375 11.4958 13.3625 11.3375C13.4875 11.1792 13.6667 10.9833 13.9 10.75C14.4833 10.1667 14.8958 9.67917 15.1375 9.2875C15.3792 8.89583 15.5 8.45 15.5 7.95C15.5 7.06667 15.2 6.35417 14.6 5.8125C14 5.27083 13.1917 5 12.175 5C11.2583 5 10.4792 5.225 9.8375 5.675C9.19583 6.125 8.75 6.75 8.5 7.55L10.15 8.2C10.2667 7.75 10.5 7.3875 10.85 7.1125C11.2 6.8375 11.6083 6.7 12.075 6.7C12.525 6.7 12.9 6.82083 13.2 7.0625C13.5 7.30417 13.65 7.625 13.65 8.025C13.65 8.30833 13.5583 8.60833 13.375 8.925C13.1917 9.24167 12.8833 9.59167 12.45 9.975C12.1667 10.2083 11.9375 10.4375 11.7625 10.6625C11.5875 10.8875 11.4417 11.125 11.325 11.375C11.2083 11.625 11.125 11.8875 11.075 12.1625C11.025 12.4375 11 12.7667 11 13.15ZM12 23L9 20H5C4.45 20 3.97917 19.8042 3.5875 19.4125C3.19583 19.0208 3 18.55 3 18V4C3 3.45 3.19583 2.97917 3.5875 2.5875C3.97917 2.19583 4.45 2 5 2H19C19.55 2 20.0208 2.19583 20.4125 2.5875C20.8042 2.97917 21 3.45 21 4V18C21 18.55 20.8042 19.0208 20.4125 19.4125C20.0208 19.8042 19.55 20 19 20H15L12 23ZM5 18H9.8L12 20.2L14.2 18H19V4H5V18Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 5`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C16.968 3.00073 21 7.03273 21 12.0007C20.9999 16.9686 16.9679 21.0007 12 21.0007C7.03207 21.0007 3.00012 16.9686 3 12.0007C3 7.03273 7.032 3.00073 12 3.00073ZM10.2324 13.6257L7.46289 10.8425L6.375 11.9392L10.2324 15.8259L17.1748 8.82983L16.0869 7.72534L10.2324 13.6257Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 6`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 7`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 8`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      clip-path="url(#clip0_2806_7818)"
    >
      <path
        d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
      />
    </g>
    <defs>
      <clippath
        id="clip0_2806_7818"
      >
        <rect
          fill="white"
          height="24"
          transform="translate(0 0.000732422)"
          width="24"
        />
      </clippath>
    </defs>
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 9`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 10`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 15.0007V18.0007H6V15.0007H4V18.0007C4 19.1007 4.9 20.0007 6 20.0007H18C19.1 20.0007 20 19.1007 20 18.0007V15.0007H18ZM17 11.0007L15.59 9.59073L13 12.1707V4.00073H11V12.1707L8.41 9.59073L7 11.0007L12 16.0007L17 11.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 11`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 12`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 13`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 14`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 18H16V16H8V18ZM8 14H16V12H8V14ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 15`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 16`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 17`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V10C4 9.45 4.19583 8.97917 4.5875 8.5875C4.97917 8.19583 5.45 8 6 8H7V6C7 4.61667 7.4875 3.4375 8.4625 2.4625C9.4375 1.4875 10.6167 1 12 1C13.3833 1 14.5625 1.4875 15.5375 2.4625C16.5125 3.4375 17 4.61667 17 6V8H18C18.55 8 19.0208 8.19583 19.4125 8.5875C19.8042 8.97917 20 9.45 20 10V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM6 20H18V10H6V20ZM12 17C12.55 17 13.0208 16.8042 13.4125 16.4125C13.8042 16.0208 14 15.55 14 15C14 14.45 13.8042 13.9792 13.4125 13.5875C13.0208 13.1958 12.55 13 12 13C11.45 13 10.9792 13.1958 10.5875 13.5875C10.1958 13.9792 10 14.45 10 15C10 15.55 10.1958 16.0208 10.5875 16.4125C10.9792 16.8042 11.45 17 12 17ZM9 8H15V6C15 5.16667 14.7083 4.45833 14.125 3.875C13.5417 3.29167 12.8333 3 12 3C11.1667 3 10.4583 3.29167 9.875 3.875C9.29167 4.45833 9 5.16667 9 6V8Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 18`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 19`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.745 11.4503C10.5426 11.4503 9.55875 11.056 8.79356 10.2674C8.02837 9.47875 7.64578 8.46484 7.64578 7.22561C7.64578 5.98639 8.02837 4.97247 8.79356 4.18388C9.55875 3.39528 10.5426 3.00098 11.745 3.00098C12.9474 3.00098 13.9312 3.39528 14.6964 4.18388C15.4616 4.97247 15.8442 5.98639 15.8442 7.22561C15.8442 8.46484 15.4616 9.47875 14.6964 10.2674C13.9312 11.056 12.9474 11.4503 11.745 11.4503ZM3 20.491V17.8435C3 17.13 3.17308 16.5198 3.51923 16.0129C3.86539 15.5059 4.31175 15.121 4.85831 14.8581C6.07897 14.2948 7.24952 13.8724 8.36998 13.5907C9.49043 13.3091 10.6154 13.1683 11.745 13.1683C12.8746 13.1683 13.995 13.3138 15.1064 13.6048C16.2177 13.8958 17.3837 14.3136 18.6044 14.8581C19.1691 15.121 19.6246 15.5059 19.9708 16.0129C20.3169 16.5198 20.49 17.13 20.49 17.8435V20.491H3ZM4.63969 18.8011H18.8503V17.8435C18.8503 17.5431 18.7638 17.2568 18.5907 16.9845C18.4176 16.7123 18.2035 16.5104 17.9485 16.379C16.7825 15.7969 15.7167 15.3979 14.7511 15.182C13.7855 14.9661 12.7835 14.8581 11.745 14.8581C10.7065 14.8581 9.69539 14.9661 8.71158 15.182C7.72777 15.3979 6.66197 15.7969 5.51419 16.379C5.25912 16.5104 5.04961 16.7123 4.88564 16.9845C4.72167 17.2568 4.63969 17.5431 4.63969 17.8435V18.8011ZM11.745 9.7604C12.4555 9.7604 13.0431 9.521 13.5077 9.04221C13.9722 8.56342 14.2045 7.95789 14.2045 7.22561C14.2045 6.49334 13.9722 5.88781 13.5077 5.40902C13.0431 4.93023 12.4555 4.69083 11.745 4.69083C11.0345 4.69083 10.4469 4.93023 9.98234 5.40902C9.51776 5.88781 9.28547 6.49334 9.28547 7.22561C9.28547 7.95789 9.51776 8.56342 9.98234 9.04221C10.4469 9.521 11.0345 9.7604 11.745 9.7604Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 20`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 6.00098C22 4.90098 21.1 4.00098 20 4.00098H4C2.9 4.00098 2 4.90098 2 6.00098V18.001C2 19.101 2.9 20.001 4 20.001H20C21.1 20.001 22 19.101 22 18.001V6.00098ZM20 6.00098L12 11.001L4 6.00098H20ZM20 18.001H4V8.00098L12 13.001L20 8.00098V18.001Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 21`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 18.001H21V16.001H3V18.001ZM3 13.001H21V11.001H3V13.001ZM3 6.00098V8.00098H21V6.00098H3Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 22`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.01 15.381C18.78 15.381 17.59 15.181 16.48 14.821C16.13 14.701 15.74 14.791 15.47 15.061L13.9 17.031C11.07 15.681 8.42 13.131 7.01 10.201L8.96 8.54098C9.23 8.26098 9.31 7.87098 9.2 7.52098C8.83 6.41098 8.64 5.22098 8.64 3.99098C8.64 3.45098 8.19 3.00098 7.65 3.00098H4.19C3.65 3.00098 3 3.24098 3 3.99098C3 13.281 10.73 21.001 20.01 21.001C20.72 21.001 21 20.371 21 19.821V16.371C21 15.831 20.55 15.381 20.01 15.381Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 23`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M14.71 14.001H15.5L20.49 19.001L19 20.491L14 15.501V14.711L13.73 14.431C12.59 15.411 11.11 16.001 9.5 16.001C5.91 16.001 3 13.091 3 9.50098C3 5.91098 5.91 3.00098 9.5 3.00098C13.09 3.00098 16 5.91098 16 9.50098C16 11.111 15.41 12.591 14.43 13.731L14.71 14.001ZM5 9.50098C5 11.991 7.01 14.001 9.5 14.001C11.99 14.001 14 11.991 14 9.50098C14 7.01098 11.99 5.00098 9.5 5.00098C7.01 5.00098 5 7.01098 5 9.50098Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 24`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 25`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 19H13V14.825L14.6 16.425L16 15L12 11L8 15L9.425 16.4L11 14.825V19ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 26`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 27`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 28`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2.00098C6.48 2.00098 2 6.48098 2 12.001C2 17.521 6.48 22.001 12 22.001C17.52 22.001 22 17.521 22 12.001C22 6.48098 17.52 2.00098 12 2.00098ZM4 12.001C4 11.391 4.08 10.791 4.21 10.221L8.99 15.001V16.001C8.99 17.101 9.89 18.001 10.99 18.001V19.931C7.06 19.431 4 16.071 4 12.001ZM17.89 17.401C17.63 16.591 16.89 16.001 15.99 16.001H14.99V13.001C14.99 12.451 14.54 12.001 13.99 12.001H7.99V10.001H9.99C10.54 10.001 10.99 9.55098 10.99 9.00098V7.00098H12.99C14.09 7.00098 14.99 6.10098 14.99 5.00098V4.59098C17.92 5.77098 20 8.65098 20 12.001C20 14.081 19.19 15.981 17.89 17.401Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 29`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 30`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.79412 17.4C9.30882 17.4 8.89338 17.2238 8.54779 16.8713C8.20221 16.5188 8.02941 16.095 8.02941 15.6V4.8C8.02941 4.305 8.20221 3.88125 8.54779 3.52875C8.89338 3.17625 9.30882 3 9.79412 3H17.7353C18.2206 3 18.636 3.17625 18.9816 3.52875C19.3272 3.88125 19.5 4.305 19.5 4.8V15.6C19.5 16.095 19.3272 16.5188 18.9816 16.8713C18.636 17.2238 18.2206 17.4 17.7353 17.4H9.79412ZM9.79412 15.6H17.7353V4.8H9.79412V15.6ZM6.26471 21C5.77941 21 5.36397 20.8238 5.01838 20.4713C4.67279 20.1188 4.5 19.695 4.5 19.2V6.6H6.26471V19.2H15.9706V21H6.26471Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 31`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 23C6.45 23 5.97917 22.8042 5.5875 22.4125C5.19583 22.0208 5 21.55 5 21V3C5 2.45 5.19583 1.97917 5.5875 1.5875C5.97917 1.19583 6.45 1 7 1H17C17.55 1 18.0208 1.19583 18.4125 1.5875C18.8042 1.97917 19 2.45 19 3V21C19 21.55 18.8042 22.0208 18.4125 22.4125C18.0208 22.8042 17.55 23 17 23H7ZM7 20V21H17V20H7ZM7 18H17V6H7V18ZM7 4H17V3H7V4Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 32`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19ZM6 17H18L14.25 12L11.25 16L9 13L6 17Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 33`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.95 18C12.3 18 12.5958 17.8792 12.8375 17.6375C13.0792 17.3958 13.2 17.1 13.2 16.75C13.2 16.4 13.0792 16.1042 12.8375 15.8625C12.5958 15.6208 12.3 15.5 11.95 15.5C11.6 15.5 11.3042 15.6208 11.0625 15.8625C10.8208 16.1042 10.7 16.4 10.7 16.75C10.7 17.1 10.8208 17.3958 11.0625 17.6375C11.3042 17.8792 11.6 18 11.95 18ZM11.05 14.15H12.9C12.9 13.6 12.9625 13.1667 13.0875 12.85C13.2125 12.5333 13.5667 12.1 14.15 11.55C14.5833 11.1167 14.925 10.7042 15.175 10.3125C15.425 9.92083 15.55 9.45 15.55 8.9C15.55 7.96667 15.2083 7.25 14.525 6.75C13.8417 6.25 13.0333 6 12.1 6C11.15 6 10.3792 6.25 9.7875 6.75C9.19583 7.25 8.78333 7.85 8.55 8.55L10.2 9.2C10.2833 8.9 10.4708 8.575 10.7625 8.225C11.0542 7.875 11.5 7.7 12.1 7.7C12.6333 7.7 13.0333 7.84583 13.3 8.1375C13.5667 8.42917 13.7 8.75 13.7 9.1C13.7 9.43333 13.6 9.74583 13.4 10.0375C13.2 10.3292 12.95 10.6 12.65 10.85C11.9167 11.5 11.4667 11.9917 11.3 12.325C11.1333 12.6583 11.05 13.2667 11.05 14.15ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size medium without crashing 34`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9 17C12.25 17 12.5458 16.8792 12.7875 16.6375C13.0292 16.3958 13.15 16.1 13.15 15.75C13.15 15.4 13.0292 15.1042 12.7875 14.8625C12.5458 14.6208 12.25 14.5 11.9 14.5C11.55 14.5 11.2542 14.6208 11.0125 14.8625C10.7708 15.1042 10.65 15.4 10.65 15.75C10.65 16.1 10.7708 16.3958 11.0125 16.6375C11.2542 16.8792 11.55 17 11.9 17ZM11 13.15H12.85C12.85 12.8667 12.8625 12.625 12.8875 12.425C12.9125 12.225 12.9667 12.0333 13.05 11.85C13.1333 11.6667 13.2375 11.4958 13.3625 11.3375C13.4875 11.1792 13.6667 10.9833 13.9 10.75C14.4833 10.1667 14.8958 9.67917 15.1375 9.2875C15.3792 8.89583 15.5 8.45 15.5 7.95C15.5 7.06667 15.2 6.35417 14.6 5.8125C14 5.27083 13.1917 5 12.175 5C11.2583 5 10.4792 5.225 9.8375 5.675C9.19583 6.125 8.75 6.75 8.5 7.55L10.15 8.2C10.2667 7.75 10.5 7.3875 10.85 7.1125C11.2 6.8375 11.6083 6.7 12.075 6.7C12.525 6.7 12.9 6.82083 13.2 7.0625C13.5 7.30417 13.65 7.625 13.65 8.025C13.65 8.30833 13.5583 8.60833 13.375 8.925C13.1917 9.24167 12.8833 9.59167 12.45 9.975C12.1667 10.2083 11.9375 10.4375 11.7625 10.6625C11.5875 10.8875 11.4417 11.125 11.325 11.375C11.2083 11.625 11.125 11.8875 11.075 12.1625C11.025 12.4375 11 12.7667 11 13.15ZM12 23L9 20H5C4.45 20 3.97917 19.8042 3.5875 19.4125C3.19583 19.0208 3 18.55 3 18V4C3 3.45 3.19583 2.97917 3.5875 2.5875C3.97917 2.19583 4.45 2 5 2H19C19.55 2 20.0208 2.19583 20.4125 2.5875C20.8042 2.97917 21 3.45 21 4V18C21 18.55 20.8042 19.0208 20.4125 19.4125C20.0208 19.8042 19.55 20 19 20H15L12 23ZM5 18H9.8L12 20.2L14.2 18H19V4H5V18Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 1`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 2`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 3`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 3.00073H19V1.00073H17V3.00073H7V1.00073H5V3.00073H4C2.9 3.00073 2 3.90073 2 5.00073V21.0007C2 22.1007 2.9 23.0007 4 23.0007H20C21.1 23.0007 22 22.1007 22 21.0007V5.00073C22 3.90073 21.1 3.00073 20 3.00073ZM20 21.0007H4V8.00073H20V21.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 4`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 5`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C16.968 3.00073 21 7.03273 21 12.0007C20.9999 16.9686 16.9679 21.0007 12 21.0007C7.03207 21.0007 3.00012 16.9686 3 12.0007C3 7.03273 7.032 3.00073 12 3.00073ZM10.2324 13.6257L7.46289 10.8425L6.375 11.9392L10.2324 15.8259L17.1748 8.82983L16.0869 7.72534L10.2324 13.6257Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 6`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 7`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 8`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g
      clip-path="url(#clip0_2806_7818)"
    >
      <path
        d="M8.5 13.0007V5.82573L5.925 8.40073L4.5 7.00073L9.5 2.00073L14.5 7.00073L13.075 8.40073L10.5 5.82573V13.0007H8.5ZM15.5 22.0007L10.5 17.0007L11.925 15.6007L14.5 18.1757V11.0007H16.5V18.1757L19.075 15.6007L20.5 17.0007L15.5 22.0007Z"
      />
    </g>
    <defs>
      <clippath
        id="clip0_2806_7818"
      >
        <rect
          fill="white"
          height="24"
          transform="translate(0 0.000732422)"
          width="24"
        />
      </clippath>
    </defs>
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 9`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M6 16.59L7.41 18L13.41 12L7.41 6L6 7.41L10.58 12L6 16.59ZM11.3133 16.59L12.7233 18L18.7233 12L12.7233 6L11.3133 7.41L15.8933 12L11.3133 16.59Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 10`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 15.0007V18.0007H6V15.0007H4V18.0007C4 19.1007 4.9 20.0007 6 20.0007H18C19.1 20.0007 20 19.1007 20 18.0007V15.0007H18ZM17 11.0007L15.59 9.59073L13 12.1707V4.00073H11V12.1707L8.41 9.59073L7 11.0007L12 16.0007L17 11.0007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 11`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 12`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 13`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 14`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 18H16V16H8V18ZM8 14H16V12H8V14ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 15`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 16`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 17`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V10C4 9.45 4.19583 8.97917 4.5875 8.5875C4.97917 8.19583 5.45 8 6 8H7V6C7 4.61667 7.4875 3.4375 8.4625 2.4625C9.4375 1.4875 10.6167 1 12 1C13.3833 1 14.5625 1.4875 15.5375 2.4625C16.5125 3.4375 17 4.61667 17 6V8H18C18.55 8 19.0208 8.19583 19.4125 8.5875C19.8042 8.97917 20 9.45 20 10V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM6 20H18V10H6V20ZM12 17C12.55 17 13.0208 16.8042 13.4125 16.4125C13.8042 16.0208 14 15.55 14 15C14 14.45 13.8042 13.9792 13.4125 13.5875C13.0208 13.1958 12.55 13 12 13C11.45 13 10.9792 13.1958 10.5875 13.5875C10.1958 13.9792 10 14.45 10 15C10 15.55 10.1958 16.0208 10.5875 16.4125C10.9792 16.8042 11.45 17 12 17ZM9 8H15V6C15 5.16667 14.7083 4.45833 14.125 3.875C13.5417 3.29167 12.8333 3 12 3C11.1667 3 10.4583 3.29167 9.875 3.875C9.29167 4.45833 9 5.16667 9 6V8Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 18`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 19`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.745 11.4503C10.5426 11.4503 9.55875 11.056 8.79356 10.2674C8.02837 9.47875 7.64578 8.46484 7.64578 7.22561C7.64578 5.98639 8.02837 4.97247 8.79356 4.18388C9.55875 3.39528 10.5426 3.00098 11.745 3.00098C12.9474 3.00098 13.9312 3.39528 14.6964 4.18388C15.4616 4.97247 15.8442 5.98639 15.8442 7.22561C15.8442 8.46484 15.4616 9.47875 14.6964 10.2674C13.9312 11.056 12.9474 11.4503 11.745 11.4503ZM3 20.491V17.8435C3 17.13 3.17308 16.5198 3.51923 16.0129C3.86539 15.5059 4.31175 15.121 4.85831 14.8581C6.07897 14.2948 7.24952 13.8724 8.36998 13.5907C9.49043 13.3091 10.6154 13.1683 11.745 13.1683C12.8746 13.1683 13.995 13.3138 15.1064 13.6048C16.2177 13.8958 17.3837 14.3136 18.6044 14.8581C19.1691 15.121 19.6246 15.5059 19.9708 16.0129C20.3169 16.5198 20.49 17.13 20.49 17.8435V20.491H3ZM4.63969 18.8011H18.8503V17.8435C18.8503 17.5431 18.7638 17.2568 18.5907 16.9845C18.4176 16.7123 18.2035 16.5104 17.9485 16.379C16.7825 15.7969 15.7167 15.3979 14.7511 15.182C13.7855 14.9661 12.7835 14.8581 11.745 14.8581C10.7065 14.8581 9.69539 14.9661 8.71158 15.182C7.72777 15.3979 6.66197 15.7969 5.51419 16.379C5.25912 16.5104 5.04961 16.7123 4.88564 16.9845C4.72167 17.2568 4.63969 17.5431 4.63969 17.8435V18.8011ZM11.745 9.7604C12.4555 9.7604 13.0431 9.521 13.5077 9.04221C13.9722 8.56342 14.2045 7.95789 14.2045 7.22561C14.2045 6.49334 13.9722 5.88781 13.5077 5.40902C13.0431 4.93023 12.4555 4.69083 11.745 4.69083C11.0345 4.69083 10.4469 4.93023 9.98234 5.40902C9.51776 5.88781 9.28547 6.49334 9.28547 7.22561C9.28547 7.95789 9.51776 8.56342 9.98234 9.04221C10.4469 9.521 11.0345 9.7604 11.745 9.7604Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 20`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 6.00098C22 4.90098 21.1 4.00098 20 4.00098H4C2.9 4.00098 2 4.90098 2 6.00098V18.001C2 19.101 2.9 20.001 4 20.001H20C21.1 20.001 22 19.101 22 18.001V6.00098ZM20 6.00098L12 11.001L4 6.00098H20ZM20 18.001H4V8.00098L12 13.001L20 8.00098V18.001Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 21`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 18.001H21V16.001H3V18.001ZM3 13.001H21V11.001H3V13.001ZM3 6.00098V8.00098H21V6.00098H3Z"
    />
    ;
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 22`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.01 15.381C18.78 15.381 17.59 15.181 16.48 14.821C16.13 14.701 15.74 14.791 15.47 15.061L13.9 17.031C11.07 15.681 8.42 13.131 7.01 10.201L8.96 8.54098C9.23 8.26098 9.31 7.87098 9.2 7.52098C8.83 6.41098 8.64 5.22098 8.64 3.99098C8.64 3.45098 8.19 3.00098 7.65 3.00098H4.19C3.65 3.00098 3 3.24098 3 3.99098C3 13.281 10.73 21.001 20.01 21.001C20.72 21.001 21 20.371 21 19.821V16.371C21 15.831 20.55 15.381 20.01 15.381Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 23`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M14.71 14.001H15.5L20.49 19.001L19 20.491L14 15.501V14.711L13.73 14.431C12.59 15.411 11.11 16.001 9.5 16.001C5.91 16.001 3 13.091 3 9.50098C3 5.91098 5.91 3.00098 9.5 3.00098C13.09 3.00098 16 5.91098 16 9.50098C16 11.111 15.41 12.591 14.43 13.731L14.71 14.001ZM5 9.50098C5 11.991 7.01 14.001 9.5 14.001C11.99 14.001 14 11.991 14 9.50098C14 7.01098 11.99 5.00098 9.5 5.00098C7.01 5.00098 5 7.01098 5 9.50098Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 24`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 25`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 19H13V14.825L14.6 16.425L16 15L12 11L8 15L9.425 16.4L11 14.825V19ZM6 22C5.45 22 4.97917 21.8042 4.5875 21.4125C4.19583 21.0208 4 20.55 4 20V4C4 3.45 4.19583 2.97917 4.5875 2.5875C4.97917 2.19583 5.45 2 6 2H14L20 8V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H6ZM13 9V4H6V20H18V9H13Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 26`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 27`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
      fill-rule="evenodd"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 28`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 2.00098C6.48 2.00098 2 6.48098 2 12.001C2 17.521 6.48 22.001 12 22.001C17.52 22.001 22 17.521 22 12.001C22 6.48098 17.52 2.00098 12 2.00098ZM4 12.001C4 11.391 4.08 10.791 4.21 10.221L8.99 15.001V16.001C8.99 17.101 9.89 18.001 10.99 18.001V19.931C7.06 19.431 4 16.071 4 12.001ZM17.89 17.401C17.63 16.591 16.89 16.001 15.99 16.001H14.99V13.001C14.99 12.451 14.54 12.001 13.99 12.001H7.99V10.001H9.99C10.54 10.001 10.99 9.55098 10.99 9.00098V7.00098H12.99C14.09 7.00098 14.99 6.10098 14.99 5.00098V4.59098C17.92 5.77098 20 8.65098 20 12.001C20 14.081 19.19 15.981 17.89 17.401Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 29`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 30`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.79412 17.4C9.30882 17.4 8.89338 17.2238 8.54779 16.8713C8.20221 16.5188 8.02941 16.095 8.02941 15.6V4.8C8.02941 4.305 8.20221 3.88125 8.54779 3.52875C8.89338 3.17625 9.30882 3 9.79412 3H17.7353C18.2206 3 18.636 3.17625 18.9816 3.52875C19.3272 3.88125 19.5 4.305 19.5 4.8V15.6C19.5 16.095 19.3272 16.5188 18.9816 16.8713C18.636 17.2238 18.2206 17.4 17.7353 17.4H9.79412ZM9.79412 15.6H17.7353V4.8H9.79412V15.6ZM6.26471 21C5.77941 21 5.36397 20.8238 5.01838 20.4713C4.67279 20.1188 4.5 19.695 4.5 19.2V6.6H6.26471V19.2H15.9706V21H6.26471Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 31`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 23C6.45 23 5.97917 22.8042 5.5875 22.4125C5.19583 22.0208 5 21.55 5 21V3C5 2.45 5.19583 1.97917 5.5875 1.5875C5.97917 1.19583 6.45 1 7 1H17C17.55 1 18.0208 1.19583 18.4125 1.5875C18.8042 1.97917 19 2.45 19 3V21C19 21.55 18.8042 22.0208 18.4125 22.4125C18.0208 22.8042 17.55 23 17 23H7ZM7 20V21H17V20H7ZM7 18H17V6H7V18ZM7 4H17V3H7V4Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 32`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19ZM6 17H18L14.25 12L11.25 16L9 13L6 17Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 33`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.95 18C12.3 18 12.5958 17.8792 12.8375 17.6375C13.0792 17.3958 13.2 17.1 13.2 16.75C13.2 16.4 13.0792 16.1042 12.8375 15.8625C12.5958 15.6208 12.3 15.5 11.95 15.5C11.6 15.5 11.3042 15.6208 11.0625 15.8625C10.8208 16.1042 10.7 16.4 10.7 16.75C10.7 17.1 10.8208 17.3958 11.0625 17.6375C11.3042 17.8792 11.6 18 11.95 18ZM11.05 14.15H12.9C12.9 13.6 12.9625 13.1667 13.0875 12.85C13.2125 12.5333 13.5667 12.1 14.15 11.55C14.5833 11.1167 14.925 10.7042 15.175 10.3125C15.425 9.92083 15.55 9.45 15.55 8.9C15.55 7.96667 15.2083 7.25 14.525 6.75C13.8417 6.25 13.0333 6 12.1 6C11.15 6 10.3792 6.25 9.7875 6.75C9.19583 7.25 8.78333 7.85 8.55 8.55L10.2 9.2C10.2833 8.9 10.4708 8.575 10.7625 8.225C11.0542 7.875 11.5 7.7 12.1 7.7C12.6333 7.7 13.0333 7.84583 13.3 8.1375C13.5667 8.42917 13.7 8.75 13.7 9.1C13.7 9.43333 13.6 9.74583 13.4 10.0375C13.2 10.3292 12.95 10.6 12.65 10.85C11.9167 11.5 11.4667 11.9917 11.3 12.325C11.1333 12.6583 11.05 13.2667 11.05 14.15ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z"
    />
  </svg>
</div>
`;

exports[`Icons > renders Icon with size small without crashing 34`] = `
.c0 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <svg
    class="c0"
    data-testid="icon"
    fill="none"
    focusable="false"
    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.9 17C12.25 17 12.5458 16.8792 12.7875 16.6375C13.0292 16.3958 13.15 16.1 13.15 15.75C13.15 15.4 13.0292 15.1042 12.7875 14.8625C12.5458 14.6208 12.25 14.5 11.9 14.5C11.55 14.5 11.2542 14.6208 11.0125 14.8625C10.7708 15.1042 10.65 15.4 10.65 15.75C10.65 16.1 10.7708 16.3958 11.0125 16.6375C11.2542 16.8792 11.55 17 11.9 17ZM11 13.15H12.85C12.85 12.8667 12.8625 12.625 12.8875 12.425C12.9125 12.225 12.9667 12.0333 13.05 11.85C13.1333 11.6667 13.2375 11.4958 13.3625 11.3375C13.4875 11.1792 13.6667 10.9833 13.9 10.75C14.4833 10.1667 14.8958 9.67917 15.1375 9.2875C15.3792 8.89583 15.5 8.45 15.5 7.95C15.5 7.06667 15.2 6.35417 14.6 5.8125C14 5.27083 13.1917 5 12.175 5C11.2583 5 10.4792 5.225 9.8375 5.675C9.19583 6.125 8.75 6.75 8.5 7.55L10.15 8.2C10.2667 7.75 10.5 7.3875 10.85 7.1125C11.2 6.8375 11.6083 6.7 12.075 6.7C12.525 6.7 12.9 6.82083 13.2 7.0625C13.5 7.30417 13.65 7.625 13.65 8.025C13.65 8.30833 13.5583 8.60833 13.375 8.925C13.1917 9.24167 12.8833 9.59167 12.45 9.975C12.1667 10.2083 11.9375 10.4375 11.7625 10.6625C11.5875 10.8875 11.4417 11.125 11.325 11.375C11.2083 11.625 11.125 11.8875 11.075 12.1625C11.025 12.4375 11 12.7667 11 13.15ZM12 23L9 20H5C4.45 20 3.97917 19.8042 3.5875 19.4125C3.19583 19.0208 3 18.55 3 18V4C3 3.45 3.19583 2.97917 3.5875 2.5875C3.97917 2.19583 4.45 2 5 2H19C19.55 2 20.0208 2.19583 20.4125 2.5875C20.8042 2.97917 21 3.45 21 4V18C21 18.55 20.8042 19.0208 20.4125 19.4125C20.0208 19.8042 19.55 20 19 20H15L12 23ZM5 18H9.8L12 20.2L14.2 18H19V4H5V18Z"
    />
  </svg>
</div>
`;

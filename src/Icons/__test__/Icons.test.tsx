import React from 'react';
import { render } from '../../utils/testUtils';
import {
  AddIcon,
  ArrowIcon,
  CalendarIcon,
  CaretIcon,
  CheckIcon,
  ChevronIcon,
  CloseIcon,
  DoubleArrowIcon,
  DoubleChevronIcon,
  DownloadIcon,
  ErrorIcon,
  ExternalIcon,
  FacebookIcon,
  FileIcon,
  InformationIcon,
  InstagramIcon,
  LinkedInIcon,
  LockIcon,
  LoginIcon,
  MailIcon,
  MenuIcon,
  PhoneIcon,
  SearchIcon,
  TwitterIcon,
  UploadFileIcon,
  UploadIcon,
  WarningIcon,
  WorldIcon,
  YoutubeIcon,
  CopyIcon,
  SmartphoneIcon,
  ImageIcon,
  HelpIcon,
  LiveHelpIcon,
} from '..';

describe('Icons', () => {
  [
    AddIcon,
    ArrowIcon,
    CalendarIcon,
    CaretIcon,
    CheckIcon,
    ChevronIcon,
    CloseIcon,
    DoubleArrowIcon,
    DoubleChevronIcon,
    DownloadIcon,
    ErrorIcon,
    ExternalIcon,
    FacebookIcon,
    FileIcon,
    InformationIcon,
    InstagramIcon,
    LockIcon,
    LinkedInIcon,
    LoginIcon,
    MailIcon,
    MenuIcon,
    PhoneIcon,
    SearchIcon,
    TwitterIcon,
    UploadFileIcon,
    UploadIcon,
    WarningIcon,
    WorldIcon,
    YoutubeIcon,
    CopyIcon,
    SmartphoneIcon,
    ImageIcon,
    HelpIcon,
    LiveHelpIcon,
  ].forEach((Icon) => {
    (['small', 'medium', 'large'] as const).forEach((size) => {
      test(`renders Icon with size ${size} without crashing`, () => {
        const { getByTestId, container } = render(<Icon size={size} data-testid="icon" />);
        const testIcon = getByTestId('icon');
        expect(testIcon).toBeInTheDocument();
        expect(container).toMatchSnapshot();
      });
    });
  });

  [CheckIcon].forEach((Icon) => {
    test(`renders Icon with isStroke true without crashing`, () => {
      const { getAllByTestId, container } = render(
        <>
          <Icon isStroke={true} data-testid="icon" size="small" />
          <Icon isStroke={true} data-testid="icon" />
          <Icon isStroke={true} data-testid="icon" size="large" />
        </>,
      );
      const allTestIcons = getAllByTestId('icon');
      expect(allTestIcons.length).toEqual(3);
      expect(container).toMatchSnapshot();
    });
  });

  [ErrorIcon, InformationIcon, WarningIcon].forEach((Icon) => {
    test(`renders Icon with isOutline true without crashing`, () => {
      const { getAllByTestId, container } = render(
        <>
          <Icon isOutline={true} data-testid="icon" size="small" />
          <Icon isOutline={true} data-testid="icon" />
          <Icon isOutline={true} data-testid="icon" size="large" />
        </>,
      );
      const allTestIcons = getAllByTestId('icon');
      expect(allTestIcons.length).toEqual(3);
      expect(container).toMatchSnapshot();
    });
  });

  [ArrowIcon, CaretIcon, ChevronIcon, DoubleChevronIcon].forEach((Icon) => {
    (['0', '90', '180', '270'] as const).forEach((rotation) => {
      test(`renders Icon with rotation ${rotation} without crashing`, () => {
        const { getByTestId, container } = render(<Icon rotation={rotation} data-testid="icon" />);
        const testIcon = getByTestId('icon');
        expect(testIcon).toBeInTheDocument();
        expect(container).toMatchSnapshot();
      });
    });
  });
});

import React from 'react';
import {
  AddIcon,
  ArrowIcon,
  CalendarIcon,
  CaretIcon,
  CheckIcon,
  ChevronIcon,
  CloseIcon,
  DoubleArrowIcon,
  DoubleChevronIcon,
  DownloadIcon,
  ErrorIcon,
  ExternalIcon,
  FacebookIcon,
  FileIcon,
  InformationIcon,
  InstagramIcon,
  LinkedInIcon,
  LockIcon,
  LoginIcon,
  MailIcon,
  MenuIcon,
  PhoneIcon,
  SearchIcon,
  TwitterIcon,
  UploadFileIcon,
  UploadIcon,
  WarningIcon,
  WorldIcon,
  YoutubeIcon,
  CopyIcon,
  SmartphoneIcon,
  ImageIcon,
  HelpIcon,
  LiveHelpIcon,
} from '..';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Icons', () => {
  test('AllIcons', async ({ task }) => {
    await takeScreenshot(
      <>
        {[
          AddIcon,
          ArrowIcon,
          CalendarIcon,
          CaretIcon,
          CheckIcon,
          ChevronIcon,
          CloseIcon,
          DoubleArrowIcon,
          DoubleChevronIcon,
          DownloadIcon,
          ErrorIcon,
          ExternalIcon,
          FacebookIcon,
          FileIcon,
          InformationIcon,
          InstagramIcon,
          LinkedInIcon,
          LockIcon,
          LoginIcon,
          MailIcon,
          MenuIcon,
          PhoneIcon,
          SearchIcon,
          TwitterIcon,
          UploadFileIcon,
          UploadIcon,
          WarningIcon,
          WorldIcon,
          YoutubeIcon,
          CopyIcon,
          SmartphoneIcon,
          ImageIcon,
          HelpIcon,
          LiveHelpIcon,
        ].map((Icon, i) => (
          <div key={i}>
            <Icon size="small" />
            <Icon />
            <Icon size="large" />
          </div>
        ))}
      </>,
      task,
      {
        viewport: { height: 1300 },
      },
    );
  });

  test('VariantIcons', async ({ task }) => {
    await takeScreenshot(
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {[CheckIcon].map((Icon, i) => (
          <div style={{ display: 'flex' }} key={i}>
            <Icon />
            <Icon isStroke />
          </div>
        ))}
        {[ErrorIcon, InformationIcon, WarningIcon].map((Icon, i) => (
          <div style={{ display: 'flex' }} key={i}>
            <Icon />
            <Icon isOutline />
          </div>
        ))}
      </div>,
      task,
    );
  });

  test('RotatedIcons', async ({ task }) => {
    await takeScreenshot(
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {[ArrowIcon, CaretIcon, ChevronIcon, DoubleChevronIcon].map((Icon, i) => (
          <div style={{ display: 'flex' }} key={i}>
            <Icon rotation="0" />
            <Icon rotation="90" />
            <Icon rotation="180" />
            <Icon rotation="270" />
          </div>
        ))}
      </div>,
      task,
    );
  });
});

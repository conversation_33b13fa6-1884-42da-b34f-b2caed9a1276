import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type ChevronIconProps = CommonIconProps & {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180' | '270';
};

const ChevronIcon: React.FC<ChevronIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z" />;
    </Icon>
  );
};

export default ChevronIcon;

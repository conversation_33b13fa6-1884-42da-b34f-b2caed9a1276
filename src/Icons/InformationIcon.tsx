import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type InformationIconProps = CommonIconProps & {
  /** Sets outline for the icon */
  isOutline?: boolean;
};

const InformationIcon: React.FC<InformationIconProps> = ({ isOutline = false, ...rest }) => {
  return (
    <Icon {...rest}>
      {isOutline ? (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
        />
      ) : (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
        />
      )}
    </Icon>
  );
};

export default InformationIcon;

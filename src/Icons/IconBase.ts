import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'size' | 'rotation'> = {
  size: {
    small: {
      height: getSpacingVar(4),
      width: getSpacingVar(4),
    },
    medium: {
      height: getSpacingVar(6),
      width: getSpacingVar(6),
    },
    large: {
      height: getSpacingVar(8),
      width: getSpacingVar(8),
    },
  },
  rotation: {
    '0': {
      transform: 'rotate(0deg)',
    },
    '90': {
      transform: 'rotate(90deg)',
    },
    '180': {
      transform: 'rotate(180deg)',
    },
    '270': {
      transform: 'rotate(270deg)',
    },
  },
};

const { useSx, getSxStyleRules } = sx('IconBase', [], variants);

export { useSx };

export const IconBase = styled.svg`
  fill: currentColor;
  ${getSxStyleRules()}
`;

export default IconBase;

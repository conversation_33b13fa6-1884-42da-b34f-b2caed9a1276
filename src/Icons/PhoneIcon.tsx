import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type PhoneIconProps = CommonIconProps;

const PhoneIcon: React.FC<PhoneIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M20.01 15.381C18.78 15.381 17.59 15.181 16.48 14.821C16.13 14.701 15.74 14.791 15.47 15.061L13.9 17.031C11.07 15.681 8.42 13.131 7.01 10.201L8.96 8.54098C9.23 8.26098 9.31 7.87098 9.2 7.52098C8.83 6.41098 8.64 5.22098 8.64 3.99098C8.64 3.45098 8.19 3.00098 7.65 3.00098H4.19C3.65 3.00098 3 3.24098 3 3.99098C3 13.281 10.73 21.001 20.01 21.001C20.72 21.001 21 20.371 21 19.821V16.371C21 15.831 20.55 15.381 20.01 15.381Z" />
    </Icon>
  );
};

export default PhoneIcon;

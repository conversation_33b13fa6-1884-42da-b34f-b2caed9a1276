import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type ErrorIconProps = CommonIconProps & {
  /** Sets outline for the icon */
  isOutline?: boolean;
};

const ErrorIcon: React.FC<ErrorIconProps> = ({ isOutline = false, ...rest }) => {
  return (
    <Icon {...rest}>
      {isOutline ? (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
        />
      ) : (
        <path d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z" />
      )}
    </Icon>
  );
};

export default ErrorIcon;

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type MailIconProps = CommonIconProps;

const MailIcon: React.FC<MailIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M22 6.00098C22 4.90098 21.1 4.00098 20 4.00098H4C2.9 4.00098 2 4.90098 2 6.00098V18.001C2 19.101 2.9 20.001 4 20.001H20C21.1 20.001 22 19.101 22 18.001V6.00098ZM20 6.00098L12 11.001L4 6.00098H20ZM20 18.001H4V8.00098L12 13.001L20 8.00098V18.001Z" />
    </Icon>
  );
};

export default MailIcon;

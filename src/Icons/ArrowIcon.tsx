import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type ArrowIconProps = CommonIconProps & {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180' | '270';
};

const ArrowIcon: React.FC<ArrowIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z" />
    </Icon>
  );
};

export default ArrowIcon;

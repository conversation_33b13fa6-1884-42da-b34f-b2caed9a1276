import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type ExternalIconProps = CommonIconProps;

const ExternalIcon: React.FC<ExternalIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z" />
    </Icon>
  );
};

export default ExternalIcon;

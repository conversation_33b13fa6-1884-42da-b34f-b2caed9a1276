import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type SearchIconProps = CommonIconProps;

const SearchIcon: React.FC<SearchIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.71 14.001H15.5L20.49 19.001L19 20.491L14 15.501V14.711L13.73 14.431C12.59 15.411 11.11 16.001 9.5 16.001C5.91 16.001 3 13.091 3 9.50098C3 5.91098 5.91 3.00098 9.5 3.00098C13.09 3.00098 16 5.91098 16 9.50098C16 11.111 15.41 12.591 14.43 13.731L14.71 14.001ZM5 9.50098C5 11.991 7.01 14.001 9.5 14.001C11.99 14.001 14 11.991 14 9.50098C14 7.01098 11.99 5.00098 9.5 5.00098C7.01 5.00098 5 7.01098 5 9.50098Z"
      />
    </Icon>
  );
};

export default SearchIcon;

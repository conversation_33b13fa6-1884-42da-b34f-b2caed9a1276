import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type YoutubeIconProps = CommonIconProps;

const YoutubeIcon: React.FC<YoutubeIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z" />
    </Icon>
  );
};

export default YoutubeIcon;

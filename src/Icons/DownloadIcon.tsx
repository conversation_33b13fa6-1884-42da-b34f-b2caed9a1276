import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type DownloadIconProps = CommonIconProps;

const DownloadIcon: React.FC<DownloadIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M18 15.0007V18.0007H6V15.0007H4V18.0007C4 19.1007 4.9 20.0007 6 20.0007H18C19.1 20.0007 20 19.1007 20 18.0007V15.0007H18ZM17 11.0007L15.59 9.59073L13 12.1707V4.00073H11V12.1707L8.41 9.59073L7 11.0007L12 16.0007L17 11.0007Z" />
    </Icon>
  );
};

export default DownloadIcon;

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type LinkedInIconProps = CommonIconProps;

const LinkedInIcon: React.FC<LinkedInIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z" />
    </Icon>
  );
};

export default LinkedInIcon;

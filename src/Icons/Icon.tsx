import React from 'react';
import { IconBase, useSx } from './IconBase';

export type CommonIconProps = {
  /** Sets the size of the icon */
  size?: 'small' | 'medium' | 'large';
};

type IconProps = {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180' | '270';
} & CommonIconProps &
  Pick<React.ComponentPropsWithoutRef<'svg'>, 'viewBox'>;

const Icon: React.FC<React.PropsWithChildren<IconProps>> = ({ children, viewBox = '0 0 24 24', size = 'medium', rotation = '0', ...rest }) => {
  return (
    <IconBase focusable={false} viewBox={viewBox} style={useSx({}, { size, rotation })} fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      {children}
    </IconBase>
  );
};

export default Icon;

The following Icons are available to import as _svg_ components.

### Importing an icon

```js
import { AddIcon } from '@axa-japan/design-system-react/Icons';
<AddIcon />;
```

### Adjusting icon size

```js
import { AddIcon } from '@axa-japan/design-system-react/Icons';
<div>
  <AddIcon size="small" />
  <AddIcon />
  <AddIcon size="large" />
</div>;
```

### Adjusting icon color

The icon inherits the _color_ from it's parent container, which inherits from the root _body_ by default. The default can be overriden by setting the _color_ on the icon's parent container.

```js
import { AddIcon } from '@axa-japan/design-system-react/Icons';
import { getColorVar } from '@axa-japan/design-system-react/colors';
<div style={{ color: getColorVar('statusDanger') }}>
  <AddIcon />
</div>;
```

### Outline and stroke variants

Some icons have outline or stroke variants.

```js
import { CheckIcon, ErrorIcon } from '@axa-japan/design-system-react/Icons';

<div style={{ display: 'flex', flexDirection: 'column' }}>
  <div style={{ display: 'flex' }}>
    <CheckIcon />
    <CheckIcon isStroke />
  </div>
  <div style={{ display: 'flex' }}>
    <ErrorIcon />
    <ErrorIcon isOutline />
  </div>
</div>;
```

### Adjusting icon rotation

Some icons can be rotated:

```js
import { ArrowIcon } from '@axa-japan/design-system-react/Icons';

<div style={{ display: 'flex' }}>
  <ArrowIcon rotation="0" />
  <ArrowIcon rotation="90" />
  <ArrowIcon rotation="180" />
</div>;
```

## All other icons and sizes, variants, rotations

For references purposes, these are the other available Icons:

### Other icons and sizes

```js
import {
  AddIcon,
  ArrowIcon,
  CalendarIcon,
  CaretIcon,
  CheckIcon,
  ChevronIcon,
  CloseIcon,
  DeleteIcon,
  DoubleArrowIcon,
  DoubleChevronIcon,
  DownloadIcon,
  ErrorIcon,
  ExternalIcon,
  FacebookIcon,
  FileIcon,
  InformationIcon,
  InstagramIcon,
  LinkedInIcon,
  LockIcon,
  LoginIcon,
  MailIcon,
  MenuIcon,
  PhoneIcon,
  SearchIcon,
  TwitterIcon,
  UploadFileIcon,
  UploadIcon,
  WarningIcon,
  WorldIcon,
  YoutubeIcon,
  CopyIcon,
  SmartphoneIcon,
  ImageIcon,
  HelpIcon,
  LiveHelpIcon,
} from '@axa-japan/design-system-react/Icons';
<>
  {[
    AddIcon,
    ArrowIcon,
    CalendarIcon,
    CaretIcon,
    CheckIcon,
    ChevronIcon,
    CloseIcon,
    DeleteIcon,
    DoubleArrowIcon,
    DoubleChevronIcon,
    DownloadIcon,
    ErrorIcon,
    ExternalIcon,
    FacebookIcon,
    FileIcon,
    InformationIcon,
    InstagramIcon,
    LockIcon,
    LinkedInIcon,
    LoginIcon,
    MailIcon,
    MenuIcon,
    PhoneIcon,
    SearchIcon,
    TwitterIcon,
    UploadFileIcon,
    UploadIcon,
    WarningIcon,
    WorldIcon,
    YoutubeIcon,
    CopyIcon,
    SmartphoneIcon,
    ImageIcon,
    HelpIcon,
    LiveHelpIcon,
  ].map((Icon, i) => (
    <div key={i}>
      <Icon size="small" />
      <Icon />
      <Icon size="large" />
    </div>
  ))}
</>;
```

### Other icon variants

Some icons have outline or stroke variants.

```js
import { CheckIcon, ErrorIcon, InformationIcon, WarningIcon } from '@axa-japan/design-system-react/Icons';
<div style={{ display: 'flex', flexDirection: 'column' }}>
  {[CheckIcon].map((Icon, i) => (
    <div style={{ display: 'flex' }} key={i}>
      <Icon />
      <Icon isStroke />
    </div>
  ))}
  {[ErrorIcon, InformationIcon, WarningIcon].map((Icon, i) => (
    <div style={{ display: 'flex' }} key={i}>
      <Icon />
      <Icon isOutline />
    </div>
  ))}
</div>;
```

### Other icon rotations

Some icons have different rotations.

```js
import { ArrowIcon, CaretIcon, ChevronIcon, DoubleChevronIcon } from '@axa-japan/design-system-react/Icons';
<div style={{ display: 'flex', flexDirection: 'column' }}>
  {[ArrowIcon, CaretIcon, ChevronIcon, DoubleChevronIcon].map((Icon, i) => (
    <div style={{ display: 'flex' }} key={i}>
      <Icon rotation="0" />
      <Icon rotation="90" />
      <Icon rotation="180" />
      <Icon rotation="270" />
    </div>
  ))}
</div>;
```

import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type CheckIconProps = CommonIconProps & {
  /** Sets the circle of the icon */
  isStroke?: boolean;
};

const CheckIcon: React.FC<CheckIconProps> = ({ isStroke = false, ...rest }) => {
  return (
    <Icon {...rest}>
      {isStroke ? (
        <path d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z" />
      ) : (
        <path d="M12 3.00073C16.968 3.00073 21 7.03273 21 12.0007C20.9999 16.9686 16.9679 21.0007 12 21.0007C7.03207 21.0007 3.00012 16.9686 3 12.0007C3 7.03273 7.032 3.00073 12 3.00073ZM10.2324 13.6257L7.46289 10.8425L6.375 11.9392L10.2324 15.8259L17.1748 8.82983L16.0869 7.72534L10.2324 13.6257Z" />
      )}
    </Icon>
  );
};

export default CheckIcon;

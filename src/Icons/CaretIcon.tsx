import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type CaretIconProps = CommonIconProps & {
  /** Sets the rotation of the icon */
  rotation?: '0' | '90' | '180' | '270';
};

const CaretIcon: React.FC<CaretIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M10 7.00073L15 12.0007L10 17.0007V7.00073Z" />;
    </Icon>
  );
};

export default CaretIcon;

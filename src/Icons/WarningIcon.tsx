import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type WarningIconProps = CommonIconProps & {
  /** Sets outline for the icon */
  isOutline?: boolean;
};

const WarningIcon: React.FC<WarningIconProps> = ({ isOutline = false, ...rest }) => {
  return (
    <Icon {...rest}>
      {isOutline ? (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
        />
      ) : (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
        />
      )}
    </Icon>
  );
};

export default WarningIcon;

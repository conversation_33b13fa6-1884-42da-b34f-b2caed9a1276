import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type CopyIconProps = CommonIconProps;

const CopyIcon: React.FC<CopyIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M9.79412 17.4C9.30882 17.4 8.89338 17.2238 8.54779 16.8713C8.20221 16.5188 8.02941 16.095 8.02941 15.6V4.8C8.02941 4.305 8.20221 3.88125 8.54779 3.52875C8.89338 3.17625 9.30882 3 9.79412 3H17.7353C18.2206 3 18.636 3.17625 18.9816 3.52875C19.3272 3.88125 19.5 4.305 19.5 4.8V15.6C19.5 16.095 19.3272 16.5188 18.9816 16.8713C18.636 17.2238 18.2206 17.4 17.7353 17.4H9.79412ZM9.79412 15.6H17.7353V4.8H9.79412V15.6ZM6.26471 21C5.77941 21 5.36397 20.8238 5.01838 20.4713C4.67279 20.1188 4.5 19.695 4.5 19.2V6.6H6.26471V19.2H15.9706V21H6.26471Z" />
    </Icon>
  );
};

export default CopyIcon;

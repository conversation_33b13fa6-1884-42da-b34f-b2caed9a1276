import React from 'react';
import Icon, { CommonIconProps } from './Icon';

export type DeleteIconProps = CommonIconProps;

const DeleteIcon: React.FC<DeleteIconProps> = (props) => {
  return (
    <Icon {...props}>
      <path d="M6.83333 21C6.31389 21 5.86921 20.815 5.49931 20.4451C5.1294 20.0752 4.94444 19.6306 4.94444 19.1111V6.83333H4V4.94444H8.72222V4H14.3889V4.94444H19.1111V6.83333H18.1667V19.1111C18.1667 19.6306 17.9817 20.0752 17.6118 20.4451C17.2419 20.815 16.7972 21 16.2778 21H6.83333ZM16.2778 6.83333H6.83333V19.1111H16.2778V6.83333ZM8.72222 17.2222H10.6111V8.72222H8.72222V17.2222ZM12.5 17.2222H14.3889V8.72222H12.5V17.2222Z" />
    </Icon>
  );
};

export default DeleteIcon;

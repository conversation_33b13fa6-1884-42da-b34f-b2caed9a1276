import React from 'react';
import { SelectArrowBase, SelectArrowSvg } from './SelectArrow.styles';

export type SelectArrowProps = {
  disabled: boolean;
} & React.ComponentPropsWithoutRef<'span'>;

const SelectArrow: React.FC<SelectArrowProps> = (props) => {
  const { disabled, ...rest } = props;
  return (
    <SelectArrowBase aria-disabled={disabled} {...rest}>
      <SelectArrowSvg disabled={disabled} xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <path fill="currentColor" fillRule="nonzero" d="M4.175 5L8 8.712 11.825 5 13 6.148 8 11 3 6.148z" />
      </SelectArrowSvg>
    </SelectArrowBase>
  );
};

export default SelectArrow;

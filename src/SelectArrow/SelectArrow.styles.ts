import styled from 'styled-components';
import { getColorVar } from '../colors';

export const SelectArrowBase = styled.span`
  flex-shrink: 0;
  padding: 12px 6px;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
`;

export const SelectArrowSvg = styled.svg<{ disabled: boolean }>`
  color: ${getColorVar('interactiveActivePrimary')};
  height: 16px;
  object-fit: contain;
  width: 16px;
  ${({ disabled }): string | false => disabled && `color: ${getColorVar('grey400')};`}
`;

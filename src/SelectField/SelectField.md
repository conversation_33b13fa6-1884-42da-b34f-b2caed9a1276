In addition to props above, &lt;SelectField&gt; accepts props from Radix UI's _SelectProps_ type.

### SelectField (uncontrolled):

You can set uncontrolled props _defaultValue_ and _defaultOpen_ to set the default selected value and whether the _SelectField_ is open by default respectively.

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1 Lorem Ipsum is simply dummy text of the printing and typesetting industry',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<div style={{ width: '400px' }}>
  <SelectField defaultValue="選択肢1" defaultOpen={false} name="select-field-default" label="SelectField" options={options} />
</div>;
```

### SelectField (controlled):

You can set controlled props _value_, _onValueChange_ and _open_, _onOpenChange_ to set the selected value and whether the _SelectField_ is open by or not respectively.

```js
import React, { useState } from 'react';
import SelectField from '@axa-japan/design-system-react/SelectField';

const Render = () => {
  const options = [
    {
      text: '選択肢1 Lorem Ipsum is simply dummy text of the printing and typesetting industry',
      value: '選択肢1',
    },
    {
      text: '選択肢2',
      value: '選択肢2',
    },
  ];
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState('選択肢1');
  return (
    <div style={{ width: '400px' }}>
      <SelectField
        open={isOpen}
        onOpenChange={(open) => setIsOpen(open)}
        value={selectedValue}
        onValueChange={(value) => setSelectedValue(value)}
        name="select-field-controlled-default"
        label="SelectField"
        options={options}
      />
    </div>
  );
};

<Render />;
```

### Required:

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<SelectField name="select-field-required" required label="Required SelectField" options={options} />;
```

### Without label:

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<SelectField name="select-field-no-label" options={options} />;
```

### Disabled:

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<SelectField name="select-field-disabled" options={options} disabled />;
```

### With placeholder:

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<SelectField name="select-field-placeholder" options={options} placeholder="example" />;
```

### With error:

```js
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

<SelectField name="select-field-with-error" options={options} errorMessage="エラー" />;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the &lt;div&gt; container of the &lt;SelectField&gt;.

```js
import React, { useRef } from 'react';
import SelectField from '@axa-japan/design-system-react/SelectField';

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

const Render = () => {
  const ref = useRef(null);
  return <SelectField ref={ref} name="select-field-ref" label="Ref" options={options} />;
};

<Render />;
```

import React from 'react';
import <PERSON><PERSON>ield from '../SelectField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

const options = [
  {
    text: '選択肢1 Lorem Ipsum is simply dummy text of the printing and typesetting industry',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

describe('SelectField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<SelectField name="basic-select-input" label="Basic SelectField" options={options} defaultOpen />, task);
  });

  test('WithLongText', async ({ task }) => {
    await takeScreenshot(<SelectField name="long-text" label="Long Text SelectField" options={options} defaultValue="選択肢1" defaultOpen />, task);
  });

  test('Required', async ({ task }) => {
    await takeScreenshot(<SelectField name="required-select-input" label="Required SelectField" required options={options} defaultOpen />, task);
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(<SelectField name="no-label" options={options} defaultOpen />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<SelectField name="input-select-input" label="SelectField Disabled" disabled options={options} />, task);
  });

  test('Placeholder', async ({ task }) => {
    await takeScreenshot(
      <SelectField name="placeholder-select-input" label="Placeholder SelectField" placeholder="Some placeholder" options={options} defaultOpen />,
      task,
    );
  });

  test('Error', async ({ task }) => {
    await takeScreenshot(
      <SelectField name="error-select-input" label="SelectField With Error" errorMessage="Danger danger danger!!!" options={options} defaultOpen />,
      task,
    );
  });
});

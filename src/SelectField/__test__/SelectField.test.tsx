import React from 'react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { render } from '../../utils/testUtils';
import SelectField from '../SelectField';

window.PointerEvent = class PointerEvent extends Event {};
window.HTMLElement.prototype.scrollIntoView = vi.fn();
window.HTMLElement.prototype.hasPointerCapture = vi.fn();
window.HTMLElement.prototype.releasePointerCapture = vi.fn();

const options = [
  {
    text: '選択肢1',
    value: '選択肢1',
  },
  {
    text: '選択肢2',
    value: '選択肢2',
  },
];

describe('SelectField', () => {
  test('renders without crashing', () => {
    const { container, getByTestId } = render(
      <SelectField id="select-field" name="basic-select" label="Basic SelectField" options={options} data-testid="selectField" />,
    );
    const testElement = getByTestId('selectField-trigger');

    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('Should select an option and display it', async () => {
    const { container, getByTestId } = render(
      <SelectField id="select-field" name="basic-select" label="Basic SelectField" options={options} data-testid="selectField" />,
    );
    const buttonElement = getByTestId('selectField-trigger');
    const selectFieldElement = getByTestId('selectField-value-text');

    expect(selectFieldElement).toBeInTheDocument();
    expect(selectFieldElement).toHaveTextContent('');

    await userEvent.click(buttonElement);

    const itemElement = getByTestId('selectField-item-選択肢1');
    expect(itemElement).toBeInTheDocument();

    await userEvent.click(itemElement);

    expect(selectFieldElement).toHaveTextContent('選択肢1');
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { container, getByTestId } = render(<SelectField id="select-field" name="basic-select" options={options} data-testid="selectField" />);
    const testElement = getByTestId('selectField-trigger');

    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with disabled ${condition}`, () => {
      const { getByTestId, container } = render(
        <SelectField id="select-field" name="basic-select" options={options} data-testid="selectField" disabled={condition} />,
      );
      const testElement = getByTestId('selectField-trigger');

      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((condition) => {
    test(`renders without crashing with required ${condition}`, () => {
      const { getByTestId, container } = render(
        <SelectField id="select-field" name="basic-select" options={options} data-testid="selectField" required={condition} />,
      );
      const testElement = getByTestId('selectField-trigger');

      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders without crashing with error`, () => {
    const { getByTestId, container } = render(
      <SelectField id="select-field" name="basic-select" options={options} data-testid="selectField" errorMessage="Error" />,
    );
    const testElement = getByTestId('selectField-trigger');

    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

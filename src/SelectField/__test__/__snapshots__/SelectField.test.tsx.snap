// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SelectField > Should select an option and display it 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c6 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c4 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c4[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c4[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c5 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="select:select-field"
      id="field-label-:r6:"
    >
      Basic SelectField
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:r7:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c3 c4"
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        >
          選択肢1
        </span>
        <span
          aria-hidden="true"
          class="c5"
        >
          <svg
            class="c6"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c6 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c4[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c4:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c4[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c4 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c4[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c4[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c5 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="select:select-field"
      id="field-label-:r1:"
    >
      Basic SelectField
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:r2:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c3 c4"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c5"
        >
          <svg
            class="c6"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing with disabled false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:rn:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c2 c3"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing with disabled true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:rj:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c2 c3"
        data-disabled=""
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        disabled=""
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing with error 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:r13:"
        aria-expanded="false"
        aria-invalid="true"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c2 c3"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
    <div
      aria-live="polite"
      class="c6"
      role="alert"
    >
      <div
        class="c7"
      >
        <svg
          class="c5"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      Error
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing with required false 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:rv:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c2 c3"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing with required true 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:rr:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="true"
        class="c2 c3"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`SelectField > renders without crashing without label 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

.c3 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c3::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c3:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c2:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c3:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c3[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c3[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c3 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c3[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c3[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c4 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <button
        aria-autocomplete="none"
        aria-controls="radix-:rf:"
        aria-expanded="false"
        aria-invalid="false"
        aria-label="select:select-field:trigger"
        aria-required="false"
        class="c2 c3"
        data-placeholder=""
        data-state="closed"
        data-testid="selectField-trigger"
        dir="ltr"
        id="select:select-field"
        role="combobox"
        type="button"
      >
        <span
          data-testid="selectField-value-text"
          style="pointer-events: none;"
        />
        <span
          aria-hidden="true"
          class="c4"
        >
          <svg
            class="c5"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>
`;

import styled from 'styled-components';
import { Item } from '@radix-ui/react-select';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getRadiusVar } from '../radius';

const SelectFieldItem = styled(Item)`
  position: relative;
  padding: ${getSpacingVar(3)};
  padding-left: ${getSpacingVar(8)};
  border-radius: ${getRadiusVar('sm')};
  min-height: ${getSpacingVar(12)};
  cursor: pointer;
  outline: none;

  :hover {
    background-color: ${getColorVar('interactiveHoverGreyTransparent')};
  }

  &[data-highlighted] {
    background-color: ${getColorVar('interactiveHoverGreyTransparent')};
  }
`;

export default SelectFieldItem;

import styled from 'styled-components';
import { Trigger } from '@radix-ui/react-select';
import input from '../styles/input';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';

const SelectFieldTrigger = styled(Trigger)`
  ${input.base}
  ${input.baseBorder}

  position: relative;

  &:focus-within:not([data-disabled]) {
    ${input.focusBoxShadow}
  }

  &[data-disabled] {
    ${input.disabledBorder}
    svg {
      fill: ${getColorVar('interactiveDisabledDark')};
    }
  }

  width: 100%;
  height: ${getSpacingVar(12)};
  padding: ${getSpacingVar(3)} ${getSpacingVar(3.5)};
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;

  span:first-of-type {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &[data-placeholder] {
    color: ${getColorVar('interactivePlaceholder')};
  }

  &[data-state='open'] {
    svg {
      transform: rotate(270deg);
    }
  }
`;

export default SelectFieldTrigger;

import styled from 'styled-components';
import { Content } from '@radix-ui/react-select';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getShadowVar } from '../shadows';
import { getZIndexVar } from '../zIndex';
import { getRadiusVar } from '../radius';

const SelectFieldContent = styled(Content)`
  box-shadow: ${getShadowVar('mediumShadow')};
  background: ${getColorVar('utilityBackgroundWhite')};
  padding: ${getSpacingVar(1)};
  border-radius: ${getRadiusVar('lg')};
  width: var(--radix-select-trigger-width);
  max-height: var(--radix-select-content-available-height);
  z-index: ${getZIndexVar('overlay')};

  &:focus-within {
    outline: 2px solid ${getColorVar('interactiveFocusPrimary')} !important;
    outline-offset: ${getSpacingVar(0.5)};
  }
`;

export default SelectFieldContent;

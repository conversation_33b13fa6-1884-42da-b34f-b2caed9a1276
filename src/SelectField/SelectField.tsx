import React, { forwardRef, useId, useMemo } from 'react';
import * as Select from '@radix-ui/react-select';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import SelectFieldTrigger from './SelectFieldTrigger';
import SelectFieldContent from './SelectFieldContent';
import ChevronIcon from '../Icons/ChevronIcon';
import SelectIndicator from './SelectIndicator';
import SelectItem from './SelectItem';

export type OptionType = {
  /** The text to display for the option */
  text: string;
  /** The value given as data when submitted with a name. */
  value: string;
};

export type SelectFieldProps = {
  /** The options of the select */
  options: OptionType[];
  /** Text to display when no value is selected. */
  placeholder?: string;
  /** For react testing library tests */
  'data-testid'?: string;
} & CommonFieldBaseProps &
  Omit<Select.SelectProps, 'autoComplete' | 'children' | 'dir' | 'required'> &
  Pick<Select.SelectContentProps, 'id'>;

const SelectField = forwardRef<HTMLDivElement, SelectFieldProps>(
  (
    {
      id,
      name,
      options,
      label,
      showError,
      errorMessage,
      required = false,
      placeholder,
      sx,
      'data-testid': dataTestId,
      showRequiredIndicator,
      ...rest
    },
    ref,
  ) => {
    const fallbackId = useId();
    const hasError = showError !== undefined ? showError : Boolean(errorMessage);
    const resolvedId = `select:${id ?? name ?? `select-id-${fallbackId}`}`;
    const listMemo = useMemo(
      () => options.map((option) => <SelectItem key={option.value} data-testid={dataTestId} value={option.value} text={option.text} />),
      [dataTestId, options],
    );
    return (
      <FieldBase
        id={resolvedId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <Select.Root {...rest} required={required} name={name}>
          <SelectFieldTrigger id={resolvedId} data-testid={`${dataTestId}-trigger`} aria-label={`${resolvedId}:trigger`} aria-invalid={hasError}>
            <Select.Value placeholder={placeholder} data-testid={`${dataTestId}-value-text`} />
            <SelectIndicator>
              <ChevronIcon rotation="90" />
            </SelectIndicator>
          </SelectFieldTrigger>
          <Select.Portal>
            <SelectFieldContent ref={ref} position="popper" sideOffset={8} collisionPadding={{ top: 16, bottom: 16, left: 0, right: 0 }}>
              <Select.ScrollUpButton style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <ChevronIcon rotation="270" />
              </Select.ScrollUpButton>
              <Select.Viewport>{listMemo}</Select.Viewport>
              <Select.ScrollDownButton style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <ChevronIcon rotation="90" />
              </Select.ScrollDownButton>
            </SelectFieldContent>
          </Select.Portal>
        </Select.Root>
      </FieldBase>
    );
  },
);

SelectField.displayName = 'SelectField';

export default SelectField;

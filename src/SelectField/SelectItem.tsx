import React, { FC } from 'react';
import SelectFieldItem from './SelectFieldItem';
import SelectFieldItemIndicator from './SelectFieldItemIndicator';
import CheckIcon from '../Icons/CheckIcon';
import SelectItemText from './SelectItemText';

type SelectItemProps = {
  /** For react testing library tests */
  'data-testid'?: string;
  value: string;
  text: string;
};

const SelectItem: FC<SelectItemProps> = ({ text, 'data-testid': dataTestId, value }) => {
  return (
    <SelectFieldItem value={value} data-testid={`${dataTestId}-item-${value}`}>
      <SelectItemText>{text}</SelectItemText>
      <SelectFieldItemIndicator>
        <CheckIcon isStroke size="small" />
      </SelectFieldItemIndicator>
    </SelectFieldItem>
  );
};

SelectItem.displayName = 'SelectItem';

export default React.memo(SelectItem);

import styled from 'styled-components';
import media from '../Breakpoints/Breakpoints';
import { getTypographyVar } from '../typography';
import { getSpacingVar } from '../spacing';

export const FooterCopyrightBarInner = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: ${getSpacingVar(6)} 0;

  ${media.mediumDown} {
    flex-direction: column;
    gap: ${getSpacingVar(8)};
  }
`;

export const FooterCopyrightText = styled.p`
  align-self: flex-end;
  font-size: ${getTypographyVar('smFontSize')};
  line-height: ${getTypographyVar('lineHeight')};
  margin: 0;
  margin-left: auto;
  padding-block: ${getSpacingVar('px')};
  text-align: right;

  ${media.extraSmallOnly} {
    width: 100%;
    text-align: center;
  }

  ${media.smallUp} {
    white-space: nowrap;
  }
`;

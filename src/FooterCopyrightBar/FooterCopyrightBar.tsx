import React from 'react';
import styled from 'styled-components';
import { FixedLengthTuple, TextLink } from 'src/Footer/types';
import { FooterCopyrightBarInner, FooterCopyrightText } from './FooterCopyrightBar.styles';
import Link from '../Link';
import media from '../Breakpoints/Breakpoints';
import { getSpacingVar } from '../spacing';

type FooterCopyrightBarProps = {
  utilityLinks?: FixedLengthTuple<TextLink>;
} & React.ComponentPropsWithoutRef<'div'>;

const FooterLinksList = styled.ul`
  list-style-type: none;
  margin: 0;
  display: flex;
  gap: ${getSpacingVar(4)};
  flex-wrap: wrap;
  line-height: 1;

  ${media.extraSmallOnly} {
    justify-content: center;
  }
`;

const FooterCopyrightBar: React.FC<React.PropsWithChildren<FooterCopyrightBarProps>> = (props) => {
  const { utilityLinks, children, ...rest } = props;

  return (
    <FooterCopyrightBarInner {...rest}>
      {utilityLinks !== undefined && (
        <FooterLinksList>
          {utilityLinks.map((link, i) =>
            i < 4 ? (
              <li key={`${link.text}-${link.url}`}>
                <Link href={link.url} icon={link.icon} color="white">
                  {link.text}
                </Link>
              </li>
            ) : null,
          )}
        </FooterLinksList>
      )}
      <FooterCopyrightText>{children}</FooterCopyrightText>
    </FooterCopyrightBarInner>
  );
};

export default FooterCopyrightBar;

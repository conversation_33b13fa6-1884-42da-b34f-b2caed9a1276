// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Footer > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: var(--ajds-spacing-6) 0;
}

.c1 {
  -webkit-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
  font-size: var(--ajds-font-size-sm);
  line-height: var(--ajds-line-height);
  margin: 0;
  margin-left: auto;
  padding-block: var(--ajds-spacing-px);
  text-align: right;
}

@media (max-width:1279px) {
  .c0 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c1 {
    width: 100%;
    text-align: center;
  }
}

@media (min-width:600px) {
  .c1 {
    white-space: nowrap;
  }
}

<div>
  <div
    class="c0"
  >
    <p
      class="c1"
    >
      Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.
    </p>
  </div>
</div>
`;

import React from 'react';
import { render } from '../../utils/testUtils';
import FooterCopyrightBar from '../FooterCopyrightBar';

describe('Footer', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<FooterCopyrightBar>Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.</FooterCopyrightBar>);
    const testElement = getByText('Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

import { CalendarDate, getDayOfWeek, getLocalTimeZone, isToday } from '@internationalized/date';
import React, { useMemo } from 'react';
import { CalendarCellRenderProps } from 'react-aria-components';
import CalendarCellTextContainer from './CalendarCellTextContainer';
import CalendarCellText from './CalendarCellText';

type Year = `${number}${number}${number}${number}`;
type Month = `0${number}` | `1${0 | 1 | 2}`;
type Day = `0${number}` | `${1 | 2}${number}` | `3${0 | 1}`;
export type IsoDateString = `${Year}-${Month}-${Day}`;

type CalendarCellProps = {
  /** CalendarDate object for each cell */
  date: CalendarDate;
  /** List of public holidays to be highlighted in the Calendar. */
  holidays?: IsoDateString[];
} & React.RefAttributes<HTMLDivElement>;

const CalendarCell: React.FC<CalendarCellProps> = ({ date, holidays = [] }) => {
  const localTimeZone = useMemo(() => getLocalTimeZone(), []);
  const today = isToday(date, localTimeZone) ? true : null;
  const isSaturday = getDayOfWeek(date, 'ja-JP') === 6 ? true : null;
  const isSunday = getDayOfWeek(date, 'ja-JP') === 0 ? true : null;
  const isHoliday = holidays.map((holiday) => holiday.toString()).includes(date.toString()) ? true : null;
  return (
    <CalendarCellTextContainer date={date} data-saturday={isSaturday} data-holiday={isSunday || isHoliday}>
      {(values: CalendarCellRenderProps) => {
        return (
          !values.isOutsideMonth && (
            <CalendarCellText
              data-today={today}
              data-selected={values.isSelected ? true : null}
              data-disabled={values.isDisabled ? true : null}
              data-unavailable={values.isUnavailable ? true : null}
              data-saturday={isSaturday}
              data-holiday={isSunday || isHoliday}
            >
              {values.formattedDate}
            </CalendarCellText>
          )
        );
      }}
    </CalendarCellTextContainer>
  );
};

export default React.memo(CalendarCell);

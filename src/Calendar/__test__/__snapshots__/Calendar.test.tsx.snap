// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Calendar > renders all dates 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="September 2023"
    class="c0"
    data-rac=""
    id="react-aria-:r1:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        September 2023
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r4:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r2::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:r2:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2023年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r7:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r5::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:r5:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  9月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="September 2023"
      class="c13"
      id="react-aria-:r8:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, August 27, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, August 28, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, August 29, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, August 30, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, August 31, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 1, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                1
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 2, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                2
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 3, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 4, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                4
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 5, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 6, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 7, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 8, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 9, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                9
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 10, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 11, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                11
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 12, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 13, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 14, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 15, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 16, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                16
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 17, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                17
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 18, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                18
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 19, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 20, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 21, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                21
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 22, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                22
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 23, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                23
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 24, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 25, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                25
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 26, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 27, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 28, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 29, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 30, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                30
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders all navigation buttons and test that both works 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="December 2023"
    class="c0"
    data-rac=""
    id="react-aria-:r1re:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        December 2023
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r1rh:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r1rf::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:r1rf:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2023年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r1rk:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r1ri::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:r1ri:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  12月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="December 2023"
      class="c13"
      id="react-aria-:r1rl:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, November 26, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, November 27, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, November 28, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, November 29, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, November 30, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, December 1, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                1
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, December 2, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                2
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, December 3, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, December 4, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                4
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, December 5, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, December 6, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, December 7, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, December 8, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, December 9, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                9
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, December 10, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, December 11, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                11
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, December 12, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, December 13, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, December 14, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, December 15, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, December 16, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                16
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, December 17, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                17
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, December 18, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                18
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, December 19, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, December 20, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, December 21, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                21
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, December 22, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                22
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, December 23, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                23
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, December 24, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, December 25, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                25
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, December 26, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, December 27, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, December 28, 2023"
              class="c17"
              data-focused="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, December 29, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, December 30, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                30
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, December 31, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                31
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, January 1, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, January 2, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, January 3, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, January 4, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, January 5, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, January 6, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders all year & month select, check that both works 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="February 2024"
    class="c0"
    data-rac=""
    id="react-aria-:r278:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        February 2024
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r27b:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r279::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:r279:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2024年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r27e:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r27c::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:r27c:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  2月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="February 2024"
      class="c13"
      id="react-aria-:r27f:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, January 28, 2024"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, January 29, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, January 30, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, January 31, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, February 1, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                1
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, February 2, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                2
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, February 3, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                3
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, February 4, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                4
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, February 5, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, February 6, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, February 7, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, February 8, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, February 9, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                9
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, February 10, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                10
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, February 11, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                11
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, February 12, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, February 13, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, February 14, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, February 15, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, February 16, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                16
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, February 17, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                17
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, February 18, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                18
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, February 19, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, February 20, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, February 21, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                21
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, February 22, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                22
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, February 23, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                23
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, February 24, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                24
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, February 25, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                25
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, February 26, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, February 27, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, February 28, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, February 29, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, March 1, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, March 2, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders with custom holidays 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="May 2024"
    class="c0"
    data-rac=""
    id="react-aria-:rnl:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        May 2024
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:rno:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:rnm::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:rnm:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2024年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rnr:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:rnp::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:rnp:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  5月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="May 2024"
      class="c13"
      id="react-aria-:rns:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, April 28, 2024"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, April 29, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, April 30, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 1, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                1
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 2, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                2
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 3, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 4, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
                data-saturday="true"
              >
                4
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 5, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 6, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, May 7, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 8, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 9, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                9
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 10, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 11, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                11
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 12, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 13, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, May 14, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 15, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 16, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                16
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 17, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                17
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 18, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                18
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 19, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 20, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, May 21, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                21
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 22, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                22
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 23, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                23
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 24, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                24
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 25, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                25
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 26, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 27, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, May 28, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 29, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 30, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                30
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 31, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                31
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, June 1, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders with disabled 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="September 2023"
    class="c0"
    data-disabled="true"
    data-rac=""
    id="react-aria-:r13q:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        September 2023
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        disabled=""
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="true"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r13t:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r13r::trigger"
              aria-required="false"
              class="c9 c10"
              data-disabled=""
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              disabled=""
              id="select:calendar-year-select-:r13r:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2023年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r140:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r13u::trigger"
                aria-required="false"
                class="c9 c10"
                data-disabled=""
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                disabled=""
                id="select:calendar-month-select-:r13u:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  9月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        disabled=""
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="true"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-disabled="true"
      aria-label="September 2023"
      class="c13"
      id="react-aria-:r141:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, August 27, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, August 28, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, August 29, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, August 30, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, August 31, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 1, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                1
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 2, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                2
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 3, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 4, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                4
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 5, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                5
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, September 6, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                6
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 7, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                7
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 8, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                8
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 9, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                9
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 10, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                10
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 11, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                11
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 12, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                12
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, September 13, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                13
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 14, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                14
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 15, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                15
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 16, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                16
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 17, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                17
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 18, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                18
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 19, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                19
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, September 20, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                20
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 21, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                21
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 22, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                22
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 23, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                23
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 24, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 25, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                25
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 26, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                26
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, September 27, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                27
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 28, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                28
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 29, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                29
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 30, 2023"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                30
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        disabled=""
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders with minimum and maximum dates set 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="May 2024"
    class="c0"
    data-rac=""
    id="react-aria-:r13f:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        May 2024
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        disabled=""
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="true"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r13i:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r13g::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:r13g:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2024年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r13l:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r13j::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:r13j:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  5月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        disabled=""
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="true"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="May 2024"
      class="c13"
      id="react-aria-:r13m:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, April 28, 2024"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, April 29, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, April 30, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, May 1, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                1
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, May 2, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                2
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, May 3, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, May 4, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                4
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, May 5, 2024"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                5
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, May 6, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                6
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, May 7, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                7
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, May 8, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                8
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, May 9, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                9
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 10, 2024, First available date"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 11, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                11
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 12, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 13, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, May 14, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, May 15, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, May 16, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                16
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, May 17, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                17
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, May 18, 2024"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                18
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, May 19, 2024"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, May 20, 2024, Last available date"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, May 21, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                21
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, May 22, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                22
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, May 23, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                23
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, May 24, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, May 25, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-saturday="true"
              >
                25
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, May 26, 2024"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
                data-holiday="true"
              >
                26
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, May 27, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                27
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, May 28, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                28
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, May 29, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                29
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, May 30, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                30
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, May 31, 2024"
              class="c17"
              data-disabled="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            >
              <div
                class="c18"
                data-disabled="true"
              >
                31
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, June 1, 2024"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        disabled=""
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders with selected date 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="September 2023"
    class="c0"
    data-rac=""
    id="react-aria-:rbr:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        September 2023
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:rbu:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:rbs::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:rbs:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2023年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rc1:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:rbv::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:rbv:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  9月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="September 2023"
      class="c13"
      id="react-aria-:rc2:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, August 27, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, August 28, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, August 29, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, August 30, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, August 31, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 1, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                1
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 2, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                2
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 3, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 4, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                4
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 5, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 6, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 7, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 8, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 9, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                9
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 10, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 11, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                11
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 12, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 13, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 14, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                14
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 15, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                15
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 16, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                16
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 17, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                17
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 18, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                18
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 19, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                19
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 20, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                20
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 21, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                21
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 22, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                22
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 23, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                23
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 24, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 25, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                25
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 26, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 27, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            aria-selected="true"
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 28, 2023 selected"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-selected="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
                data-selected="true"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 29, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 30, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                30
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

exports[`Calendar > renders with specific disabled dates 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: var(--ajds-color-utility-background-white);
  gap: var(--ajds-spacing-2);
  min-width: -webkit-min-content;
  min-width: -moz-min-content;
  min-width: min-content;
  width: min(326px,100vw - var(--ajds-spacing-8));
}

.c14 tr {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 var(--ajds-spacing-1);
  background-color: var(--ajds-color-utility-background-light);
}

.c15 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: var(--ajds-spacing-10);
  min-width: var(--ajds-spacing-10);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: var(--ajds-spacing-1);
  gap: var(--ajds-spacing-1);
}

.c16 tr {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c16 td {
  all: unset;
}

.c17 {
  all: unset;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-sm);
  font-weight: var(--ajds-font-weight-bold);
  position: relative;
  cursor: pointer;
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-hover-primary-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable]):is([data-saturday],[data-holiday]) {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c17[data-hovered]:not([data-disabled],[data-unavailable])[data-selected] {
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c17[data-saturday] {
  color: var(--ajds-color-status-information);
}

.c17[data-holiday] {
  color: var(--ajds-color-character-accent);
}

.c17[data-selected] {
  background-color: var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-character-primary-white);
}

.c17[data-focus-visible] {
  outline: 2px solid var(--ajds-color-interactive-focus-primary);
  outline-offset: var(--ajds-spacing-0-5);
}

.c17:is([data-disabled],[data-unavailable]) {
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c17[data-selected]:is([data-disabled],[data-unavailable]) {
  background-color: var(--ajds-color-interactive-disabled-dark);
  color: var(--ajds-color-interactive-disabled-light);
}

.c17[data-outside-month] {
  pointer-events: none;
}

.c18 {
  position: absolute;
  width: var(--ajds-spacing-6);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c18[data-today]::after {
  position: absolute;
  content: '';
  display: block;
  width: var(--ajds-spacing-4);
  height: var(--ajds-spacing-1);
  border-radius: var(--ajds-radius-lg);
  background: var(--ajds-color-character-primary);
  bottom: -4px;
}

.c18[data-saturday]::after {
  background: var(--ajds-color-status-information);
}

.c18[data-holiday]::after {
  background: var(--ajds-color-character-accent);
}

.c18[data-selected]::after {
  background: var(--ajds-color-character-primary-white);
}

.c18:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-dark);
}

.c18[data-selected]:is([data-disabled],[data-unavailable])::after {
  background: var(--ajds-color-interactive-disabled-light);
}

.c5 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--ajds-spacing-1);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.c7 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c8 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c8 input,
.c8 select,
.c8 button,
.c8 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c8 input {
  overflow: visible;
}

.c8 select {
  text-transform: none;
}

.c8 textarea {
  overflow: auto;
}

.c10 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  position: relative;
  width: 100%;
  height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.c10::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c10:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c9:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c10:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c10[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10:focus-within:not([data-disabled]) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c10[data-disabled] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled] svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c10 span:first-of-type {
  overflow: hidden;
  text-overflow: ellipsis;
}

.c10[data-placeholder] {
  color: var(--ajds-color-interactive-placeholder);
}

.c10[data-state='open'] svg {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c11 {
  height: var(--ajds-spacing-6);
  color: var(--ajds-color-character-primary);
}

.c12 .field-input-wrap {
  min-width: 5.5rem;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  max-height: var(--ajds-spacing-12);
}

.c2 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c2 svg {
  color: currentColor;
}

.c2:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c2:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c3 svg {
  color: currentColor;
}

.c3[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c4 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    aria-label="September 2023"
    class="c0"
    data-rac=""
    id="react-aria-:r1fk:"
    role="application"
  >
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <h2>
        September 2023
      </h2>
    </div>
    <header
      class="c1"
    >
      <button
        aria-label="calendar-previous-month-button"
        class="c2"
        data-testid="calendar-previous-month-button"
        name="calendar-previous-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
      <div
        class="c6"
      >
        <div
          class="c7"
        >
          <div
            class="c8 field-input-wrap"
          >
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r1fn:"
              aria-expanded="false"
              aria-invalid="false"
              aria-label="select:calendar-year-select-:r1fl::trigger"
              aria-required="false"
              class="c9 c10"
              data-state="closed"
              data-testid="calendar-year-select-trigger"
              dir="ltr"
              id="select:calendar-year-select-:r1fl:"
              role="combobox"
              type="button"
            >
              <span
                data-testid="calendar-year-select-value-text"
                style="pointer-events: none;"
              >
                2023年
              </span>
              <span
                aria-hidden="true"
                class="c11"
              >
                <svg
                  class="c5"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                  />
                  ;
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div
          class="c12"
        >
          <div
            class="c7"
          >
            <div
              class="c8 field-input-wrap"
            >
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r1fq:"
                aria-expanded="false"
                aria-invalid="false"
                aria-label="select:calendar-month-select-:r1fo::trigger"
                aria-required="false"
                class="c9 c10"
                data-state="closed"
                data-testid="calendar-month-select-trigger"
                dir="ltr"
                id="select:calendar-month-select-:r1fo:"
                role="combobox"
                type="button"
              >
                <span
                  data-testid="calendar-month-select-value-text"
                  style="pointer-events: none;"
                >
                  9月
                </span>
                <span
                  aria-hidden="true"
                  class="c11"
                >
                  <svg
                    class="c5"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(90deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
                    />
                    ;
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="calendar-next-month-button"
        class="c2"
        data-testid="calendar-next-month-button"
        name="calendar-next-month-button"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c3"
          data-disabled="false"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
        >
          <span
            class="c4"
          >
            <svg
              class="c5"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
              />
              ;
            </svg>
          </span>
        </div>
      </button>
    </header>
    <table
      aria-label="September 2023"
      class="c13"
      id="react-aria-:r1fr:"
      role="grid"
    >
      <thead
        aria-hidden="true"
        class="c14"
      >
        <tr>
          <th
            class="c15"
          >
            S
          </th>
          <th
            class="c15"
          >
            M
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            W
          </th>
          <th
            class="c15"
          >
            T
          </th>
          <th
            class="c15"
          >
            F
          </th>
          <th
            class="c15"
          >
            S
          </th>
        </tr>
      </thead>
      <tbody
        class="c16"
      >
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, August 27, 2023"
              class="c17"
              data-disabled="true"
              data-holiday="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, August 28, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, August 29, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, August 30, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, August 31, 2023"
              class="c17"
              data-disabled="true"
              data-outside-month="true"
              data-outside-visible-range="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
            />
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 1, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                1
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 2, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
                data-unavailable="true"
              >
                2
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 3, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
                data-unavailable="true"
              >
                3
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 4, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                4
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 5, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                5
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 6, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                6
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 7, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                7
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 8, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                8
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 9, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                9
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            role="gridcell"
          >
            <div
              aria-label="Sunday, September 10, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
              >
                10
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 11, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                11
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 12, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                12
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 13, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                13
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 14, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                14
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 15, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                15
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 16, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
                data-unavailable="true"
              >
                16
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 17, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
                data-unavailable="true"
              >
                17
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Monday, September 18, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                18
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Tuesday, September 19, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                19
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Wednesday, September 20, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                20
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Thursday, September 21, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                21
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Friday, September 22, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-unavailable="true"
              >
                22
              </div>
            </div>
          </td>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Saturday, September 23, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
                data-unavailable="true"
              >
                23
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td
            aria-disabled="true"
            role="gridcell"
          >
            <div
              aria-disabled="true"
              aria-label="Sunday, September 24, 2023"
              class="c17"
              data-holiday="true"
              data-rac=""
              data-react-aria-pressable="true"
              data-unavailable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-holiday="true"
                data-unavailable="true"
              >
                24
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Monday, September 25, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                25
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Tuesday, September 26, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                26
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Wednesday, September 27, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                27
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Thursday, September 28, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="0"
            >
              <div
                class="c18"
              >
                28
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Friday, September 29, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
              >
                29
              </div>
            </div>
          </td>
          <td
            role="gridcell"
          >
            <div
              aria-label="Saturday, September 30, 2023"
              class="c17"
              data-rac=""
              data-react-aria-pressable="true"
              data-saturday="true"
              role="button"
              tabindex="-1"
            >
              <div
                class="c18"
                data-saturday="true"
              >
                30
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div
      style="border: 0px; clip-path: inset(50%); height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap;"
    >
      <button
        aria-label="Next"
        tabindex="-1"
      />
    </div>
  </div>
</div>
`;

import React from 'react';
import { CalendarDate } from '@internationalized/date';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { render } from '../../utils/testUtils';
import Calendar from '../Calendar';
import { CALENDAR_YEAR_SELECT_LABEL, CALENDAR_MONTH_SELECT_LABEL } from '../../DatePickerField/constants';

window.PointerEvent = class PointerEvent extends Event {};
window.HTMLElement.prototype.scrollIntoView = vi.fn();
window.HTMLElement.prototype.hasPointerCapture = vi.fn();
window.HTMLElement.prototype.releasePointerCapture = vi.fn();

describe('Calendar', () => {
  test('renders all dates', () => {
    const { queryAllByRole, container } = render(<Calendar defaultFocusedValue={new CalendarDate(2023, 9, 28)} />);
    const dateElements = queryAllByRole('button').filter(
      (element) => element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(30);

    expect(container).toMatchSnapshot();
  }, 20000);

  test('renders with selected date', () => {
    const { queryAllByRole, container } = render(<Calendar value={new CalendarDate(2023, 9, 28)} />);
    const dateElements = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-selected') === 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(1);

    expect(container).toMatchSnapshot();
  }, 20000);

  test('renders with custom holidays', () => {
    const { queryAllByRole, container } = render(
      <Calendar defaultFocusedValue={new CalendarDate(2024, 5, 1)} holidays={['2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06']} />,
    );
    const dateElements = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-holiday') === 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(7);

    expect(container).toMatchSnapshot();
  }, 20000);

  test('renders with minimum and maximum dates set', () => {
    const { queryAllByRole, getByTestId, container } = render(
      <Calendar
        defaultFocusedValue={new CalendarDate(2024, 5, 16)}
        minValue={new CalendarDate(2024, 5, 10)}
        maxValue={new CalendarDate(2024, 5, 20)}
      />,
    );
    const dateElements = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-disabled') !== 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(11);

    // Check that year & month dropdown is rendered
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });

  test('renders with disabled', () => {
    const { queryAllByRole, container } = render(<Calendar defaultFocusedValue={new CalendarDate(2023, 9, 28)} isDisabled />);
    const dateElements = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-disabled') === 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(30);

    expect(container).toMatchSnapshot();
  }, 20000);

  test('renders with specific disabled dates', () => {
    const { queryAllByRole, container } = render(
      <Calendar
        defaultFocusedValue={new CalendarDate(2023, 9, 28)}
        isDateUnavailable={(date) => {
          const dateRange = [
            [new CalendarDate(2023, 9, 1), new CalendarDate(2023, 9, 5)],
            [new CalendarDate(2023, 9, 8), new CalendarDate(2023, 9, 8)],
            [new CalendarDate(2023, 9, 14), new CalendarDate(2023, 9, 24)],
          ];
          return dateRange.some((interval) => date.compare(interval[0]) >= 0 && date.compare(interval[1]) <= 0);
        }}
      />,
    );
    const dateElements = queryAllByRole('button').filter(
      (element) =>
        element.tagName === 'DIV' && element.getAttribute('data-outside-month') !== 'true' && element.getAttribute('data-unavailable') === 'true',
    );
    dateElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    expect(dateElements).toHaveLength(17);

    expect(container).toMatchSnapshot();
  });

  test('renders all navigation buttons and test that both works', async () => {
    const { queryAllByRole, getByTestId, container } = render(<Calendar defaultFocusedValue={new CalendarDate(2023, 12, 28)} />);
    const previousMonthButton = queryAllByRole('button').find(
      (element) => element.tagName === 'BUTTON' && element.getAttribute('name') === 'calendar-previous-month-button',
    );
    expect(previousMonthButton).toBeInTheDocument();
    const nextMonthButton = queryAllByRole('button').find(
      (element) => element.tagName === 'BUTTON' && element.getAttribute('name') === 'calendar-next-month-button',
    );
    expect(nextMonthButton).toBeInTheDocument();

    // navigate to next month
    await userEvent.click(nextMonthButton as HTMLButtonElement);

    // Check that year & month dropdown value updated
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();
    expect(yearSelect).toHaveTextContent('2024年');

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();
    expect(monthSelect).toHaveTextContent('1月');

    // navigate to previous month
    await userEvent.click(previousMonthButton as HTMLButtonElement);

    // Check that year & month dropdown value updated
    expect(yearSelect).toHaveTextContent('2023年');
    expect(monthSelect).toHaveTextContent('12月');

    expect(container).toMatchSnapshot();
  }, 20000);

  test('renders all year & month select, check that both works', async () => {
    const { getByTestId, getAllByTestId, container } = render(<Calendar defaultFocusedValue={new CalendarDate(2023, 9, 28)} />);
    const yearSelect = getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-value-text`);
    expect(yearSelect).toBeInTheDocument();
    expect(yearSelect).toHaveTextContent('2023年');

    const monthSelect = getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-value-text`);
    expect(monthSelect).toBeInTheDocument();
    expect(monthSelect).toHaveTextContent('9月');

    await userEvent.click(getByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-trigger`));
    const yearOption = getAllByTestId(`${CALENDAR_YEAR_SELECT_LABEL}-item-2024`);
    expect(yearOption[0]).toBeInTheDocument();

    await userEvent.click(yearOption[0]);
    expect(yearSelect).toHaveTextContent('2024年');

    await userEvent.click(getByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-trigger`));
    const monthOption = getAllByTestId(`${CALENDAR_MONTH_SELECT_LABEL}-item-2`);
    expect(monthOption[0]).toBeInTheDocument();

    await userEvent.click(monthOption[0]);
    expect(monthSelect).toHaveTextContent('2月');

    expect(container).toMatchSnapshot();
  }, 20000);
});

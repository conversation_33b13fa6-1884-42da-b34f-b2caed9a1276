import React from 'react';
import { CalendarDate } from '@internationalized/date';
import Calendar from '../Calendar';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Calendar', () => {
  test('WithFocusedDate', async ({ task }) => {
    await takeScreenshot(<Calendar focusedValue={new CalendarDate(2023, 9, 28)} />, task, {
      interactionSelector: 'div[role="button"][tabindex="0"]',
      interactionType: 'focus',
      viewport: { height: 800 },
    });
  });

  test('WithSelectedDateAndFocusStyle', async ({ task }) => {
    await takeScreenshot(<Calendar value={new CalendarDate(2023, 9, 28)} />, task, {
      interactionSelector: 'div[role="button"][tabindex="0"]',
      interactionType: 'focus',
      viewport: { height: 800 },
    });
  });

  test('WithMinAndMaxDate', async ({ task }) => {
    await takeScreenshot(
      <Calendar focusedValue={new CalendarDate(2023, 9, 15)} minValue={new CalendarDate(2023, 9, 8)} maxValue={new CalendarDate(2023, 9, 20)} />,
      task,
      { viewport: { height: 800 } },
    );
  });

  test('WithHolidays', async ({ task }) => {
    await takeScreenshot(
      <Calendar focusedValue={new CalendarDate(2024, 5, 1)} holidays={['2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06']} />,
      task,
      { viewport: { height: 800 } },
    );
  });

  test('WithDisabled', async ({ task }) => {
    await takeScreenshot(<Calendar focusedValue={new CalendarDate(2023, 9, 28)} isDisabled />, task, {
      viewport: { height: 800 },
    });
  });

  test('WithDisabledSpecificDates', async ({ task }) => {
    await takeScreenshot(
      <Calendar
        focusedValue={new CalendarDate(2023, 9, 28)}
        isDateUnavailable={(date) => {
          const dateRange = [
            [new CalendarDate(2023, 9, 1), new CalendarDate(2023, 9, 5)],
            [new CalendarDate(2023, 9, 8), new CalendarDate(2023, 9, 8)],
            [new CalendarDate(2023, 9, 14), new CalendarDate(2023, 9, 24)],
          ];
          return dateRange.some((interval) => date.compare(interval[0]) >= 0 && date.compare(interval[1]) <= 0);
        }}
      />,
      task,
      { viewport: { height: 800 } },
    );
  });
});

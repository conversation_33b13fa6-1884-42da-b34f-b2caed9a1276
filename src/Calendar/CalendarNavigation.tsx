import React, { useContext } from 'react';
import { CalendarStateContext } from 'react-aria-components';
import ChevronIcon from '../Icons/ChevronIcon';
import CalendarNavigationHeader from './CalendarNavigationHeader';
import CalendarMonthSelect from './CalendarMonthSelect';
import CalendarYearSelect from './CalendarYearSelect';
import CalendarYearMonthSelectContainer from './CalendarYearMonthSelectContainer';
import IconButton from '../IconButton';
import { NEXT_MONTH_BUTTON_LABEL, PREVIOUS_MONTH_BUTTON_LABEL } from '../DatePickerField/constants';

const CalendarNavigation: React.FC = () => {
  const { isDisabled, setFocusedDate, focusedDate, minValue, maxValue } = useContext(CalendarStateContext)!;
  return (
    <CalendarNavigationHeader>
      <IconButton
        name={PREVIOUS_MONTH_BUTTON_LABEL}
        aria-label={PREVIOUS_MONTH_BUTTON_LABEL}
        data-testid={PREVIOUS_MONTH_BUTTON_LABEL}
        icon={<ChevronIcon rotation="180" />}
        // If currently focused year & month equal minimum year & month, disable previous button
        disabled={isDisabled || minValue?.set({ day: 1 }).compare(focusedDate.set({ day: 1 })) === 0}
        onClick={() => setFocusedDate(focusedDate.subtract({ months: 1 }))}
      />
      <CalendarYearMonthSelectContainer>
        <CalendarYearSelect />
        <CalendarMonthSelect />
      </CalendarYearMonthSelectContainer>
      <IconButton
        name={NEXT_MONTH_BUTTON_LABEL}
        aria-label={NEXT_MONTH_BUTTON_LABEL}
        data-testid={NEXT_MONTH_BUTTON_LABEL}
        icon={<ChevronIcon />}
        // If currently focused year & month equal maximum year & month, disable next button
        disabled={isDisabled || maxValue?.set({ day: 1 }).compare(focusedDate.set({ day: 1 })) === 0}
        onClick={() => {
          setFocusedDate(focusedDate.add({ months: 1 }));
        }}
      />
    </CalendarNavigationHeader>
  );
};

export default React.memo(CalendarNavigation);

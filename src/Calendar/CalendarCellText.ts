import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getRadiusVar } from '../radius';

const CalendarCellText = styled.div`
  position: absolute;
  width: ${getSpacingVar(6)};
  display: flex;
  justify-content: center;

  &[data-today]::after {
    position: absolute;
    content: '';
    display: block;
    width: ${getSpacingVar(4)};
    height: ${getSpacingVar(1)};
    border-radius: ${getRadiusVar('lg')};
    background: ${getColorVar('characterPrimary')};
    bottom: -4px;
  }

  &[data-saturday]::after {
    background: ${getColorVar('statusInformation')};
  }

  &[data-holiday]::after {
    background: ${getColorVar('characterAccent')};
  }

  &[data-selected]::after {
    background: ${getColorVar('characterPrimaryWhite')};
  }

  &:is([data-disabled], [data-unavailable])::after {
    background: ${getColorVar('interactiveDisabledDark')};
  }

  &[data-selected]:is([data-disabled], [data-unavailable])::after {
    background: ${getColorVar('interactiveDisabledLight')};
  }
`;

export default CalendarCellText;

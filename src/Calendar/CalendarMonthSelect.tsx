import React, { useContext, useId, useMemo } from 'react';
import { CalendarStateContext } from 'react-aria-components';
import styled from 'styled-components';
import SelectField from '../SelectField';
import { DEFAULT_START_MONTH, DEFAULT_END_MONTH, CALENDAR_MONTH_SELECT_LABEL } from '../DatePickerField/constants';

/** This wrapper is required to stop wrapping of longer entries */
const FixedWidthSelect = styled.div`
  .field-input-wrap {
    min-width: 5.5rem;
  }
`;

const CalendarMonthSelect: React.FC = () => {
  const id = useId();
  const { visibleRange, focusedDate, minValue: minDate, maxValue: maxDate, isDisabled, setFocusedDate } = useContext(CalendarStateContext)!;
  // The minimum and maximum selectable months are 1-12 by default, but it can be be changed
  // based on the minValue & maxValue prop set by the user. For example, if the user set 2024/05/01
  // as the minValue, and 2024/09/01 as the maxValue. The selectable months will be 5-9.
  const monthList = useMemo(() => {
    const startMonth = minDate?.year && minDate?.year === visibleRange.start.year ? minDate.month : DEFAULT_START_MONTH;
    const endMonth = maxDate?.year && maxDate.year === visibleRange.start.year ? maxDate.month + 1 : DEFAULT_END_MONTH + 1;
    const size = endMonth - startMonth;
    const monthArray: { text: string; value: string }[] = new Array(size);
    for (let i = 0; i < size; i++) {
      monthArray[i] = {
        text: `${i + startMonth}月`,
        value: `${i + startMonth}`,
      };
    }
    return monthArray;
  }, [maxDate?.month, maxDate?.year, minDate?.month, minDate?.year, visibleRange.start.year]);

  return (
    <FixedWidthSelect>
      <SelectField
        aria-label={CALENDAR_MONTH_SELECT_LABEL}
        name={CALENDAR_MONTH_SELECT_LABEL}
        data-testid={CALENDAR_MONTH_SELECT_LABEL}
        id={`${CALENDAR_MONTH_SELECT_LABEL}-${id}`}
        options={monthList}
        value={focusedDate.month.toString()}
        disabled={isDisabled}
        onValueChange={(value) => setFocusedDate(visibleRange.start.set({ month: parseInt(value, 10) }))}
      />
    </FixedWidthSelect>
  );
};

export default React.memo(CalendarMonthSelect);

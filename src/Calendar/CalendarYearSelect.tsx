import React, { useContext, useId, useMemo } from 'react';
import { CalendarStateContext } from 'react-aria-components';
import SelectField from '../SelectField';
import { DEFAULT_START_YEAR, DEFAULT_END_YEAR, CALENDAR_YEAR_SELECT_LABEL } from '../DatePickerField/constants';

const CalendarYearSelect: React.FC = () => {
  const id = useId();
  const { visibleRange, focusedDate, minValue: minDate, maxValue: maxDate, isDisabled, setFocusedDate } = useContext(CalendarStateContext)!;
  // The minimum and maximum selectable years are 1868-3000 by default, but it can be be changed
  // based on the minValue & maxValue prop set by the user
  const yearList = useMemo(() => {
    const startYear = minDate?.year ?? DEFAULT_START_YEAR;
    const size = (maxDate?.year ? maxDate.year + 1 : DEFAULT_END_YEAR) - startYear;
    const yearArray: { text: string; value: string }[] = new Array(size);
    for (let i = 0; i < size; i++) {
      yearArray[i] = {
        text: `${i + startYear}年`,
        value: `${i + startYear}`,
      };
    }
    return yearArray;
  }, [maxDate?.year, minDate?.year]);

  return (
    <SelectField
      aria-label={CALENDAR_YEAR_SELECT_LABEL}
      name={CALENDAR_YEAR_SELECT_LABEL}
      data-testid={CALENDAR_YEAR_SELECT_LABEL}
      id={`${CALENDAR_YEAR_SELECT_LABEL}-${id}`}
      options={yearList}
      value={focusedDate.year.toString()}
      disabled={isDisabled}
      onValueChange={(value) => setFocusedDate(visibleRange.start.set({ year: parseInt(value, 10) }))}
    />
  );
};

export default React.memo(CalendarYearSelect);

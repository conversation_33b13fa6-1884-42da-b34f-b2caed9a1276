import styled from 'styled-components';
import { CalendarCell } from 'react-aria-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

const CalendarCellTextContainer = styled(CalendarCell)`
  all: unset;
  display: flex;
  justify-content: center;
  align-items: center;
  height: ${getSpacingVar(10)};
  width: ${getSpacingVar(10)};
  border-radius: ${getRadiusVar('sm')};
  font-weight: ${getTypographyVar('boldFontWeight')};
  position: relative;
  cursor: pointer;

  &[data-hovered]:not([data-disabled], [data-unavailable]) {
    background-color: ${getColorVar('interactiveHoverPrimaryTransparent')};

    &:is([data-saturday], [data-holiday]) {
      background-color: ${getColorVar('interactiveHoverGreyTransparent')};
    }

    &[data-selected] {
      background-color: ${getColorVar('interactiveHoverPrimary')};
    }
  }

  &[data-saturday] {
    color: ${getColorVar('statusInformation')};
  }

  &[data-holiday] {
    color: ${getColorVar('characterAccent')};
  }

  &[data-selected] {
    background-color: ${getColorVar('interactiveActivePrimary')};
    color: ${getColorVar('characterPrimaryWhite')};
  }

  &[data-focus-visible] {
    outline: 2px solid ${getColorVar('interactiveFocusPrimary')};
    outline-offset: ${getSpacingVar(0.5)};
  }

  &:is([data-disabled], [data-unavailable]) {
    color: ${getColorVar('interactiveDisabledDark')};
    cursor: not-allowed;
  }

  &[data-selected]:is([data-disabled], [data-unavailable]) {
    background-color: ${getColorVar('interactiveDisabledDark')};
    color: ${getColorVar('interactiveDisabledLight')};
  }

  &[data-outside-month] {
    pointer-events: none;
  }
`;

export default CalendarCellTextContainer;

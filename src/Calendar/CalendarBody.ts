import styled from 'styled-components';
import { CalendarGridBody } from 'react-aria-components';
import { getSpacingVar } from '../spacing';

const CalendarBody = styled(CalendarGridBody)`
  display: flex;
  flex-direction: column;
  padding: ${getSpacingVar(1)};
  gap: ${getSpacingVar(1)};

  tr {
    display: flex;
    justify-content: space-between;
  }

  td {
    all: unset;
  }
`;

export default CalendarBody;

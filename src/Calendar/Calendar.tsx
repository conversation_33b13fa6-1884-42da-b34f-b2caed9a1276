import React, { FC } from 'react';
import { DateValue } from '@internationalized/date';
import { CalendarProps as RACCalendarProps } from 'react-aria-components';
import CalendarBase from './CalendarBase';
import CalendarHeader from './CalendarHeader';
import CalendarHeaderCell from './CalendarHeaderCell';
import CalendarContent from './CalendarContent';
import CalendarBody from './CalendarBody';
import CalendarCell, { IsoDateString } from './CalendarCell';
import CalendarNavigation from './CalendarNavigation';

export type CommonCalendarProps = {
  /** List of public holidays to be highlighted in the Calendar. */
  holidays?: IsoDateString[];
};

type CalendarProps = CommonCalendarProps &
  Omit<RACCalendarProps<DateValue>, 'visibleDuration' | 'pageBehavior' | 'children' | 'className' | 'style' | 'isReadOnly' | 'autoFocus' | 'slot'>;

const headerFn = (day: string) => <CalendarHeaderCell>{day}</CalendarHeaderCell>;

const Calendar: FC<CalendarProps> = ({ holidays = [], onChange, ...rest }) => {
  return (
    // We have to turn Ark UI's calendar autofocus off, so that focus doesn't move to calendar on open
    // eslint-disable-next-line jsx-a11y/no-autofocus
    <CalendarBase autoFocus={false} {...rest}>
      <CalendarNavigation />
      <CalendarContent>
        <CalendarHeader>{headerFn}</CalendarHeader>
        <CalendarBody>{(date) => <CalendarCell date={date} holidays={holidays} />}</CalendarBody>
      </CalendarContent>
    </CalendarBase>
  );
};

Calendar.displayName = 'Calendar';

export default React.memo(Calendar);

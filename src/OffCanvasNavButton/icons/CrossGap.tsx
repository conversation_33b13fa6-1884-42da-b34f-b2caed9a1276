import React from 'react';

const CrossGap: React.FC<React.ComponentPropsWithoutRef<'svg'>> = (props) => {
  return (
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" {...props}>
      <path d="M12 13.333c-0.333 0-0.667-0.133-0.933-0.4l-9.333-9.333c-1.2-1.2 0.667-3.133 1.867-1.867l9.333 9.333c0.8 0.8 0.2 2.267-0.933 2.267z" />
      <path d="M29.333 30.667c-0.333 0-0.667-0.133-0.933-0.4l-9.333-9.333c-1.2-1.2 0.667-3.133 1.867-1.867l9.333 9.333c0.8 0.8 0.2 2.267-0.933 2.267z" />
      <path d="M2.667 30.667c-1.133 0-1.733-1.467-0.933-2.267l26.667-26.667c1.2-1.2 3.133 0.667 1.867 1.867l-26.667 26.667c-0.267 0.267-0.6 0.4-0.933 0.4z" />
    </svg>
  );
};

export default CrossGap;

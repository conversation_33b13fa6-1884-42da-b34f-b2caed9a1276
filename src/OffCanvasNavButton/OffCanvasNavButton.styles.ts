import styled from 'styled-components';
import { getColorVar } from '../colors';
import media from '../Breakpoints/Breakpoints';
import buttonElBase from '../styles/buttonElBase';

export const OffCanvasNavButtonBase = styled.button`
  ${buttonElBase}

  background-color: ${getColorVar('white')};
  border-bottom: none;
  border-left: 1px solid ${getColorVar('utilityStrokeLight')};
  border-right: none;
  border-top: none;
  line-height: 1;
  padding-left: 14px;
  padding-right: 0;

  svg {
    color: ${getColorVar('characterPrimary')};

    ${media.smallOnly} {
      height: 20px;
      width: 20px;
    }

    ${media.mediumOnly} {
      height: 25px;
      width: 25px;
    }
  }
`;

export default OffCanvasNavButtonBase;

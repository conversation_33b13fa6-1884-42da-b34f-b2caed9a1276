import React from 'react';
import CrossGap from './icons/CrossGap';
import MenuIcon from './icons/MenuIcon';
import { OffCanvasNavButtonBase } from './OffCanvasNavButton.styles';

export type OffCanvasNavButtonProps = {
  /** Flag to switch the icon if the menu is open */
  isNavOpen: boolean;
  /** Click event for the button */
  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
} & React.ComponentPropsWithoutRef<'button'>;

const OffCanvasNavButton: React.FC<React.PropsWithChildren<OffCanvasNavButtonProps>> = (props) => {
  const { onClick, isNavOpen } = props;
  return <OffCanvasNavButtonBase onClick={onClick}>{isNavOpen ? <CrossGap /> : <MenuIcon />}</OffCanvasNavButtonBase>;
};

export default OffCanvasNavButton;

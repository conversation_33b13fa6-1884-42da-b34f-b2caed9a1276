import styled from 'styled-components';
import { Checkbox } from '@ark-ui/react';

import { getColorVar } from '../colors';
import input from '../styles/input';

const CheckboxRoot = styled(Checkbox.Root)`
  ${input.item}
  width: fit-content;

  &:has(:focus-visible) {
    box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
  }

  &[data-disabled] {
    &:has(:focus-visible) {
      box-shadow: initial;
    }
  }

  &[data-invalid] {
    &:has(:focus-visible) {
      box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
    }
  }
`;

export default CheckboxRoot;

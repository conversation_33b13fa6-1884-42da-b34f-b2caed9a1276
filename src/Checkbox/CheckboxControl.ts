import styled from 'styled-components';
import { Checkbox } from '@ark-ui/react';
import { getColorVar } from '../colors';
import CheckboxRoot from './CheckboxRoot';
import checkbox from '../styles/checkbox';

const CheckboxControl = styled(Checkbox.Control)`
  ${checkbox.control}

  &[data-state='checked'] {
    ${CheckboxRoot}:hover > &:not([data-invalid], [data-disabled]) {
      border: 2px solid ${getColorVar('interactiveHoverPrimary')};
      background-color: ${getColorVar('interactiveHoverPrimary')};
    }
  }
`;

export default CheckboxControl;

import React, { forwardRef } from 'react';
import { Checkbox as Ark<PERSON>heck<PERSON>, CheckboxRootBaseProps } from '@ark-ui/react';
import CheckboxRoot from './CheckboxRoot';
import CheckboxControl from './CheckboxControl';
import CheckboxLabel from './CheckboxLabel';
import { CheckIcon } from '../Icons';

export type CheckboxOptions = {
  /** Label text */
  label: string;
  /** For react testing library tests */
  'data-testid'?: string;
  /**
   * The checked state of the checkbox when it is first rendered. Use this when you do not need to control the state of the checkbox.
   * @deprecated This will be removed soon. Use the defaultValue on CheckboxGroupField instead.
   */
  defaultChecked?: boolean;
} & Pick<React.ComponentPropsWithRef<'input'>, 'id' | 'ref' | 'onChange' | 'onBlur' | 'name'> &
  Required<Pick<CheckboxRootBaseProps, 'value'>>;

type CheckboxCommonProps = {
  /** Shows error state. Note: should be passed down from Checkbox component */
  hasError?: boolean;
} & Pick<CheckboxRootBaseProps, 'form' | 'checked' | 'onCheckedChange' | 'disabled'> &
  Pick<React.ComponentPropsWithRef<'input'>, 'aria-checked'>;

const Checkbox = forwardRef<HTMLInputElement, CheckboxOptions & CheckboxCommonProps>(
  ({ label, onChange, onBlur, name, hasError = false, ...rest }, ref) => {
    return (
      <CheckboxRoot invalid={hasError} {...rest}>
        <CheckboxControl>
          <CheckIcon size="small" isStroke />
        </CheckboxControl>
        {label !== '' && <CheckboxLabel>{label}</CheckboxLabel>}
        <ArkCheckbox.HiddenInput ref={ref} onBlur={onBlur} onChange={onChange} name={name} />
      </CheckboxRoot>
    );
  },
);

Checkbox.displayName = 'Checkbox';

export default React.memo(Checkbox);

import React from 'react';
import { render } from '../../utils/testUtils';
import Checkbox from '../Checkbox';

describe('Checkbox', () => {
  test('renders without crashing', () => {
    const { queryAllByText, container } = render(<Checkbox id="check-id" label="check" value="check1" name="check1" defaultChecked />);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(1);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { getAllByRole, container } = render(<Checkbox id="check-id" label="" value="check1" name="check1" />);

    const allTestElements = getAllByRole('checkbox');
    expect(allTestElements.length).toEqual(1);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled', () => {
    const { queryAllByText, container } = render(<Checkbox id="check-id" label="check" value="check1" name="check1" disabled={true} />);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(1);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with hasError true', () => {
    const { queryAllByText, container } = render(<Checkbox id="check-id" label="check" value="check1" name="check1" hasError={true} />);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(1);
    expect(container).toMatchSnapshot();
  });
});

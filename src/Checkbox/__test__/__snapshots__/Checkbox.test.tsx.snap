// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Checkbox > renders without crashing 1`] = `
.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c1[data-disabled] {
  cursor: not-allowed;
}

.c1[data-disabled]:hover {
  background-color: initial;
}

.c1:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c1[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c3[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c3[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c3[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c3[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c0:hover > .c2[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c5[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c5[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <label
    class="c0 c1"
    data-part="root"
    data-scope="checkbox"
    data-state="checked"
    dir="ltr"
    for="checkbox:check-id:input"
    id="checkbox:check-id"
  >
    <div
      aria-hidden="true"
      class="c2 c3"
      data-part="control"
      data-scope="checkbox"
      data-state="checked"
      dir="ltr"
      id="checkbox:check-id:control"
    >
      <svg
        class="c4"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
        />
      </svg>
    </div>
    <span
      class="c5"
      data-part="label"
      data-scope="checkbox"
      data-state="checked"
      dir="ltr"
      id="checkbox:check-id:label"
    >
      check
    </span>
    <input
      aria-invalid="false"
      aria-labelledby="checkbox:check-id:label"
      checked=""
      id="checkbox:check-id:input"
      name="check1"
      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
      type="checkbox"
      value="check1"
    />
  </label>
</div>
`;

exports[`Checkbox > renders without crashing with disabled 1`] = `
.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c1[data-disabled] {
  cursor: not-allowed;
}

.c1[data-disabled]:hover {
  background-color: initial;
}

.c1:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c1[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c3[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c3[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c3[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c3[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c0:hover > .c2[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c5[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c5[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <label
    class="c0 c1"
    data-disabled=""
    data-part="root"
    data-scope="checkbox"
    data-state="unchecked"
    dir="ltr"
    for="checkbox:check-id:input"
    id="checkbox:check-id"
  >
    <div
      aria-hidden="true"
      class="c2 c3"
      data-disabled=""
      data-part="control"
      data-scope="checkbox"
      data-state="unchecked"
      dir="ltr"
      id="checkbox:check-id:control"
    >
      <svg
        class="c4"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
        />
      </svg>
    </div>
    <span
      class="c5"
      data-disabled=""
      data-part="label"
      data-scope="checkbox"
      data-state="unchecked"
      dir="ltr"
      id="checkbox:check-id:label"
    >
      check
    </span>
    <input
      aria-invalid="false"
      aria-labelledby="checkbox:check-id:label"
      disabled=""
      id="checkbox:check-id:input"
      name="check1"
      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
      type="checkbox"
      value="check1"
    />
  </label>
</div>
`;

exports[`Checkbox > renders without crashing with hasError true 1`] = `
.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c1[data-disabled] {
  cursor: not-allowed;
}

.c1[data-disabled]:hover {
  background-color: initial;
}

.c1:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c1[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c3[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c3[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c3[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c3[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c0:hover > .c2[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c5[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c5[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <label
    class="c0 c1"
    data-invalid=""
    data-part="root"
    data-scope="checkbox"
    data-state="unchecked"
    dir="ltr"
    for="checkbox:check-id:input"
    id="checkbox:check-id"
  >
    <div
      aria-hidden="true"
      class="c2 c3"
      data-invalid=""
      data-part="control"
      data-scope="checkbox"
      data-state="unchecked"
      dir="ltr"
      id="checkbox:check-id:control"
    >
      <svg
        class="c4"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
        />
      </svg>
    </div>
    <span
      class="c5"
      data-invalid=""
      data-part="label"
      data-scope="checkbox"
      data-state="unchecked"
      dir="ltr"
      id="checkbox:check-id:label"
    >
      check
    </span>
    <input
      aria-invalid="true"
      aria-labelledby="checkbox:check-id:label"
      id="checkbox:check-id:input"
      name="check1"
      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
      type="checkbox"
      value="check1"
    />
  </label>
</div>
`;

exports[`Checkbox > renders without crashing without label 1`] = `
.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c1[data-disabled] {
  cursor: not-allowed;
}

.c1[data-disabled]:hover {
  background-color: initial;
}

.c1:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c1[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c3[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c3[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c3[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c3[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c3[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c3[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c3[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c0:hover > .c2[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <label
    class="c0 c1"
    data-part="root"
    data-scope="checkbox"
    data-state="unchecked"
    dir="ltr"
    for="checkbox:check-id:input"
    id="checkbox:check-id"
  >
    <div
      aria-hidden="true"
      class="c2 c3"
      data-part="control"
      data-scope="checkbox"
      data-state="unchecked"
      dir="ltr"
      id="checkbox:check-id:control"
    >
      <svg
        class="c4"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
        />
      </svg>
    </div>
    <input
      aria-invalid="false"
      aria-labelledby="checkbox:check-id:label"
      id="checkbox:check-id:input"
      name="check1"
      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
      type="checkbox"
      value="check1"
    />
  </label>
</div>
`;

import React from 'react';
import Checkbox from '../Checkbox';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Checkbox', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Checkbox key={1} id="check-id-1" label="check" value="check" name="check" defaultChecked={false} />, task);
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(<Checkbox key={1} id="check-id-2" label="check" value="check" name="check" defaultChecked={false} />, task, {
      interactionSelector: 'label[data-part="root"]:nth-child(1)',
      interactionType: 'focus',
    });
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(<Checkbox key={1} id="check-id-4" label="" value="check" name="check" defaultChecked={false} />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<Checkbox key={1} id="check-id-5" label="check" value="check" name="check" defaultChecked={false} disabled />, task);
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(<Checkbox key={1} id="check-id-6" label="check" value="check" name="check" defaultChecked={false} hasError />, task);
  });
});

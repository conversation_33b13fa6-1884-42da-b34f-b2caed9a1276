import styled from 'styled-components';
import { getColorVar } from '../colors';
import media from '../Breakpoints/Breakpoints';
import { getSpacingVar } from '../spacing';
import Heading from '../Heading';

export const FooterBase = styled.footer`
  background-color: ${getColorVar('ocean200')};
  color: ${getColorVar('white')};
  padding: 0 ${getSpacingVar(4)};

  ${media.smallUp} {
    padding: 0 ${getSpacingVar(6)};
  }

  ${media.mediumUp} {
    padding: 0 ${getSpacingVar(8)};
  }
`;

export const FooterHeader = styled.div`
  display: flex;
  gap: ${getSpacingVar(8)};
  margin: 0 auto;
  max-width: 1440px;
  padding-top: ${getSpacingVar(4)};

  ${media.extraSmallOnly} {
    flex-direction: column-reverse;
  }
`;

export const FooterLogo = styled.div`
  flex-grow: 1;
  margin-bottom: ${getSpacingVar(4)};

  svg {
    margin-right: ${getSpacingVar(4)};
  }
`;

export const Socials = styled.nav`
  display: flex;
  gap: ${getSpacingVar(2)};
  justify-content: center;
  ${media.extraSmallOnly} {
    border-bottom: 1px solid white;
    padding-bottom: ${getSpacingVar(4)};
  }
`;

export const FooterInner = styled.nav`
  display: flex;
  margin: ${getSpacingVar(6)} auto;
  max-width: 1440px;
  gap: ${getSpacingVar(8)};
  flex-direction: row;

  ${media.extraSmallOnly} {
    flex-direction: column;
    margin: ${getSpacingVar(4)} auto;
  }

  ${media.smallOnly} {
    flex-wrap: wrap;

    > div {
      flex: 1 1 calc(50% - ${getSpacingVar(4)});
    }
  }

  ${media.mediumUp} {
    > div {
      flex: 1 1 calc(25% - ${getSpacingVar(4)});
    }
  }
`;

export const LinksList = styled.ul`
  display: flex;
  flex-direction: column;
  gap: ${getSpacingVar(4)};
  list-style-type: none;
  margin: 0;
  line-height: 1;
`;

export const LinkHeading = styled(Heading)`
  margin-bottom: ${getSpacingVar(4)};
`;

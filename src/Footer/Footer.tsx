import React, { <PERSON> } from 'react';
import { FacebookIcon, InstagramIcon, LinkedInIcon, TwitterIcon, YoutubeIcon } from '../Icons';
import Logo from '../Logo';
import FooterCopyrightBar from '../FooterCopyrightBar/FooterCopyrightBar';
import { FooterBase, FooterHeader, FooterInner, FooterLogo, LinkHeading, LinksList, Socials } from './Footer.styles';
import { FixedLengthTuple, LinkEntry, TextLink } from './types';
import IconButton from '../IconButton';
import { LinkBase } from '../Link/LinkBase';
import Link from '../Link';

const socials = [
  { href: 'https://www.facebook.com/axajapan/', label: 'Facebook', icon: <FacebookIcon /> },
  { href: 'https://twitter.com/AXA_JP', label: 'Twitter', icon: <TwitterIcon /> },
  { href: 'https://www.linkedin.com/company/axa/', label: 'LinkedIn', icon: <LinkedInIcon /> },
  { href: 'https://www.instagram.com/axa/', label: 'Instagram', icon: <InstagramIcon /> },
  { href: 'https://www.youtube.com/c/AXAJP', label: 'Youtube', icon: <YoutubeIcon /> },
] as const;

const LinkList: FC<{ links: LinkEntry[] }> = ({ links }) => {
  return (
    <LinksList>
      {links.map((link, i) => (
        <li key={i}>
          {'url' in link ? (
            <Link href={link.url} icon={link.icon} color="white" underline={false}>
              {link.text}
            </Link>
          ) : (
            <Link asChild icon={link.icon} color="white" underline={false}>
              {link.children}
            </Link>
          )}
        </li>
      ))}
    </LinksList>
  );
};

const LinkSet: FC<{
  linkSet:
    | {
        heading: string;
        links: LinkEntry[];
      }
    | LinkEntry[];
}> = ({ linkSet }) => {
  if (Array.isArray(linkSet)) {
    return (
      <div>
        <LinkList links={linkSet} />
      </div>
    );
  }
  return (
    <div>
      <LinkHeading size="md" color="primary-white">
        {linkSet.heading}
      </LinkHeading>
      <LinkList links={linkSet.links} />
    </div>
  );
};

const brands = ['axa-life'] as const;
type Brands = (typeof brands)[number];
type DecoratedLinkEntry = LinkEntry & { icon?: React.ReactNode };

export type FooterProps = {
  /** Sets of links to be displayed in up to four columns */
  links?:
    | FixedLengthTuple<{ heading: string; links: LinkEntry[] }, { heading: string; links: DecoratedLinkEntry[] }>
    | FixedLengthTuple<LinkEntry[], DecoratedLinkEntry[]>;
  /**
   * Links displayed next to the copyright notice at the bottom of the footer.
   * Supports up to four links.
   */
  utilityLinks?: FixedLengthTuple<TextLink>;
  /** Branding for the footer, if it is undefined it will default to the global brand */
  brand: Brands;
} & React.ComponentPropsWithoutRef<'div'>;

const Footer: FC<FooterProps> = ({ links, utilityLinks, brand, ...rest }) => {
  const filteredBrand = brands.includes(brand) ? brand : 'axa-life';
  return (
    <FooterBase {...rest}>
      {(links !== undefined || utilityLinks !== undefined) && (
        <FooterHeader>
          <FooterLogo>
            <Logo brand={filteredBrand} variant="open-white" />
          </FooterLogo>
          <Socials>
            {socials.map(({ href, label, icon }) => (
              <IconButton
                icon={
                  <LinkBase target="_blank" color="white" href={href} tabIndex={-1}>
                    {icon}
                  </LinkBase>
                }
                color="white"
                aria-label={label}
                key={href}
              />
            ))}
          </Socials>
        </FooterHeader>
      )}
      {links !== undefined && (
        <FooterInner>
          {links.map((linkSet, idx) => (
            <LinkSet key={idx} linkSet={linkSet} />
          ))}
        </FooterInner>
      )}
      <FooterCopyrightBar utilityLinks={utilityLinks}>Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.</FooterCopyrightBar>
    </FooterBase>
  );
};

export default Footer;

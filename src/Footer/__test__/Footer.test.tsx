import React from 'react';
import { ExternalIcon } from '../../Icons';
import { render } from '../../utils/testUtils';
import Footer from '../Footer';

describe('Footer', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Footer brand="axa-life" />);
    const testElement = getByText('Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders copyright text links provided', () => {
    const { container } = render(
      <Footer
        brand="axa-life"
        utilityLinks={[
          { text: 'サイトマップ', url: '#/Navigation/Footer' },
          { text: 'サイトポリシー', url: '#/Navigation/Footer' },
          { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
          { text: 'Cookie設定', url: '#/Navigation/Footer' },
        ]}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  test('renders four column main link section', () => {
    const { container } = render(
      <Footer
        brand="axa-life"
        links={[
          {
            heading: 'アクサ生命について',
            links: [
              { text: '勧誘方針', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'バイク保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'ペット保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            ],
          },
          {
            heading: 'ご契約者様・ご契約される方',
            links: [
              { text: '取引時確認について', url: '#/Navigation/Footer' },
              { text: '生命保険相談所(裁定審査会)について', url: '#/Navigation/Footer' },
              { text: '生命保険契約者保護機構について', url: '#/Navigation/Footer' },
            ],
          },
          {
            heading: 'クイックリンク',
            links: [
              { text: '日本版スチュワードシップ・コードの受け入れ', url: '#/Navigation/Footer' },
              { text: 'AXAグループデータプライバシー宣言', url: '#/Navigation/Footer' },
            ],
          },
          {
            heading: 'アクサのグループ会社',
            links: [
              { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            ],
          },
        ]}
        utilityLinks={[
          { text: 'サイトマップ', url: '#/Navigation/Footer' },
          { text: 'サイトポリシー', url: '#/Navigation/Footer' },
          { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
          { text: 'Cookie設定', url: '#/Navigation/Footer' },
        ]}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  test('renders two column main link section with no headings', () => {
    const { container } = render(
      <Footer
        brand="axa-life"
        links={[
          [
            { text: '勧誘方針', url: '#/Navigation/Footer' },
            { text: 'バイク保険', url: '#/Navigation/Footer' },
            { text: 'ペット保険', url: '#/Navigation/Footer' },
          ],
          [
            { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
          ],
        ]}
      />,
    );
    expect(container).toMatchSnapshot();
  });
});

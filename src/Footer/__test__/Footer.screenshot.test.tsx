import React from 'react';
import Footer from '../Footer';
import { ExternalIcon } from '../../Icons';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Footer', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Footer brand="axa-life" />, task);
  });

  test('WithUtilityLinks', async ({ task }) => {
    await takeScreenshot(
      <Footer
        brand="axa-life"
        utilityLinks={[
          { text: 'サイトマップ', url: '#/Navigation/Footer' },
          { text: 'サイトポリシー', url: '#/Navigation/Footer' },
          { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
          { text: 'Cookie設定', url: '#/Navigation/Footer' },
        ]}
      />,
      task,
    );
  });

  test('WithMainLinks', async ({ task }) => {
    await takeScreenshot(
      <Footer
        brand="axa-life"
        links={[
          {
            heading: 'アクサ生命について',
            links: [
              { text: '勧誘方針', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'バイク保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'ペット保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            ],
          },
          {
            heading: 'ご契約者様・ご契約される方',
            links: [
              { text: '取引時確認について', url: '#/Navigation/Footer' },
              { text: '生命保険相談所(裁定審査会)について', url: '#/Navigation/Footer' },
              { text: '生命保険契約者保護機構について', url: '#/Navigation/Footer' },
            ],
          },
          {
            heading: 'クイックリンク',
            links: [
              { text: '日本版スチュワードシップ・コードの受け入れ', url: '#/Navigation/Footer' },
              { text: 'AXAグループデータプライバシー宣言', url: '#/Navigation/Footer' },
            ],
          },
          {
            heading: 'アクサのグループ会社',
            links: [
              { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
              { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            ],
          },
        ]}
        utilityLinks={[
          { text: 'サイトマップ', url: '#/Navigation/Footer' },
          { text: 'サイトポリシー', url: '#/Navigation/Footer' },
          { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
          { text: 'Cookie設定', url: '#/Navigation/Footer' },
        ]}
      />,
      task,
    );
  });

  test('WithMainLinksNoHeadings', async ({ task }) => {
    await takeScreenshot(
      <Footer
        brand="axa-life"
        links={[
          [
            { text: '勧誘方針', url: '#/Navigation/Footer' },
            { text: 'バイク保険', url: '#/Navigation/Footer' },
            { text: 'ペット保険', url: '#/Navigation/Footer' },
          ],
          [
            { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
            { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
          ],
        ]}
      />,
      task,
    );
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Footer > renders copyright text links provided 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: var(--ajds-spacing-6) 0;
}

.c11 {
  -webkit-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
  font-size: var(--ajds-font-size-sm);
  line-height: var(--ajds-line-height);
  margin: 0;
  margin-left: auto;
  padding-block: var(--ajds-spacing-px);
  text-align: right;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c10 {
  list-style-type: none;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-4);
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  line-height: 1;
}

.c0 {
  background-color: var(--ajds-color-ocean-200);
  color: var(--ajds-color-white);
  padding: 0 var(--ajds-spacing-4);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-8);
  margin: 0 auto;
  max-width: 1440px;
  padding-top: var(--ajds-spacing-4);
}

.c2 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-4);
}

.c2 svg {
  margin-right: var(--ajds-spacing-4);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (max-width:1279px) {
  .c9 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c11 {
    width: 100%;
    text-align: center;
  }
}

@media (min-width:600px) {
  .c11 {
    white-space: nowrap;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

@media (max-width:599px) {
  .c10 {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media (min-width:600px) {
  .c0 {
    padding: 0 var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c0 {
    padding: 0 var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
}

@media (max-width:599px) {
  .c3 {
    border-bottom: 1px solid white;
    padding-bottom: var(--ajds-spacing-4);
  }
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <footer
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          fill="none"
          height="48"
          viewBox="0 0 149 48"
          width="149"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            clip-path="url(#clip0_20792_2397)"
          >
            <path
              d="M0 0.000732422V48.0007H47.9981V0.000732422H0ZM46.8349 46.8375H1.16272V1.1644H46.8349V46.8375Z"
              fill="white"
            />
            <path
              d="M29.4679 23.7255L46.8349 1.16437H44.27L26.8438 23.7255H29.4679Z"
              fill="#FF1721"
            />
            <path
              d="M36.0656 33.787C36.861 36.0156 38.4992 41.8123 39.1507 42.2718H34.8441C34.8441 42.2718 34.8314 41.4139 34.64 40.7488C34.4537 40.0893 32.901 35.0777 32.901 35.0777H26.0602L24.9816 36.6007C24.9816 36.6007 26.2746 40.6476 26.3485 40.8447C26.4943 41.2003 27.1086 42.2713 27.1086 42.2713H22.9821C22.9821 42.2713 22.8801 41.6533 22.8377 41.3965C22.8006 41.1886 22.4459 40.0508 22.4459 40.0508C22.4459 40.0508 21.5076 41.0766 21.255 41.5568C20.9996 42.0234 20.8829 42.2718 20.8829 42.2718H17.6539C17.6539 42.2718 17.5457 41.6537 17.5072 41.3969C17.4752 41.189 17.0796 39.9609 17.0796 39.9609C17.0796 39.9609 16.177 41.062 15.9188 41.5333C15.6662 42.0074 15.5496 42.2718 15.5496 42.2718H12.3591C12.3591 42.2718 13.2646 41.4115 13.5778 41.0446C14.1088 40.4191 16.0938 37.8293 16.0938 37.8293L15.2974 35.0777H8.50406C8.50406 35.0777 4.63443 40.166 4.48203 40.3259C4.33011 40.4764 3.20078 42.0926 3.17068 42.2718H1.16272V40.9891C1.19988 40.9534 1.22669 40.9256 1.2408 40.9139C1.30336 40.8687 4.1763 37.3006 6.81266 33.787C9.18702 30.7222 11.4142 27.7368 11.6056 27.4536C12.0783 26.7679 12.757 25.2829 12.757 25.2829H16.2711C16.2711 25.2829 16.3807 26.647 16.4837 26.9781C16.5721 27.2716 18.7075 34.2903 18.7602 34.3665L19.9422 32.8552L17.9211 26.6286C17.9211 26.6286 17.4489 25.4621 17.2941 25.2829H21.39C21.39 25.2829 21.366 25.8878 21.5301 26.4C21.692 26.9123 22.5772 30.082 22.5772 30.082C22.5772 30.082 25.372 26.5774 25.531 26.33C25.8405 25.9076 25.8791 25.2825 25.8791 25.2825H29.292C29.292 25.2825 28.6711 25.742 27.5742 27.13C27.2069 27.5976 23.5974 32.1756 23.5974 32.1756C23.5974 32.1756 23.9106 33.2522 24.063 33.787C24.1082 33.9403 24.134 34.0424 24.134 34.0513C24.134 34.0612 24.2192 33.9639 24.3514 33.787C25.2827 32.6083 29.5178 27.0552 29.7732 26.5774C29.9792 26.1973 30.2859 25.7636 30.4613 25.2829H33.7971C33.7971 25.2829 33.8751 26.2825 33.9763 26.5534L36.0656 33.787ZM30.99 28.5326C30.5032 29.5858 27.6185 33.0899 27.6185 33.0899H32.1823C32.1823 33.0899 31.2985 30.3708 31.1452 29.756C31.0229 29.2683 31.0563 28.638 31.0563 28.5627C31.0563 28.5044 31.0431 28.4132 30.99 28.5326ZM13.4616 28.5326C12.972 29.5858 10.0948 33.0899 10.0948 33.0899H14.6578C14.6578 33.0899 13.7697 30.3708 13.6178 29.756C13.4974 29.2683 13.5294 28.638 13.5294 28.5627C13.5289 28.5044 13.5223 28.4132 13.4616 28.5326ZM20.3312 39.166L21.5856 37.4426C21.4695 37.3133 20.7625 35.1708 20.7625 35.1708L19.549 36.7456L20.3312 39.166Z"
              fill="white"
            />
            <path
              d="M140.089 32.8679L140.034 37.7606C140.034 38.6994 139.803 43.2285 139.803 43.2285L142.199 43.2304L142.186 38.5193C142.186 37.8561 142.281 35.541 142.309 34.8759H145.153V39.0193C145.153 39.4233 145.028 39.4934 144.652 39.4934L142.771 39.4788L143.044 41.6688C143.044 41.6688 144.192 41.5935 145.674 41.5935C146.138 41.5935 146.517 41.4519 146.796 41.1711C147.147 40.8165 147.324 40.2652 147.32 39.5286L147.358 32.8684L140.089 32.8679Z"
              fill="white"
            />
            <path
              d="M131.836 32.8679L131.863 37.1176C131.863 38.5616 131.639 41.7643 131.639 41.7643H138.819L138.847 36.9356C138.847 35.8279 139.016 32.8679 139.016 32.8679H131.836ZM134.051 34.9036H136.73L136.633 39.6768H133.98L134.051 34.9036Z"
              fill="white"
            />
            <path
              d="M144.069 27.9456C141.544 26.4391 141.375 25.4579 141.375 25.4579H138.404C138.404 25.4579 137.888 26.5421 135.258 28.0759C132.589 29.6323 130.759 29.994 130.759 29.994L130.763 32.2136C130.763 32.2136 132.695 31.668 134.495 30.9107L134.48 31.7983C134.48 31.7983 138.283 31.6548 139.722 31.7066C141.17 31.7579 144.833 31.7913 144.833 31.7913L144.894 30.8487C146.568 31.5523 148.668 32.223 148.668 32.223V29.9446C148.668 29.9446 146.696 29.5147 144.069 27.9456ZM142.589 29.6949L139.729 29.7259L136.927 29.6968C138.571 28.7156 139.506 27.8849 139.85 27.5543C140.162 27.9037 140.987 28.7175 142.589 29.6949Z"
              fill="white"
            />
            <path
              d="M124.567 40.5883C123.571 40.5883 122.581 40.6123 122.102 40.6231V36.7319C122.66 36.721 124.816 36.6529 125.435 36.6952C126.189 36.7483 127.992 36.8165 127.992 36.8165V34.4869C127.992 34.4869 126.763 34.5551 125.44 34.6082L122.102 34.6576V31.4055C122.677 31.4037 124.141 31.3999 124.742 31.4182L128.426 31.5579V29.2485C128.426 29.2485 125.55 29.32 124.802 29.32L122.147 29.3308C122.178 28.7485 122.215 27.0928 122.215 26.6958C122.215 26.2476 122.32 25.4612 122.32 25.4612H119.847C119.847 25.4612 119.871 26.1926 119.897 26.7067C119.919 27.129 119.904 28.772 119.9 29.3322H116.356C116.472 29.073 116.644 28.6836 116.757 28.3877C116.944 27.9155 117.299 26.6719 117.316 26.6182L117.385 26.3722H114.861L114.765 26.9089C114.765 26.9089 114.668 27.5905 113.733 29.8176C112.827 31.9653 111.859 32.3161 111.859 32.3161L111.857 35.0334C111.857 35.0334 113.279 34.5151 114.323 33.1778C114.705 32.6854 115.28 31.7117 115.46 31.4032L119.817 31.4051V34.6407C119.253 34.6223 117.096 34.5692 116.135 34.5692C114.973 34.5692 114.158 34.5085 114.158 34.5085L114.148 36.7921C114.148 36.7921 114.81 36.761 115.454 36.761C116.011 36.761 119.083 36.706 119.816 36.6858V40.5973C119.245 40.6029 117.207 40.6081 115.98 40.5879C114.508 40.5601 111.919 40.4778 111.919 40.4778V42.9472C111.919 42.9472 119.939 42.8296 120.579 42.8555C121.227 42.8813 128.766 42.9401 128.766 42.9401V40.4486C128.766 40.4486 125.802 40.5883 124.567 40.5883Z"
              fill="white"
            />
            <path
              d="M106.601 28.725L106.787 25.4692H104.239L104.267 28.7542L98.4017 28.7579L98.5616 25.473H96.072L96.0207 28.7528L91.9328 28.7048L91.9272 31.0843L96.0372 30.9794C96.0094 31.764 95.914 34.5052 95.914 34.8472C95.914 35.2277 95.7837 36.5447 95.7837 36.556L95.7616 36.7691H98.2822L98.2878 36.5833C98.2878 36.5739 98.3137 35.7823 98.2878 35.1026C98.2657 34.5222 98.3815 31.6539 98.413 30.8971H104.189V33.4088C104.189 35.5419 103.497 37.3222 102.183 38.5221C100.421 40.1335 97.1505 40.6665 97.1505 40.6665V43.0018C97.1505 43.0018 101.704 42.2266 104.254 39.7977C106.277 37.8721 106.486 34.8331 106.486 33.6186C106.486 33.2108 106.56 31.5387 106.582 30.9785C106.582 30.9785 109.123 30.9879 109.654 30.9879C109.845 30.9879 110.723 31.0373 110.723 31.0373V28.6342C110.723 28.6342 109.076 28.6672 109.65 28.6672C109.907 28.6662 106.601 28.725 106.601 28.725Z"
              fill="white"
            />
            <path
              d="M81.8653 27.6794L82.782 25.8074H80.0093L79.9246 26.2133C79.9227 26.2241 79.7336 27.5416 77.6885 30.0406C75.6509 32.5321 74.1886 32.9178 74.1886 32.9178L74.1792 35.319C74.1792 35.319 76.3235 34.6684 78.2854 32.8167C79.1048 32.0429 80.3761 30.3233 80.6593 29.9376H87.0369C86.8972 30.6798 86.2156 33.3402 83.3253 36.4215C81.3154 38.5691 78.0629 39.9397 77.2088 40.1787C76.3565 40.4195 75.7332 40.4708 75.7332 40.4708V42.9971C75.7332 42.9971 76.0954 42.8555 76.5728 42.7473C77.3922 42.5582 81.7082 41.3104 85.2782 37.8452C88.1573 35.0433 89.2857 30.9517 89.6384 29.32L89.6511 29.2612C89.7523 28.8073 90.0608 27.9532 90.0627 27.9456L90.1563 27.6883L81.8653 27.6794Z"
              fill="white"
            />
            <path
              d="M74.507 26.6136H65.383L64.1996 26.6173C61.9047 26.6192 57.7228 26.496 57.7228 26.496L57.7284 28.9729C57.7284 28.9729 59.5327 28.8534 60.33 28.8534L65.2264 28.8332H71.4549C71.3171 29.3717 70.8613 30.7936 69.6214 32.0137C68.3848 33.2282 66.946 33.5518 66.406 33.6341C66.43 33.3326 66.4648 32.7964 66.4648 32.359C66.4648 31.7452 66.5843 30.0458 66.5843 30.0458H64.1389L64.091 33.3806C64.091 33.9831 63.6921 36.6068 61.741 38.4534C59.8761 40.2154 57.989 40.522 57.989 40.522V42.982C57.989 42.982 61.4725 42.0135 63.5801 39.9595C65.3953 38.19 65.8807 36.5108 65.9888 36.0146C66.6887 35.9248 69.7423 35.4064 71.7324 33.1943C73.9115 30.7711 74.4534 26.9913 74.4774 26.8337L74.507 26.6136Z"
              fill="white"
            />
          </g>
          <defs>
            <clippath
              id="clip0_20792_2397"
            >
              <rect
                fill="white"
                height="48"
                width="149"
              />
            </clippath>
          </defs>
        </svg>
      </div>
      <nav
        class="c3"
      >
        <button
          aria-label="Facebook"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.facebook.com/axajapan/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Twitter"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://twitter.com/AXA_JP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="LinkedIn"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.linkedin.com/company/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Instagram"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.instagram.com/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Youtube"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.youtube.com/c/AXAJP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
      </nav>
    </div>
    <div
      class="c9"
    >
      <ul
        class="c10"
      >
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            サイトマップ
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            サイトポリシー
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            個人情報の取り扱い
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            Cookie設定
          </a>
        </li>
      </ul>
      <p
        class="c11"
      >
        Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.
      </p>
    </div>
  </footer>
</div>
`;

exports[`Footer > renders four column main link section 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: var(--ajds-spacing-6) 0;
}

.c16 {
  -webkit-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
  font-size: var(--ajds-font-size-sm);
  line-height: var(--ajds-line-height);
  margin: 0;
  margin-left: auto;
  padding-block: var(--ajds-spacing-px);
  text-align: right;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c13 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c15 {
  list-style-type: none;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-4);
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  line-height: 1;
}

.c10 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

.c0 {
  background-color: var(--ajds-color-ocean-200);
  color: var(--ajds-color-white);
  padding: 0 var(--ajds-spacing-4);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-8);
  margin: 0 auto;
  max-width: 1440px;
  padding-top: var(--ajds-spacing-4);
}

.c2 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-4);
}

.c2 svg {
  margin-right: var(--ajds-spacing-4);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: var(--ajds-spacing-6) auto;
  max-width: 1440px;
  gap: var(--ajds-spacing-8);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-4);
  list-style-type: none;
  margin: 0;
  line-height: 1;
}

.c11 {
  margin-bottom: var(--ajds-spacing-4);
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (max-width:1279px) {
  .c14 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c16 {
    width: 100%;
    text-align: center;
  }
}

@media (min-width:600px) {
  .c16 {
    white-space: nowrap;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

@media (max-width:599px) {
  .c15 {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media (min-width:600px) {
  .c0 {
    padding: 0 var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c0 {
    padding: 0 var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
}

@media (max-width:599px) {
  .c3 {
    border-bottom: 1px solid white;
    padding-bottom: var(--ajds-spacing-4);
  }
}

@media (max-width:599px) {
  .c9 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: var(--ajds-spacing-4) auto;
  }
}

@media (max-width:899px) {
  .c9 {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .c9 > div {
    -webkit-flex: 1 1 calc(50% - var(--ajds-spacing-4));
    -ms-flex: 1 1 calc(50% - var(--ajds-spacing-4));
    flex: 1 1 calc(50% - var(--ajds-spacing-4));
  }
}

@media (min-width:900px) {
  .c9 > div {
    -webkit-flex: 1 1 calc(25% - var(--ajds-spacing-4));
    -ms-flex: 1 1 calc(25% - var(--ajds-spacing-4));
    flex: 1 1 calc(25% - var(--ajds-spacing-4));
  }
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <footer
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          fill="none"
          height="48"
          viewBox="0 0 149 48"
          width="149"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            clip-path="url(#clip0_20792_2397)"
          >
            <path
              d="M0 0.000732422V48.0007H47.9981V0.000732422H0ZM46.8349 46.8375H1.16272V1.1644H46.8349V46.8375Z"
              fill="white"
            />
            <path
              d="M29.4679 23.7255L46.8349 1.16437H44.27L26.8438 23.7255H29.4679Z"
              fill="#FF1721"
            />
            <path
              d="M36.0656 33.787C36.861 36.0156 38.4992 41.8123 39.1507 42.2718H34.8441C34.8441 42.2718 34.8314 41.4139 34.64 40.7488C34.4537 40.0893 32.901 35.0777 32.901 35.0777H26.0602L24.9816 36.6007C24.9816 36.6007 26.2746 40.6476 26.3485 40.8447C26.4943 41.2003 27.1086 42.2713 27.1086 42.2713H22.9821C22.9821 42.2713 22.8801 41.6533 22.8377 41.3965C22.8006 41.1886 22.4459 40.0508 22.4459 40.0508C22.4459 40.0508 21.5076 41.0766 21.255 41.5568C20.9996 42.0234 20.8829 42.2718 20.8829 42.2718H17.6539C17.6539 42.2718 17.5457 41.6537 17.5072 41.3969C17.4752 41.189 17.0796 39.9609 17.0796 39.9609C17.0796 39.9609 16.177 41.062 15.9188 41.5333C15.6662 42.0074 15.5496 42.2718 15.5496 42.2718H12.3591C12.3591 42.2718 13.2646 41.4115 13.5778 41.0446C14.1088 40.4191 16.0938 37.8293 16.0938 37.8293L15.2974 35.0777H8.50406C8.50406 35.0777 4.63443 40.166 4.48203 40.3259C4.33011 40.4764 3.20078 42.0926 3.17068 42.2718H1.16272V40.9891C1.19988 40.9534 1.22669 40.9256 1.2408 40.9139C1.30336 40.8687 4.1763 37.3006 6.81266 33.787C9.18702 30.7222 11.4142 27.7368 11.6056 27.4536C12.0783 26.7679 12.757 25.2829 12.757 25.2829H16.2711C16.2711 25.2829 16.3807 26.647 16.4837 26.9781C16.5721 27.2716 18.7075 34.2903 18.7602 34.3665L19.9422 32.8552L17.9211 26.6286C17.9211 26.6286 17.4489 25.4621 17.2941 25.2829H21.39C21.39 25.2829 21.366 25.8878 21.5301 26.4C21.692 26.9123 22.5772 30.082 22.5772 30.082C22.5772 30.082 25.372 26.5774 25.531 26.33C25.8405 25.9076 25.8791 25.2825 25.8791 25.2825H29.292C29.292 25.2825 28.6711 25.742 27.5742 27.13C27.2069 27.5976 23.5974 32.1756 23.5974 32.1756C23.5974 32.1756 23.9106 33.2522 24.063 33.787C24.1082 33.9403 24.134 34.0424 24.134 34.0513C24.134 34.0612 24.2192 33.9639 24.3514 33.787C25.2827 32.6083 29.5178 27.0552 29.7732 26.5774C29.9792 26.1973 30.2859 25.7636 30.4613 25.2829H33.7971C33.7971 25.2829 33.8751 26.2825 33.9763 26.5534L36.0656 33.787ZM30.99 28.5326C30.5032 29.5858 27.6185 33.0899 27.6185 33.0899H32.1823C32.1823 33.0899 31.2985 30.3708 31.1452 29.756C31.0229 29.2683 31.0563 28.638 31.0563 28.5627C31.0563 28.5044 31.0431 28.4132 30.99 28.5326ZM13.4616 28.5326C12.972 29.5858 10.0948 33.0899 10.0948 33.0899H14.6578C14.6578 33.0899 13.7697 30.3708 13.6178 29.756C13.4974 29.2683 13.5294 28.638 13.5294 28.5627C13.5289 28.5044 13.5223 28.4132 13.4616 28.5326ZM20.3312 39.166L21.5856 37.4426C21.4695 37.3133 20.7625 35.1708 20.7625 35.1708L19.549 36.7456L20.3312 39.166Z"
              fill="white"
            />
            <path
              d="M140.089 32.8679L140.034 37.7606C140.034 38.6994 139.803 43.2285 139.803 43.2285L142.199 43.2304L142.186 38.5193C142.186 37.8561 142.281 35.541 142.309 34.8759H145.153V39.0193C145.153 39.4233 145.028 39.4934 144.652 39.4934L142.771 39.4788L143.044 41.6688C143.044 41.6688 144.192 41.5935 145.674 41.5935C146.138 41.5935 146.517 41.4519 146.796 41.1711C147.147 40.8165 147.324 40.2652 147.32 39.5286L147.358 32.8684L140.089 32.8679Z"
              fill="white"
            />
            <path
              d="M131.836 32.8679L131.863 37.1176C131.863 38.5616 131.639 41.7643 131.639 41.7643H138.819L138.847 36.9356C138.847 35.8279 139.016 32.8679 139.016 32.8679H131.836ZM134.051 34.9036H136.73L136.633 39.6768H133.98L134.051 34.9036Z"
              fill="white"
            />
            <path
              d="M144.069 27.9456C141.544 26.4391 141.375 25.4579 141.375 25.4579H138.404C138.404 25.4579 137.888 26.5421 135.258 28.0759C132.589 29.6323 130.759 29.994 130.759 29.994L130.763 32.2136C130.763 32.2136 132.695 31.668 134.495 30.9107L134.48 31.7983C134.48 31.7983 138.283 31.6548 139.722 31.7066C141.17 31.7579 144.833 31.7913 144.833 31.7913L144.894 30.8487C146.568 31.5523 148.668 32.223 148.668 32.223V29.9446C148.668 29.9446 146.696 29.5147 144.069 27.9456ZM142.589 29.6949L139.729 29.7259L136.927 29.6968C138.571 28.7156 139.506 27.8849 139.85 27.5543C140.162 27.9037 140.987 28.7175 142.589 29.6949Z"
              fill="white"
            />
            <path
              d="M124.567 40.5883C123.571 40.5883 122.581 40.6123 122.102 40.6231V36.7319C122.66 36.721 124.816 36.6529 125.435 36.6952C126.189 36.7483 127.992 36.8165 127.992 36.8165V34.4869C127.992 34.4869 126.763 34.5551 125.44 34.6082L122.102 34.6576V31.4055C122.677 31.4037 124.141 31.3999 124.742 31.4182L128.426 31.5579V29.2485C128.426 29.2485 125.55 29.32 124.802 29.32L122.147 29.3308C122.178 28.7485 122.215 27.0928 122.215 26.6958C122.215 26.2476 122.32 25.4612 122.32 25.4612H119.847C119.847 25.4612 119.871 26.1926 119.897 26.7067C119.919 27.129 119.904 28.772 119.9 29.3322H116.356C116.472 29.073 116.644 28.6836 116.757 28.3877C116.944 27.9155 117.299 26.6719 117.316 26.6182L117.385 26.3722H114.861L114.765 26.9089C114.765 26.9089 114.668 27.5905 113.733 29.8176C112.827 31.9653 111.859 32.3161 111.859 32.3161L111.857 35.0334C111.857 35.0334 113.279 34.5151 114.323 33.1778C114.705 32.6854 115.28 31.7117 115.46 31.4032L119.817 31.4051V34.6407C119.253 34.6223 117.096 34.5692 116.135 34.5692C114.973 34.5692 114.158 34.5085 114.158 34.5085L114.148 36.7921C114.148 36.7921 114.81 36.761 115.454 36.761C116.011 36.761 119.083 36.706 119.816 36.6858V40.5973C119.245 40.6029 117.207 40.6081 115.98 40.5879C114.508 40.5601 111.919 40.4778 111.919 40.4778V42.9472C111.919 42.9472 119.939 42.8296 120.579 42.8555C121.227 42.8813 128.766 42.9401 128.766 42.9401V40.4486C128.766 40.4486 125.802 40.5883 124.567 40.5883Z"
              fill="white"
            />
            <path
              d="M106.601 28.725L106.787 25.4692H104.239L104.267 28.7542L98.4017 28.7579L98.5616 25.473H96.072L96.0207 28.7528L91.9328 28.7048L91.9272 31.0843L96.0372 30.9794C96.0094 31.764 95.914 34.5052 95.914 34.8472C95.914 35.2277 95.7837 36.5447 95.7837 36.556L95.7616 36.7691H98.2822L98.2878 36.5833C98.2878 36.5739 98.3137 35.7823 98.2878 35.1026C98.2657 34.5222 98.3815 31.6539 98.413 30.8971H104.189V33.4088C104.189 35.5419 103.497 37.3222 102.183 38.5221C100.421 40.1335 97.1505 40.6665 97.1505 40.6665V43.0018C97.1505 43.0018 101.704 42.2266 104.254 39.7977C106.277 37.8721 106.486 34.8331 106.486 33.6186C106.486 33.2108 106.56 31.5387 106.582 30.9785C106.582 30.9785 109.123 30.9879 109.654 30.9879C109.845 30.9879 110.723 31.0373 110.723 31.0373V28.6342C110.723 28.6342 109.076 28.6672 109.65 28.6672C109.907 28.6662 106.601 28.725 106.601 28.725Z"
              fill="white"
            />
            <path
              d="M81.8653 27.6794L82.782 25.8074H80.0093L79.9246 26.2133C79.9227 26.2241 79.7336 27.5416 77.6885 30.0406C75.6509 32.5321 74.1886 32.9178 74.1886 32.9178L74.1792 35.319C74.1792 35.319 76.3235 34.6684 78.2854 32.8167C79.1048 32.0429 80.3761 30.3233 80.6593 29.9376H87.0369C86.8972 30.6798 86.2156 33.3402 83.3253 36.4215C81.3154 38.5691 78.0629 39.9397 77.2088 40.1787C76.3565 40.4195 75.7332 40.4708 75.7332 40.4708V42.9971C75.7332 42.9971 76.0954 42.8555 76.5728 42.7473C77.3922 42.5582 81.7082 41.3104 85.2782 37.8452C88.1573 35.0433 89.2857 30.9517 89.6384 29.32L89.6511 29.2612C89.7523 28.8073 90.0608 27.9532 90.0627 27.9456L90.1563 27.6883L81.8653 27.6794Z"
              fill="white"
            />
            <path
              d="M74.507 26.6136H65.383L64.1996 26.6173C61.9047 26.6192 57.7228 26.496 57.7228 26.496L57.7284 28.9729C57.7284 28.9729 59.5327 28.8534 60.33 28.8534L65.2264 28.8332H71.4549C71.3171 29.3717 70.8613 30.7936 69.6214 32.0137C68.3848 33.2282 66.946 33.5518 66.406 33.6341C66.43 33.3326 66.4648 32.7964 66.4648 32.359C66.4648 31.7452 66.5843 30.0458 66.5843 30.0458H64.1389L64.091 33.3806C64.091 33.9831 63.6921 36.6068 61.741 38.4534C59.8761 40.2154 57.989 40.522 57.989 40.522V42.982C57.989 42.982 61.4725 42.0135 63.5801 39.9595C65.3953 38.19 65.8807 36.5108 65.9888 36.0146C66.6887 35.9248 69.7423 35.4064 71.7324 33.1943C73.9115 30.7711 74.4534 26.9913 74.4774 26.8337L74.507 26.6136Z"
              fill="white"
            />
          </g>
          <defs>
            <clippath
              id="clip0_20792_2397"
            >
              <rect
                fill="white"
                height="48"
                width="149"
              />
            </clippath>
          </defs>
        </svg>
      </div>
      <nav
        class="c3"
      >
        <button
          aria-label="Facebook"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.facebook.com/axajapan/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Twitter"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://twitter.com/AXA_JP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="LinkedIn"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.linkedin.com/company/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Instagram"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.instagram.com/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Youtube"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.youtube.com/c/AXAJP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
      </nav>
    </div>
    <nav
      class="c9"
    >
      <div>
        <h2
          class="c10 c11"
          style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          アクサ生命について
        </h2>
        <ul
          class="c12"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              勧誘方針
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              バイク保険
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              ペット保険
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
        </ul>
      </div>
      <div>
        <h2
          class="c10 c11"
          style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          ご契約者様・ご契約される方
        </h2>
        <ul
          class="c12"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              取引時確認について
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              生命保険相談所(裁定審査会)について
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              生命保険契約者保護機構について
            </a>
          </li>
        </ul>
      </div>
      <div>
        <h2
          class="c10 c11"
          style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          クイックリンク
        </h2>
        <ul
          class="c12"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              日本版スチュワードシップ・コードの受け入れ
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              AXAグループデータプライバシー宣言
            </a>
          </li>
        </ul>
      </div>
      <div>
        <h2
          class="c10 c11"
          style="--ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          アクサのグループ会社
        </h2>
        <ul
          class="c12"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ生命
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサダイレクト(損害保険)
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ生命のネット完結保険
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ・ホールディングス・ジャパン
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              AXA.com
              <span
                class="c13"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
        </ul>
      </div>
    </nav>
    <div
      class="c14"
    >
      <ul
        class="c15"
      >
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            サイトマップ
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            サイトポリシー
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            個人情報の取り扱い
          </a>
        </li>
        <li>
          <a
            class="c7"
            href="#/Navigation/Footer"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            Cookie設定
          </a>
        </li>
      </ul>
      <p
        class="c16"
      >
        Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.
      </p>
    </div>
  </footer>
</div>
`;

exports[`Footer > renders two column main link section with no headings 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: var(--ajds-spacing-6) 0;
}

.c13 {
  -webkit-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
  font-size: var(--ajds-font-size-sm);
  line-height: var(--ajds-line-height);
  margin: 0;
  margin-left: auto;
  padding-block: var(--ajds-spacing-px);
  text-align: right;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c11 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c0 {
  background-color: var(--ajds-color-ocean-200);
  color: var(--ajds-color-white);
  padding: 0 var(--ajds-spacing-4);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-8);
  margin: 0 auto;
  max-width: 1440px;
  padding-top: var(--ajds-spacing-4);
}

.c2 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-4);
}

.c2 svg {
  margin-right: var(--ajds-spacing-4);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-2);
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: var(--ajds-spacing-6) auto;
  max-width: 1440px;
  gap: var(--ajds-spacing-8);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-4);
  list-style-type: none;
  margin: 0;
  line-height: 1;
}

.c4 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c4:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c5 svg {
  color: currentColor;
}

.c5[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

@media (max-width:1279px) {
  .c12 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c13 {
    width: 100%;
    text-align: center;
  }
}

@media (min-width:600px) {
  .c13 {
    white-space: nowrap;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

@media (min-width:600px) {
  .c0 {
    padding: 0 var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c0 {
    padding: 0 var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c1 {
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
}

@media (max-width:599px) {
  .c3 {
    border-bottom: 1px solid white;
    padding-bottom: var(--ajds-spacing-4);
  }
}

@media (max-width:599px) {
  .c9 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: var(--ajds-spacing-4) auto;
  }
}

@media (max-width:899px) {
  .c9 {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .c9 > div {
    -webkit-flex: 1 1 calc(50% - var(--ajds-spacing-4));
    -ms-flex: 1 1 calc(50% - var(--ajds-spacing-4));
    flex: 1 1 calc(50% - var(--ajds-spacing-4));
  }
}

@media (min-width:900px) {
  .c9 > div {
    -webkit-flex: 1 1 calc(25% - var(--ajds-spacing-4));
    -ms-flex: 1 1 calc(25% - var(--ajds-spacing-4));
    flex: 1 1 calc(25% - var(--ajds-spacing-4));
  }
}

@media (hover:hover) {
  .c5:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c5:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <footer
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          fill="none"
          height="48"
          viewBox="0 0 149 48"
          width="149"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            clip-path="url(#clip0_20792_2397)"
          >
            <path
              d="M0 0.000732422V48.0007H47.9981V0.000732422H0ZM46.8349 46.8375H1.16272V1.1644H46.8349V46.8375Z"
              fill="white"
            />
            <path
              d="M29.4679 23.7255L46.8349 1.16437H44.27L26.8438 23.7255H29.4679Z"
              fill="#FF1721"
            />
            <path
              d="M36.0656 33.787C36.861 36.0156 38.4992 41.8123 39.1507 42.2718H34.8441C34.8441 42.2718 34.8314 41.4139 34.64 40.7488C34.4537 40.0893 32.901 35.0777 32.901 35.0777H26.0602L24.9816 36.6007C24.9816 36.6007 26.2746 40.6476 26.3485 40.8447C26.4943 41.2003 27.1086 42.2713 27.1086 42.2713H22.9821C22.9821 42.2713 22.8801 41.6533 22.8377 41.3965C22.8006 41.1886 22.4459 40.0508 22.4459 40.0508C22.4459 40.0508 21.5076 41.0766 21.255 41.5568C20.9996 42.0234 20.8829 42.2718 20.8829 42.2718H17.6539C17.6539 42.2718 17.5457 41.6537 17.5072 41.3969C17.4752 41.189 17.0796 39.9609 17.0796 39.9609C17.0796 39.9609 16.177 41.062 15.9188 41.5333C15.6662 42.0074 15.5496 42.2718 15.5496 42.2718H12.3591C12.3591 42.2718 13.2646 41.4115 13.5778 41.0446C14.1088 40.4191 16.0938 37.8293 16.0938 37.8293L15.2974 35.0777H8.50406C8.50406 35.0777 4.63443 40.166 4.48203 40.3259C4.33011 40.4764 3.20078 42.0926 3.17068 42.2718H1.16272V40.9891C1.19988 40.9534 1.22669 40.9256 1.2408 40.9139C1.30336 40.8687 4.1763 37.3006 6.81266 33.787C9.18702 30.7222 11.4142 27.7368 11.6056 27.4536C12.0783 26.7679 12.757 25.2829 12.757 25.2829H16.2711C16.2711 25.2829 16.3807 26.647 16.4837 26.9781C16.5721 27.2716 18.7075 34.2903 18.7602 34.3665L19.9422 32.8552L17.9211 26.6286C17.9211 26.6286 17.4489 25.4621 17.2941 25.2829H21.39C21.39 25.2829 21.366 25.8878 21.5301 26.4C21.692 26.9123 22.5772 30.082 22.5772 30.082C22.5772 30.082 25.372 26.5774 25.531 26.33C25.8405 25.9076 25.8791 25.2825 25.8791 25.2825H29.292C29.292 25.2825 28.6711 25.742 27.5742 27.13C27.2069 27.5976 23.5974 32.1756 23.5974 32.1756C23.5974 32.1756 23.9106 33.2522 24.063 33.787C24.1082 33.9403 24.134 34.0424 24.134 34.0513C24.134 34.0612 24.2192 33.9639 24.3514 33.787C25.2827 32.6083 29.5178 27.0552 29.7732 26.5774C29.9792 26.1973 30.2859 25.7636 30.4613 25.2829H33.7971C33.7971 25.2829 33.8751 26.2825 33.9763 26.5534L36.0656 33.787ZM30.99 28.5326C30.5032 29.5858 27.6185 33.0899 27.6185 33.0899H32.1823C32.1823 33.0899 31.2985 30.3708 31.1452 29.756C31.0229 29.2683 31.0563 28.638 31.0563 28.5627C31.0563 28.5044 31.0431 28.4132 30.99 28.5326ZM13.4616 28.5326C12.972 29.5858 10.0948 33.0899 10.0948 33.0899H14.6578C14.6578 33.0899 13.7697 30.3708 13.6178 29.756C13.4974 29.2683 13.5294 28.638 13.5294 28.5627C13.5289 28.5044 13.5223 28.4132 13.4616 28.5326ZM20.3312 39.166L21.5856 37.4426C21.4695 37.3133 20.7625 35.1708 20.7625 35.1708L19.549 36.7456L20.3312 39.166Z"
              fill="white"
            />
            <path
              d="M140.089 32.8679L140.034 37.7606C140.034 38.6994 139.803 43.2285 139.803 43.2285L142.199 43.2304L142.186 38.5193C142.186 37.8561 142.281 35.541 142.309 34.8759H145.153V39.0193C145.153 39.4233 145.028 39.4934 144.652 39.4934L142.771 39.4788L143.044 41.6688C143.044 41.6688 144.192 41.5935 145.674 41.5935C146.138 41.5935 146.517 41.4519 146.796 41.1711C147.147 40.8165 147.324 40.2652 147.32 39.5286L147.358 32.8684L140.089 32.8679Z"
              fill="white"
            />
            <path
              d="M131.836 32.8679L131.863 37.1176C131.863 38.5616 131.639 41.7643 131.639 41.7643H138.819L138.847 36.9356C138.847 35.8279 139.016 32.8679 139.016 32.8679H131.836ZM134.051 34.9036H136.73L136.633 39.6768H133.98L134.051 34.9036Z"
              fill="white"
            />
            <path
              d="M144.069 27.9456C141.544 26.4391 141.375 25.4579 141.375 25.4579H138.404C138.404 25.4579 137.888 26.5421 135.258 28.0759C132.589 29.6323 130.759 29.994 130.759 29.994L130.763 32.2136C130.763 32.2136 132.695 31.668 134.495 30.9107L134.48 31.7983C134.48 31.7983 138.283 31.6548 139.722 31.7066C141.17 31.7579 144.833 31.7913 144.833 31.7913L144.894 30.8487C146.568 31.5523 148.668 32.223 148.668 32.223V29.9446C148.668 29.9446 146.696 29.5147 144.069 27.9456ZM142.589 29.6949L139.729 29.7259L136.927 29.6968C138.571 28.7156 139.506 27.8849 139.85 27.5543C140.162 27.9037 140.987 28.7175 142.589 29.6949Z"
              fill="white"
            />
            <path
              d="M124.567 40.5883C123.571 40.5883 122.581 40.6123 122.102 40.6231V36.7319C122.66 36.721 124.816 36.6529 125.435 36.6952C126.189 36.7483 127.992 36.8165 127.992 36.8165V34.4869C127.992 34.4869 126.763 34.5551 125.44 34.6082L122.102 34.6576V31.4055C122.677 31.4037 124.141 31.3999 124.742 31.4182L128.426 31.5579V29.2485C128.426 29.2485 125.55 29.32 124.802 29.32L122.147 29.3308C122.178 28.7485 122.215 27.0928 122.215 26.6958C122.215 26.2476 122.32 25.4612 122.32 25.4612H119.847C119.847 25.4612 119.871 26.1926 119.897 26.7067C119.919 27.129 119.904 28.772 119.9 29.3322H116.356C116.472 29.073 116.644 28.6836 116.757 28.3877C116.944 27.9155 117.299 26.6719 117.316 26.6182L117.385 26.3722H114.861L114.765 26.9089C114.765 26.9089 114.668 27.5905 113.733 29.8176C112.827 31.9653 111.859 32.3161 111.859 32.3161L111.857 35.0334C111.857 35.0334 113.279 34.5151 114.323 33.1778C114.705 32.6854 115.28 31.7117 115.46 31.4032L119.817 31.4051V34.6407C119.253 34.6223 117.096 34.5692 116.135 34.5692C114.973 34.5692 114.158 34.5085 114.158 34.5085L114.148 36.7921C114.148 36.7921 114.81 36.761 115.454 36.761C116.011 36.761 119.083 36.706 119.816 36.6858V40.5973C119.245 40.6029 117.207 40.6081 115.98 40.5879C114.508 40.5601 111.919 40.4778 111.919 40.4778V42.9472C111.919 42.9472 119.939 42.8296 120.579 42.8555C121.227 42.8813 128.766 42.9401 128.766 42.9401V40.4486C128.766 40.4486 125.802 40.5883 124.567 40.5883Z"
              fill="white"
            />
            <path
              d="M106.601 28.725L106.787 25.4692H104.239L104.267 28.7542L98.4017 28.7579L98.5616 25.473H96.072L96.0207 28.7528L91.9328 28.7048L91.9272 31.0843L96.0372 30.9794C96.0094 31.764 95.914 34.5052 95.914 34.8472C95.914 35.2277 95.7837 36.5447 95.7837 36.556L95.7616 36.7691H98.2822L98.2878 36.5833C98.2878 36.5739 98.3137 35.7823 98.2878 35.1026C98.2657 34.5222 98.3815 31.6539 98.413 30.8971H104.189V33.4088C104.189 35.5419 103.497 37.3222 102.183 38.5221C100.421 40.1335 97.1505 40.6665 97.1505 40.6665V43.0018C97.1505 43.0018 101.704 42.2266 104.254 39.7977C106.277 37.8721 106.486 34.8331 106.486 33.6186C106.486 33.2108 106.56 31.5387 106.582 30.9785C106.582 30.9785 109.123 30.9879 109.654 30.9879C109.845 30.9879 110.723 31.0373 110.723 31.0373V28.6342C110.723 28.6342 109.076 28.6672 109.65 28.6672C109.907 28.6662 106.601 28.725 106.601 28.725Z"
              fill="white"
            />
            <path
              d="M81.8653 27.6794L82.782 25.8074H80.0093L79.9246 26.2133C79.9227 26.2241 79.7336 27.5416 77.6885 30.0406C75.6509 32.5321 74.1886 32.9178 74.1886 32.9178L74.1792 35.319C74.1792 35.319 76.3235 34.6684 78.2854 32.8167C79.1048 32.0429 80.3761 30.3233 80.6593 29.9376H87.0369C86.8972 30.6798 86.2156 33.3402 83.3253 36.4215C81.3154 38.5691 78.0629 39.9397 77.2088 40.1787C76.3565 40.4195 75.7332 40.4708 75.7332 40.4708V42.9971C75.7332 42.9971 76.0954 42.8555 76.5728 42.7473C77.3922 42.5582 81.7082 41.3104 85.2782 37.8452C88.1573 35.0433 89.2857 30.9517 89.6384 29.32L89.6511 29.2612C89.7523 28.8073 90.0608 27.9532 90.0627 27.9456L90.1563 27.6883L81.8653 27.6794Z"
              fill="white"
            />
            <path
              d="M74.507 26.6136H65.383L64.1996 26.6173C61.9047 26.6192 57.7228 26.496 57.7228 26.496L57.7284 28.9729C57.7284 28.9729 59.5327 28.8534 60.33 28.8534L65.2264 28.8332H71.4549C71.3171 29.3717 70.8613 30.7936 69.6214 32.0137C68.3848 33.2282 66.946 33.5518 66.406 33.6341C66.43 33.3326 66.4648 32.7964 66.4648 32.359C66.4648 31.7452 66.5843 30.0458 66.5843 30.0458H64.1389L64.091 33.3806C64.091 33.9831 63.6921 36.6068 61.741 38.4534C59.8761 40.2154 57.989 40.522 57.989 40.522V42.982C57.989 42.982 61.4725 42.0135 63.5801 39.9595C65.3953 38.19 65.8807 36.5108 65.9888 36.0146C66.6887 35.9248 69.7423 35.4064 71.7324 33.1943C73.9115 30.7711 74.4534 26.9913 74.4774 26.8337L74.507 26.6136Z"
              fill="white"
            />
          </g>
          <defs>
            <clippath
              id="clip0_20792_2397"
            >
              <rect
                fill="white"
                height="48"
                width="149"
              />
            </clippath>
          </defs>
        </svg>
      </div>
      <nav
        class="c3"
      >
        <button
          aria-label="Facebook"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.facebook.com/axajapan/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5 1.65242V4.98415H14.592C13.8953 4.98415 13.4253 5.13559 13.1823 5.43848C12.9392 5.74136 12.8177 6.19569 12.8177 6.80146V9.18667H16.3785L15.9045 12.9223H12.8177V22.501H9.09896V12.9223H6V9.18667H9.09896V6.43547C9.09896 4.87057 9.52025 3.65693 10.3628 2.79455C11.2054 1.93217 12.3275 1.50098 13.7292 1.50098C14.9201 1.50098 15.8438 1.55146 16.5 1.65242Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Twitter"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://twitter.com/AXA_JP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.1761 4.00098H19.9362L13.9061 10.7784L21 20.001H15.4456L11.0951 14.4075L6.11723 20.001H3.35544L9.80517 12.7517L3 4.00098H8.69545L12.6279 9.11359L17.1761 4.00098ZM16.2073 18.3764H17.7368L7.86441 5.54026H6.2232L16.2073 18.3764Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="LinkedIn"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.linkedin.com/company/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.17258 22.501V9.34436H1.76032V22.501H6.17258ZM3.96702 7.54697C5.50566 7.54697 6.46338 6.53669 6.46338 5.27419C6.43471 3.98321 5.50571 3.00098 3.99622 3.00098C2.48696 3.00098 1.5 3.98323 1.5 5.27419C1.5 6.53675 2.45749 7.54697 3.93822 7.54697H3.96689H3.96702ZM8.61476 22.501H13.027V15.1537C13.027 14.7605 13.0557 14.3677 13.1722 14.0866C13.4912 13.3009 14.2172 12.4872 15.436 12.4872C17.0326 12.4872 17.6714 13.6937 17.6714 15.4624V22.5009H22.0834V14.957C22.0834 10.9158 19.9066 9.03548 17.0036 9.03548C14.6233 9.03548 13.5783 10.3541 12.9977 11.2522H13.0271V9.34409H8.61485C8.67276 10.5786 8.61485 22.5007 8.61485 22.5007L8.61476 22.501Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Instagram"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.instagram.com/axa/"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.3663 9.24542C10.646 9.24542 9.2421 10.6493 9.2421 12.3696C9.2421 14.09 10.646 15.4939 12.3663 15.4939C14.0866 15.4939 15.4905 14.09 15.4905 12.3696C15.4905 10.6493 14.0866 9.24542 12.3663 9.24542ZM21.7366 12.3696C21.7366 11.0759 21.7484 9.79386 21.6757 8.50245C21.603 7.00245 21.2609 5.6712 20.164 4.57433C19.0648 3.47511 17.7359 3.13527 16.2359 3.06261C14.9421 2.98995 13.6601 3.00167 12.3687 3.00167C11.0749 3.00167 9.79288 2.98995 8.50148 3.06261C7.00148 3.13527 5.67023 3.47745 4.57335 4.57433C3.47413 5.67355 3.13429 7.00245 3.06163 8.50245C2.98898 9.7962 3.0007 11.0782 3.0007 12.3696C3.0007 13.661 2.98898 14.9454 3.06163 16.2368C3.13429 17.7368 3.47648 19.0681 4.57335 20.165C5.67257 21.2642 7.00148 21.604 8.50148 21.6767C9.79523 21.7493 11.0773 21.7376 12.3687 21.7376C13.6624 21.7376 14.9444 21.7493 16.2359 21.6767C17.7359 21.604 19.0671 21.2618 20.164 20.165C21.2632 19.0657 21.603 17.7368 21.6757 16.2368C21.7507 14.9454 21.7366 13.6634 21.7366 12.3696ZM12.3663 17.1767C9.70617 17.1767 7.55929 15.0298 7.55929 12.3696C7.55929 9.70949 9.70617 7.56261 12.3663 7.56261C15.0265 7.56261 17.1734 9.70949 17.1734 12.3696C17.1734 15.0298 15.0265 17.1767 12.3663 17.1767ZM17.3702 8.48839C16.7491 8.48839 16.2476 7.98683 16.2476 7.36574C16.2476 6.74464 16.7491 6.24308 17.3702 6.24308C17.9913 6.24308 18.4929 6.74464 18.4929 7.36574C18.4931 7.51322 18.4642 7.65929 18.4078 7.79558C18.3515 7.93187 18.2688 8.0557 18.1645 8.15999C18.0602 8.26427 17.9364 8.34696 17.8001 8.40331C17.6638 8.45967 17.5177 8.48858 17.3702 8.48839Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
        <button
          aria-label="Youtube"
          class="c4"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c5"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c6"
            >
              <a
                class="c7"
                color="white"
                href="https://www.youtube.com/c/AXAJP"
                tabindex="-1"
                target="_blank"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.1157 5.00098H12.2331C13.317 5.00476 18.8088 5.0426 20.2895 5.42349C20.7371 5.53974 21.1451 5.76632 21.4725 6.0806C21.7999 6.39488 22.0353 6.78585 22.1553 7.21444C22.2884 7.69371 22.3821 8.32811 22.4454 8.98269L22.4585 9.11386L22.4875 9.44178L22.4981 9.57295C22.5838 10.7257 22.5944 11.8053 22.5957 12.0412V12.1358C22.5944 12.3805 22.5825 13.5332 22.4875 14.7339L22.477 14.8663L22.4651 14.9975C22.3992 15.7189 22.3016 16.4353 22.1553 16.9625C22.0357 17.3913 21.8004 17.7824 21.4729 18.0968C21.1454 18.4111 20.7373 18.6376 20.2895 18.7535C18.76 19.147 12.9464 19.1747 12.1408 19.176H11.9536C11.5461 19.176 9.861 19.1684 8.09413 19.1104L7.86997 19.1028L7.75526 19.0978L7.52978 19.089L7.30431 19.0801C5.84071 19.0183 4.44699 18.9187 3.80485 18.7522C3.35718 18.6364 2.94919 18.4101 2.62173 18.096C2.29428 17.7819 2.05887 17.391 1.93908 16.9625C1.79272 16.4366 1.69515 15.7189 1.62922 14.9975L1.61867 14.8651L1.60812 14.7339C1.54305 13.8793 1.50699 13.0229 1.5 12.166L1.5 12.0109C1.50264 11.7397 1.51319 10.8026 1.58439 9.76844L1.59362 9.63853L1.59757 9.57295L1.60812 9.44178L1.63713 9.11386L1.65032 8.98269C1.71361 8.32811 1.80722 7.69245 1.9404 7.21444C2.05998 6.78568 2.29531 6.39453 2.62277 6.08019C2.95024 5.76586 3.35834 5.53939 3.80616 5.42349C4.4483 5.25953 5.84202 5.15863 7.30563 5.09557L7.52978 5.08674L7.75658 5.07917L7.86997 5.07539L8.09545 5.06656C9.35033 5.02794 10.6057 5.00649 11.8613 5.00224H12.1157V5.00098ZM9.9388 9.04953V15.1262L15.4201 12.0891L9.9388 9.04953Z"
                  />
                </svg>
              </a>
            </span>
          </div>
        </button>
      </nav>
    </div>
    <nav
      class="c9"
    >
      <div>
        <ul
          class="c10"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              勧誘方針
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              バイク保険
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              ペット保険
            </a>
          </li>
        </ul>
      </div>
      <div>
        <ul
          class="c10"
        >
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ生命
              <span
                class="c11"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサダイレクト(損害保険)
              <span
                class="c11"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ生命のネット完結保険
              <span
                class="c11"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              アクサ・ホールディングス・ジャパン
              <span
                class="c11"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
          <li>
            <a
              class="c7"
              href="#/Navigation/Footer"
              style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
            >
              AXA.com
              <span
                class="c11"
                style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
              >
                <svg
                  class="c8"
                  fill="none"
                  focusable="false"
                  style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 21.5007C4.45 21.5007 3.97917 21.3049 3.5875 20.9132C3.19583 20.5216 3 20.0507 3 19.5007V5.50073C3 4.95073 3.19583 4.4799 3.5875 4.08823C3.97917 3.69657 4.45 3.50073 5 3.50073H12V5.50073H5V19.5007H19V12.5007H21V19.5007C21 20.0507 20.8042 20.5216 20.4125 20.9132C20.0208 21.3049 19.55 21.5007 19 21.5007H5ZM9.7 16.2007L8.3 14.8007L17.6 5.50073H14V3.50073H21V10.5007H19V6.90073L9.7 16.2007Z"
                  />
                </svg>
              </span>
            </a>
          </li>
        </ul>
      </div>
    </nav>
    <div
      class="c12"
    >
      <p
        class="c13"
      >
        Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.
      </p>
    </div>
  </footer>
</div>
`;

exports[`Footer > renders without crashing 1`] = `
.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
  padding: var(--ajds-spacing-6) 0;
}

.c2 {
  -webkit-align-self: flex-end;
  -ms-flex-item-align: end;
  align-self: flex-end;
  font-size: var(--ajds-font-size-sm);
  line-height: var(--ajds-line-height);
  margin: 0;
  margin-left: auto;
  padding-block: var(--ajds-spacing-px);
  text-align: right;
}

.c0 {
  background-color: var(--ajds-color-ocean-200);
  color: var(--ajds-color-white);
  padding: 0 var(--ajds-spacing-4);
}

@media (max-width:1279px) {
  .c1 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: var(--ajds-spacing-8);
  }
}

@media (max-width:599px) {
  .c2 {
    width: 100%;
    text-align: center;
  }
}

@media (min-width:600px) {
  .c2 {
    white-space: nowrap;
  }
}

@media (min-width:600px) {
  .c0 {
    padding: 0 var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c0 {
    padding: 0 var(--ajds-spacing-8);
  }
}

<div>
  <footer
    class="c0"
  >
    <div
      class="c1"
    >
      <p
        class="c2"
      >
        Copyright © AXA Life Insurance Co.,Ltd. All Rights Reserved.
      </p>
    </div>
  </footer>
</div>
`;

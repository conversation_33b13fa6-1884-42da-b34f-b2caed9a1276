### Full Footer

For consistency the links section will only accept having every section with a header, or none. It is not possible for only a certain column to have a heading. Additionally, the last column is the only column to support icons after the links.

Please also note that the social links and logo will only show if either the links or utilityLinks properties are populated.

```js
import Footer from '@axa-japan/design-system-react/Footer';
import { ExternalIcon } from '@axa-japan/design-system-react/Icons';

<Footer
  brand="axa-life"
  links={[
    {
      heading: 'アクサ生命について',
      links: [
        { text: '勧誘方針', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'バイク保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'ペット保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      ],
    },
    {
      heading: 'ご契約者様・ご契約される方',
      links: [
        { text: '取引時確認について', url: '#/Navigation/Footer' },
        { text: '生命保険相談所(裁定審査会)について', url: '#/Navigation/Footer' },
        { text: '生命保険契約者保護機構について', url: '#/Navigation/Footer' },
      ],
    },
    {
      heading: 'クイックリンク',
      links: [
        { text: '日本版スチュワードシップ・コードの受け入れ', url: '#/Navigation/Footer' },
        { text: 'AXAグループデータプライバシー宣言', url: '#/Navigation/Footer' },
      ],
    },
    {
      heading: 'アクサのグループ会社',
      links: [
        { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
        { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      ],
    },
  ]}
  utilityLinks={[
    { text: 'サイトマップ', url: '#/Navigation/Footer' },
    { text: 'サイトポリシー', url: '#/Navigation/Footer' },
    { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
    { text: 'Cookie設定', url: '#/Navigation/Footer' },
  ]}
/>;
```

### Two Columns With No Headings

This also uses a react router link, similar to how it is supported in the Link component.

```js
import Footer from '@axa-japan/design-system-react/Footer';
import { ExternalIcon } from '@axa-japan/design-system-react/Icons';
import { Link as ReactRouterLink } from 'react-router-dom';

<Footer
  brand="axa-life"
  links={[
    [
      { children: <ReactRouterLink to="#/Navigation/Footer">React Router Link</ReactRouterLink> },
      { text: 'バイク保険', url: '#/Navigation/Footer' },
      { text: 'ペット保険', url: '#/Navigation/Footer' },
    ],
    [
      { text: 'アクサ生命', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      { text: 'アクサダイレクト(損害保険)', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      { text: 'アクサ生命のネット完結保険', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      { text: 'アクサ・ホールディングス・ジャパン', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
      { text: 'AXA.com', url: '#/Navigation/Footer', icon: <ExternalIcon /> },
    ],
  ]}
/>;
```

### Footer with only copyright-level links

```js
import Footer from '@axa-japan/design-system-react/Footer';

<Footer
  brand="axa-life"
  utilityLinks={[
    { text: 'サイトマップ', url: '#/Navigation/Footer' },
    { text: 'サイトポリシー', url: '#/Navigation/Footer' },
    { text: '個人情報の取り扱い', url: '#/Navigation/Footer' },
    { text: 'Cookie設定', url: '#/Navigation/Footer' },
  ]}
/>;
```

### Basic Footer

This is intended only for use in internal applications.

```js
import Footer from '@axa-japan/design-system-react/Footer';

<Footer brand="axa-life" />;
```

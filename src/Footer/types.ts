type Link = {
  text: string;
  url: string;
  /** Component to use for the icon */
  icon?: React.ReactElement;
};

export type LinkEntry =
  | Link
  | {
      children: React.ReactElement;
      /** Component to use for the icon */
      icon?: React.ReactElement;
    };
export type FixedLengthTuple<T, K = T> = [K] | [T, K] | [T, T, K] | [T, T, T, K];
export type TextLink = Link & { id?: string };

import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../CheckboxGroupField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('CheckboxGroupField', () => {
  test('DefaultWithRequired', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        required={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
    );
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
      {
        interactionSelector: 'label[data-part="root"]:nth-child(1)',
        interactionType: 'focus',
      },
    );
  });

  test('DisplayInline', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        displayInline={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
    );
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        defaultValue={['2']}
        options={[
          { label: '', value: '1' },
          { label: '', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
    );
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        disabled={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        errorMessage="error error error error"
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
    );
  });

  test('HasErrorFocus', async ({ task }) => {
    await takeScreenshot(
      <CheckboxGroupField
        label="title"
        errorMessage="error error error error"
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
        onChange={() => console.log('change')}
      />,
      task,
      {
        interactionSelector: 'label[data-part="root"]:nth-child(1)',
        interactionType: 'focus',
      },
    );
  });
});

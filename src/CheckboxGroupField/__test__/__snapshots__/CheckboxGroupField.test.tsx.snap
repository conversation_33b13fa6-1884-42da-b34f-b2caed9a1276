// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CheckboxGroupField > renders without crashing for default with required 1`] = `
.c10 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7[data-disabled] {
  cursor: not-allowed;
}

.c7[data-disabled]:hover {
  background-color: initial;
}

.c7:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c7[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c9[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c9[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c9[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c9[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c9[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c9[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c9[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c6:hover > .c8[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c11 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c11[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c11[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c5[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c5[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r1:"
    >
      title
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <div
        class="c5"
        data-orientation="vertical"
        data-part="group"
        data-scope="checkbox"
        id="check-id-:r0:"
        role="group"
      >
        <label
          class="c6 c7"
          data-hover=""
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::r2::input"
          id="checkbox::r2:"
        >
          <div
            aria-hidden="true"
            class="c8 c9"
            data-hover=""
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r2::control"
          >
            <svg
              class="c10"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c11"
            data-hover=""
            data-part="label"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r2::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::r2::label"
            id="checkbox::r2::input"
            name="check-id-:r0:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="1"
          />
        </label>
        <label
          class="c6 c7"
          data-focus=""
          data-hover=""
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::r3::input"
          id="checkbox::r3:"
        >
          <div
            aria-hidden="true"
            class="c8 c9"
            data-focus=""
            data-hover=""
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r3::control"
          >
            <svg
              class="c10"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c11"
            data-focus=""
            data-hover=""
            data-part="label"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r3::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::r3::label"
            id="checkbox::r3::input"
            name="check-id-:r0:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="2"
          />
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`CheckboxGroupField > renders without crashing with disabled 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5[data-disabled] {
  cursor: not-allowed;
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c7[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c7[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c7[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c7[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c7[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c9 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c9[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c3[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:rd:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
        data-orientation="vertical"
        data-part="group"
        data-scope="checkbox"
        id="check-id-:rc:"
        role="group"
      >
        <label
          class="c4 c5"
          data-disabled=""
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::re::input"
          id="checkbox::re:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-disabled=""
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::re::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-disabled=""
            data-part="label"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::re::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::re::label"
            disabled=""
            id="checkbox::re::input"
            name="check-id-:rc:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="1"
          />
        </label>
        <label
          class="c4 c5"
          data-disabled=""
          data-part="root"
          data-scope="checkbox"
          data-state="checked"
          dir="ltr"
          for="checkbox::rf::input"
          id="checkbox::rf:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-disabled=""
            data-part="control"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::rf::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-disabled=""
            data-part="label"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::rf::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::rf::label"
            checked=""
            disabled=""
            id="checkbox::rf::input"
            name="check-id-:rc:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="2"
          />
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`CheckboxGroupField > renders without crashing with display inline 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5[data-disabled] {
  cursor: not-allowed;
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c7[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c7[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c7[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c7[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c7[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c9 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c9[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c3[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r5:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
        data-orientation="horizontal"
        data-part="group"
        data-scope="checkbox"
        id="check-id-:r4:"
        role="group"
      >
        <label
          class="c4 c5"
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::r6::input"
          id="checkbox::r6:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r6::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-part="label"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::r6::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::r6::label"
            id="checkbox::r6::input"
            name="check-id-:r4:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="1"
          />
        </label>
        <label
          class="c4 c5"
          data-part="root"
          data-scope="checkbox"
          data-state="checked"
          dir="ltr"
          for="checkbox::r7::input"
          id="checkbox::r7:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-part="control"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::r7::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-part="label"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::r7::label"
          >
            check
          </span>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::r7::label"
            checked=""
            id="checkbox::r7::input"
            name="check-id-:r4:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="2"
          />
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`CheckboxGroupField > renders without crashing with error 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5[data-disabled] {
  cursor: not-allowed;
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c7[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c7[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c7[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c7[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c7[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c9 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c9[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c9[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c3[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:rh:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
        data-orientation="vertical"
        data-part="group"
        data-scope="checkbox"
        id="check-id-:rg:"
        role="group"
      >
        <label
          class="c4 c5"
          data-invalid=""
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::ri::input"
          id="checkbox::ri:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-invalid=""
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::ri::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-invalid=""
            data-part="label"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::ri::label"
          >
            check
          </span>
          <input
            aria-invalid="true"
            aria-labelledby="checkbox::ri::label"
            id="checkbox::ri::input"
            name="check-id-:rg:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="1"
          />
        </label>
        <label
          class="c4 c5"
          data-invalid=""
          data-part="root"
          data-scope="checkbox"
          data-state="checked"
          dir="ltr"
          for="checkbox::rj::input"
          id="checkbox::rj:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-invalid=""
            data-part="control"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::rj::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <span
            class="c9"
            data-invalid=""
            data-part="label"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::rj::label"
          >
            check
          </span>
          <input
            aria-invalid="true"
            aria-labelledby="checkbox::rj::label"
            checked=""
            id="checkbox::rj::input"
            name="check-id-:rg:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="2"
          />
        </label>
      </div>
    </div>
    <div
      aria-live="polite"
      class="c10"
      role="alert"
    >
      <div
        class="c11"
      >
        <svg
          class="c8"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      error error error error
    </div>
  </fieldset>
</div>
`;

exports[`CheckboxGroupField > renders without crashing without label 1`] = `
.c8 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5[data-disabled] {
  cursor: not-allowed;
}

.c5[data-disabled]:hover {
  background-color: initial;
}

.c5:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c5[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c5[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c7[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c7[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c7[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c7[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c7[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c7[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c7[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c4:hover > .c6[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c3[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c3[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r9:"
    >
      title
    </legend>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
        data-orientation="vertical"
        data-part="group"
        data-scope="checkbox"
        id="check-id-:r8:"
        role="group"
      >
        <label
          class="c4 c5"
          data-part="root"
          data-scope="checkbox"
          data-state="unchecked"
          dir="ltr"
          for="checkbox::ra::input"
          id="checkbox::ra:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-part="control"
            data-scope="checkbox"
            data-state="unchecked"
            dir="ltr"
            id="checkbox::ra::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::ra::label"
            id="checkbox::ra::input"
            name="check-id-:r8:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="1"
          />
        </label>
        <label
          class="c4 c5"
          data-part="root"
          data-scope="checkbox"
          data-state="checked"
          dir="ltr"
          for="checkbox::rb::input"
          id="checkbox::rb:"
        >
          <div
            aria-hidden="true"
            class="c6 c7"
            data-part="control"
            data-scope="checkbox"
            data-state="checked"
            dir="ltr"
            id="checkbox::rb::control"
          >
            <svg
              class="c8"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
              />
            </svg>
          </div>
          <input
            aria-invalid="false"
            aria-labelledby="checkbox::rb::label"
            checked=""
            id="checkbox::rb::input"
            name="check-id-:r8:"
            style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
            type="checkbox"
            value="2"
          />
        </label>
      </div>
    </div>
  </fieldset>
</div>
`;

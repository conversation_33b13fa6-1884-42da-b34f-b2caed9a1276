import React from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import CheckboxGroupField from '../CheckboxGroupField';

describe('CheckboxGroupField', () => {
  beforeAll(() => {
    (window as any).PointerEvent = MouseEvent;
  });
  test('renders without crashing for default with required', async () => {
    const { queryAllByText, getAllByRole, container } = render(
      <CheckboxGroupField
        label="title"
        required={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1', defaultChecked: true },
          { label: 'check', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);
    const checkboxes = getAllByRole('checkbox');
    expect(checkboxes).toHaveLength(2);
    await waitFor(() => {
      checkboxes.forEach((checkbox) => expect(checkbox.getAttribute('checked')).toEqual(''));
    });
    await userEvent.click(checkboxes[0]);
    await userEvent.click(checkboxes[1]);
    await waitFor(() => {
      checkboxes.forEach((checkbox) => expect(checkbox.getAttribute('checked')).toEqual(null));
    });
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with display inline', () => {
    const { queryAllByText, container } = render(
      <CheckboxGroupField
        label="title"
        displayInline={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { queryAllByText, getAllByRole, container } = render(
      <CheckboxGroupField
        label="title"
        defaultValue={['2']}
        options={[
          { label: '', value: '1' },
          { label: '', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const allTestElements = getAllByRole('checkbox');
    expect(allTestElements.length).toEqual(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled', () => {
    const { queryAllByText, container } = render(
      <CheckboxGroupField
        label="title"
        disabled={true}
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with error', () => {
    const { queryAllByText, container } = render(
      <CheckboxGroupField
        label="title"
        errorMessage="error error error error"
        defaultValue={['2']}
        options={[
          { label: 'check', value: '1' },
          { label: 'check', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const errorText = queryAllByText('error error error error');
    expect(errorText).toHaveLength(1);
    const testElement = queryAllByText('check');
    expect(testElement).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });
});

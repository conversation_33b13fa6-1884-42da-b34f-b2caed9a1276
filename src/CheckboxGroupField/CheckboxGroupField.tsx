import React, { forwardRef } from 'react';
import { CheckboxGroupFieldBase } from './CheckboxGroupFieldBase';
import { CheckboxGroupFieldProps } from './types';

const CheckboxGroupField = forwardRef<HTMLDivElement, CheckboxGroupFieldProps>((props, ref) => {
  return <CheckboxGroupFieldBase {...{ ...props }} ref={ref} />;
});

CheckboxGroupField.displayName = 'CheckboxGroupField';

export default CheckboxGroupField;

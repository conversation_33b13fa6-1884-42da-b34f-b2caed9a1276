import { CheckboxGroupProps, CheckboxRootBaseProps } from '@ark-ui/react';
import { CommonFieldBaseProps, UseFieldsetWrapperProp } from '../FieldBase/FieldBase';
import { CheckboxOptions } from '../Checkbox/Checkbox';

export type CheckboxGroupFieldProps = {
  /** Set to true to have Checkboxes aligned horizontally */
  displayInline?: boolean;
  /** Props for Checkbox components, at least one must be provided */
  options: [CheckboxOptions, ...CheckboxOptions[]];
} & CommonFieldBaseProps &
  Pick<React.ComponentPropsWithoutRef<'input'>, 'onChange'> &
  Pick<CheckboxRootBaseProps, 'disabled' | 'form' | 'id' | 'name'> &
  Pick<CheckboxGroupProps, 'defaultValue' | 'value' | 'onValueChange'>;

export type CheckboxGroupFieldBaseProps = CheckboxGroupFieldProps & UseFieldsetWrapperProp;

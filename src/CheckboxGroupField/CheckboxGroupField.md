The component accepts an array of _options_, where you can specify _label_, _value_, _data-testid_ for each option.

### Example CheckboxGroupField - Uncontrolled with required

```js
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

<CheckboxGroupField
  label="Uncontrolled"
  defaultValue={['2']}
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
  ]}
/>;
```

### Example CheckboxGroupField - Controlled

```js
import React, { useState } from 'react';
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

const [value, setValue] = useState(['2']);

<CheckboxGroupField
  label="Controlled"
  required={true}
  value={value}
  onValueChange={setValue}
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
  ]}
/>;
```

### Example CheckboxGroupField - Horizontal

```js
import CheckboxGroup<PERSON>ield from '@axa-japan/design-system-react/CheckboxGroupField';

<CheckboxGroupField
  label="Horizontal"
  displayInline={true}
  defaultValue={['2', '3', '4', '5', '6']}
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
    { label: 'check', value: '3' },
    { label: 'check', value: '4' },
    { label: 'check', value: '5' },
    { label: 'check', value: '6' },
  ]}
/>;
```

### Example CheckboxGroupField - Default No Label

```js
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

<CheckboxGroupField
  defaultValue={['2']}
  options={[
    { label: '', value: '1' },
    { label: '', value: '2' },
  ]}
/>;
```

### Example CheckboxGroupField - Disabled

```js
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

<CheckboxGroupField
  label="Disabled"
  disabled={true}
  defaultValue={['2']}
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
  ]}
/>;
```

### Example CheckboxGroupField - Error

```js
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

<CheckboxGroupField
  label="Error"
  defaultValue={['2']}
  errorMessage="error error error error"
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
  ]}
/>;
```

### Example CheckboxGroupField - Retrieving Checkbox's properties onChange

If you want to get the value of the Checkbox onChange, you can retrieve the CheckboxGroupField element and their properties by wrapping &lt;CheckboxGroupField&gt; in a &lt;Form&gt; like so:

```js
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

<form
  id="form1"
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
>
  <CheckboxGroupField
    form="form1"
    label="Form integration"
    displayInline={true}
    defaultValue={['2', '3']}
    options={[
      { label: 'check1', value: '1' },
      { label: 'check2', value: '2' },
      { label: 'check3', value: '3' },
    ]}
  />
</form>;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the &lt;div&gt; container of the lt;CheckboxGroupField&gt;.

```js
import React, { useRef } from 'react';
import CheckboxGroupField from '@axa-japan/design-system-react/CheckboxGroupField';

const ref = useRef(null);
<CheckboxGroupField
  ref={ref}
  label="Ref"
  required={true}
  defaultValue={['2']}
  options={[
    { label: 'check', value: '1' },
    { label: 'check', value: '2' },
  ]}
/>;
```

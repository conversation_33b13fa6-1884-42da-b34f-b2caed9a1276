import React, { forwardRef, useId } from 'react';
import checkHasError from '../utils/checkHasError';
import FieldBase from '../FieldBase/FieldBase';
import Checkbox from '../Checkbox/Checkbox';
import CheckboxGroup from '../Checkbox/CheckboxGroup';
import { CheckboxGroupFieldBaseProps } from './types';

export const CheckboxGroupFieldBase = forwardRef<HTMLDivElement, CheckboxGroupFieldBaseProps>(
  (
    {
      id,
      name,
      form,
      label,
      onChange,
      showError,
      errorMessage,
      required = false,
      showRequiredIndicator,
      displayInline = false,
      disabled = false,
      useFieldsetWrapper = true,
      options,
      value,
      defaultValue,
      onValueChange,
      sx,
    },
    ref,
  ) => {
    const hasError = checkHasError(showError, errorMessage);
    const fallbackId = `check-id-${useId()}`;
    // If the user passes defaultChecked in options, combine it with defaultValue if it's passed.
    // This is to support a legacy feature, defaultChecked which is deprecated in Checkbox, will be removed soon.
    const defaultValueFromOptions = options
      .filter((option) => option.defaultChecked && !defaultValue?.includes(option.value))
      .flatMap((option) => option.value);
    const aggregatedDefaultValue = defaultValue ? [...defaultValue, ...defaultValueFromOptions] : undefined;
    return (
      <FieldBase
        useFieldsetWrapper={useFieldsetWrapper}
        id={id || name || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <CheckboxGroup
          disabled={disabled}
          invalid={hasError}
          name={name || fallbackId}
          id={id || name || fallbackId}
          value={value}
          defaultValue={aggregatedDefaultValue}
          onValueChange={onValueChange}
          ref={ref}
          data-orientation={displayInline ? 'horizontal' : 'vertical'}
        >
          {options.map((option, idx) => {
            return <Checkbox key={`${fallbackId}:${idx}`} form={form} onChange={onChange} {...option} />;
          })}
        </CheckboxGroup>
      </FieldBase>
    );
  },
);

CheckboxGroupFieldBase.displayName = 'CheckboxGroupFieldBase';

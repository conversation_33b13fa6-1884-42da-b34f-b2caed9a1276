import { CommonFieldBaseProps, UseFieldsetWrapperProp } from '../FieldBase/FieldBase';
import { RadioOptions } from '../Radio/Radio';

export type RadioGroupFieldProps = {
  /** Set to true to have Radio Buttons aligned horizontally */
  displayInline?: boolean;
  /** Props for Radio components, at least one must be provided */
  options: [RadioOptions, ...RadioOptions[]];
} & CommonFieldBaseProps &
  Pick<React.ComponentPropsWithoutRef<'input'>, 'defaultValue' | 'disabled' | 'form' | 'id' | 'name' | 'value' | 'onChange'>;

export type RadioGroupFieldBaseProps = RadioGroupFieldProps & UseFieldsetWrapperProp;

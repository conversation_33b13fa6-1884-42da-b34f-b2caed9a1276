import React, { forwardRef, useId } from 'react';
import FieldBase from '../FieldBase/FieldBase';
import RadioRoot from '../Radio/RadioRoot';
import Radio from '../Radio/Radio';
import checkHasError from '../utils/checkHasError';
import { RadioGroupFieldBaseProps } from './types';

export const RadioGroupFieldBase = forwardRef<HTMLDivElement, RadioGroupFieldBaseProps>(
  (
    {
      id,
      name,
      form,
      label,
      onChange,
      showError,
      errorMessage,
      required = false,
      showRequiredIndicator,
      value,
      defaultValue,
      displayInline = false,
      disabled = false,
      useFieldsetWrapper = true,
      options,
      sx,
    },
    ref,
  ) => {
    const hasError = checkHasError(showError, errorMessage);
    const fallbackId = `radio-id-${useId()}`;

    return (
      <FieldBase
        useFieldsetWrapper={useFieldsetWrapper}
        id={`radio-group:${id || name || fallbackId}`}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <RadioRoot
          disabled={disabled}
          ref={ref}
          id={id || name || fallbackId}
          name={name || fallbackId}
          form={form}
          defaultValue={defaultValue?.toString()}
          value={value?.toString()}
          orientation={displayInline ? 'horizontal' : 'vertical'}
          onChange={onChange}
        >
          {options.map((option, idx) => {
            return <Radio key={`${fallbackId}:${idx}`} hasError={hasError} form={form} {...option} />;
          })}
        </RadioRoot>
      </FieldBase>
    );
  },
);

RadioGroupFieldBase.displayName = 'RadioGroupFieldBase';

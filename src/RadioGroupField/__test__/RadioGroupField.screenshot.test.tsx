import React from 'react';
import RadioGroupField from '../RadioGroupField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('RadioGroupField', () => {
  test('DefaultWithRequired', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Active"
        required={true}
        defaultValue="2"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Focused"
        defaultValue="2"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
      {
        interactionSelector: 'label[data-part="item"]:nth-child(1)',
        interactionType: 'focus',
      },
    );
  });

  test('DisplayInline', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Horizontal"
        defaultValue="2"
        displayInline={true}
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="No Label"
        defaultValue="2"
        options={[
          { label: '', value: '1' },
          { label: '', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Disabled"
        defaultValue="2"
        disabled={true}
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Error"
        defaultValue="2"
        errorMessage="error error error error"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasErrorFocus', async ({ task }) => {
    await takeScreenshot(
      <RadioGroupField
        label="Error with focus"
        defaultValue="2"
        errorMessage="error error error error"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
      task,
      {
        interactionSelector: 'label[data-part="item"]:nth-child(1)',
        interactionType: 'focus',
      },
    );
  });
});

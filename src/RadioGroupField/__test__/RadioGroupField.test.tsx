import React from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import RadioGroupField from '../RadioGroupField';

describe('RadioGroupField', () => {
  test('renders without crashing for default with required', async () => {
    const { queryAllByText, container } = render(
      <RadioGroupField
        label="title"
        required={true}
        defaultValue="2"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);
    const title = queryAllByText('radio');
    expect(title).toHaveLength(2);
    // Check that interactions are working properly
    expect(title[0].getAttribute('data-state')).toBe('unchecked');
    expect(title[1].getAttribute('data-state')).toBe('checked');
    await userEvent.click(title[0]);
    await waitFor(() => {
      expect(title[0].getAttribute('data-state')).toBe('checked');
      expect(title[1].getAttribute('data-state')).toBe('unchecked');
    });
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with display inline', () => {
    const { queryAllByText, container } = render(
      <RadioGroupField
        label="title"
        defaultValue="2"
        displayInline={true}
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('radio');
    expect(testElement).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing without label', () => {
    const { queryAllByText, getAllByRole, container } = render(
      <RadioGroupField
        label="title"
        defaultValue="2"
        options={[
          { label: '', value: '1' },
          { label: '', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const allTestElements = getAllByRole('radio');
    expect(allTestElements.length).toEqual(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled', () => {
    const { queryAllByText, getAllByRole, container } = render(
      <RadioGroupField
        label="title"
        defaultValue="2"
        disabled={true}
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('radio');
    expect(testElement).toHaveLength(2);
    const testItems = getAllByRole('radio');
    testItems.forEach((radio) => expect(radio).toBeDisabled());
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with error', () => {
    const { queryAllByText, container } = render(
      <RadioGroupField
        label="title"
        defaultValue="2"
        errorMessage="error error error error"
        options={[
          { label: 'radio', value: '1' },
          { label: 'radio', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const errorText = queryAllByText('error error error error');
    expect(errorText).toHaveLength(1);
    const testItems = queryAllByText('radio');
    expect(testItems).toHaveLength(2);
    testItems.forEach((item) => expect(item.getAttribute('data-invalid')).not.toBeUndefined());
    expect(container).toMatchSnapshot();
  });
});

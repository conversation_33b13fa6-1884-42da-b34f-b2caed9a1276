### Example RadioGroupField - Uncontrolled with required

If you want to get the value of the Radio onChange, you can retrieve the Radio element and its HTMLInputElement properties through an onChange handler:

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<RadioGroupField
  label="Uncontrolled with Required"
  required={true}
  defaultValue="2"
  options={[
    { label: 'radio', value: '1' },
    { label: 'radio', value: '2' },
  ]}
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
/>;
```

### Example RadioGroupField - Controlled

The component can be either controlled or uncontrolled by passing the _value_ or _defaultValue_ prop respectively.

```js
import React, { useState } from 'react';
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

const [value, setValue] = useState('2');

<RadioGroupField
  label="Controlled"
  value={value}
  options={[
    { label: 'radio', value: '1' },
    { label: 'radio', value: '2' },
  ]}
  onChange={(e) => {
    setValue(e.target.value);
  }}
/>;
```

### Example RadioGroupField - Horizontal

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<RadioGroupField
  label="Horizontal"
  displayInline={true}
  defaultValue="8"
  options={[
    { label: 'radio', value: '3' },
    { label: 'radio', value: '4' },
    { label: 'radio', value: '5' },
    { label: 'radio', value: '6' },
    { label: 'radio', value: '7' },
    { label: 'radio', value: '8' },
  ]}
/>;
```

### Example RadioGroupField - No Label

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<RadioGroupField
  defaultValue="2"
  options={[
    { label: '', value: '1' },
    { label: '', value: '2' },
  ]}
/>;
```

### Example RadioGroupField - Disabled

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<RadioGroupField
  label="Disabled"
  disabled={true}
  defaultValue="2"
  options={[
    { label: 'radio', value: '1' },
    { label: 'radio', value: '2' },
  ]}
/>;
```

### Example RadioGroupField - Error

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<RadioGroupField
  label="Error"
  errorMessage="error error error error"
  defaultValue="2"
  options={[
    { label: 'radio', value: '1' },
    { label: 'radio', value: '2' },
  ]}
/>;
```

### Example RadioGroupField - Form Interaction

If you want to get the value of the Radio onChange through a &lt;Form&gt;, you can do so by wrapping &lt;RadioGroupField&gt; in a &lt;Form&gt;:

```js
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

<form
  id="form1"
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
>
  <RadioGroupField
    form="form1"
    label="Form interaction"
    displayInline={true}
    defaultValue="3"
    options={[
      { label: 'radio1', value: '1' },
      { label: 'radio2', value: '2' },
      { label: 'radio3', value: '3' },
    ]}
  />
</form>;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the &lt;div&gt; container of the &lt;RadioGroupField&gt;.

```js
import React, { useRef } from 'react';
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

const Render = () => {
  const ref = useRef(null);
  return (
    <RadioGroupField
      ref={ref}
      label="Ref"
      defaultValue="2"
      options={[
        { label: 'radio', value: '1' },
        { label: 'radio', value: '2' },
      ]}
    />
  );
};

<Render />;
```

import React, { forwardRef } from 'react';
import { flexRender, RowData, Table } from '@tanstack/react-table';
import { getCellStyles } from '../Table/utils';
import DataListContainer from './DataListContainer';
import DataListRow from './DataListRow';
import DataListBodyCell from './DataListBodyCell';
import DataListHeaderCell from './DataListHeaderCell';
import { SpacingKeysType } from '../spacing';

export type ListProps = {
  table: Table<RowData>;
  hasCheckboxHeader?: boolean;
} & Pick<React.ComponentPropsWithoutRef<'table'>, 'id'>;

const List = forwardRef<HTMLTableElement, ListProps>(({ id, table, hasCheckboxHeader }, ref) => {
  // Check if table has any fill columns to apply appropriate styling
  const hasFillColumns = table.getAllColumns().some((col) => col.getColumnWidthOption() === 'fill');

  return (
    <DataListContainer id={id} ref={ref} data-has-fill-columns={hasFillColumns || undefined}>
      <thead>
        {table.getHeaderGroups().map((headerGroup, headerGroupIdx) => (
          <tr key={headerGroup.id}>
            {headerGroup.headers.map((header, headerIdx) => {
              const { column } = header;
              const isHeaderColumn = headerGroupIdx === 0 && headerIdx === 0;
              const isCheckboxHeader = hasCheckboxHeader && isHeaderColumn ? true : undefined;
              return (
                <DataListHeaderCell
                  style={getCellStyles({
                    colWidthOption: column.getColumnWidthOption(),
                    colWidth: column.getSize() as SpacingKeysType,
                    table,
                  })}
                  key={header.id}
                  colSpan={header.colSpan}
                  data-horizontal-align={column.getHorizontalAlign()}
                  data-checkbox-header={isCheckboxHeader}
                >
                  {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                </DataListHeaderCell>
              );
            })}
          </tr>
        ))}
      </thead>
      <tbody>
        {table.getRowModel().rows.map((row) => {
          return (
            <DataListRow key={row.id}>
              {row.getVisibleCells().map((cell) => {
                const { column } = cell;
                return (
                  <DataListBodyCell
                    data-col-width-option={column.getColumnWidthOption()}
                    style={getCellStyles({
                      colWidthOption: column.getColumnWidthOption(),
                      colWidth: column.getSize() as SpacingKeysType,
                      table,
                    })}
                    key={cell.id}
                    data-horizontal-align={column.getHorizontalAlign()}
                  >
                    {flexRender(column.columnDef.cell, cell.getContext())}
                  </DataListBodyCell>
                );
              })}
            </DataListRow>
          );
        })}
      </tbody>
    </DataListContainer>
  );
});

List.displayName = 'List';

export default List;

### Data List Introduction:

The two most important props that will be used to generate the list are the _data_ and _columns_.

- [data](https://tanstack.com/table/latest/docs/guide/data): The core data array you provide the list
- [columns](https://tanstack.com/table/latest/docs/guide/column-defs): The column definitions are the single most important part of building a list. They are responsible for building and formatting the underlying data model into what will be displayed in the list. Creating header groups, headers, etc.
  - Use the [createColumnHelper](https://tanstack.com/table/latest/docs/guide/column-defs#column-helpers) helper function which, when called with a row type, returns a utility for creating different column definition types with the highest type-safety possible. Here are examples of the declarations.

### How to type the data and columns:

```js
import { createColumnHelper } from '@axa-japan/design-system-react/DataTable';
// Example on how to type:
// type Person = {
//   firstName?: string;
//   lastName?: string;
//   age?: number;
//   visits?: number;
//   progress?: number;
//   status?: 'relationship' | 'complicated' | 'single';
//   rank?: number;
//   createdAt?: Date;
// };
// export const data: Person[] = [
// export const columns: DataListColumnDef<Person, string>[] = [
// // const columnHelper = createColumnHelper<Person>();

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    createdAt: new Date('2024-04-20T08:40:28'),
    status: 'relationship',
    rank: 64,
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    createdAt: new Date('2024-10-14T12:16:20'),
    status: 'single',
    rank: 35,
  },
];

// define columns with the helper
const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
];
```

### List cell contents:

Note that unlike the _DataTable_ component, the _DataList_ contains an optional _action area_ which is a cell containing up to 3 buttons. Please wrap the buttons in our _DataListActionContainer_ container for optimal responsiveness.

Here are the various ways that are recommended to render cell contents:

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';
import DataListActionContainer from '@axa-japan/design-system-react/DataList/DataListActionContainer';
import Badge from '@axa-japan/design-system-react/Badge';
import Button from '@axa-japan/design-system-react/Button';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Button',
    header: () => null,
    cell: () => (
      <DataListActionContainer>
        <Button>ボタン</Button>
        <Button>ボタン</Button>
        <Button>削除</Button>
      </DataListActionContainer>
    ),
  }),
];

<DataList data={data} columns={columns} />;
```

### List cell styles:

Here are the various ways you can style the cells. They can either be set on a _list_ or _column_ level, where _column_ has a higher priority:

- horizontalAlign: _left_ or _center_ or _right_. Defaults to _left_.

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';
import Badge from '@axa-japan/design-system-react/Badge';
import Button from '@axa-japan/design-system-react/Button';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.getValue()} variant="info" />,
    header: () => <span>Badge</span>,
    meta: { horizontalAlign: 'center' },
  }),
  columnHelper.accessor('status', {
    id: 'Button',
    cell: ({ getValue }) => <Button>{getValue()}</Button>,
    header: () => <span>Button</span>,
  }),
];

<DataList data={data} columns={columns} horizontalAlign="right" />;
```

### Column width:

There are 3 ways to set column width: _fit_, _fill_, _fixed_. Default is _fit_. Set via the _columnWidthOption_ prop which can be list or column level. If _fixed_ is chosen, the _width_ prop should be provided as well, which a width value must be picked from our _Spacing_ values.

Note: _fit_, which is the default should not be used for the compact DataList, please use _fixed_ or _fill_ instead.

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'fit',
    cell: (info) => info.getValue(),
    header: 'fit (default)',
  }),
  columnHelper.accessor('firstName', {
    id: 'fixed',
    cell: (info) => info.getValue(),
    header: 'fixed',
    meta: { columnWidthOption: 'fixed', width: 80 },
  }),
  columnHelper.accessor('firstName', {
    id: 'fill',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
];

<DataList data={data} columns={columns} />;
```

### Row selection (Checkbox):

_checkboxConfig_ prop can be added to the list to create a form / editable list. It'll automatically be inserted in the first column of the list. It contains:

- id - id of the form column.
- headerConfig - Callback prop to return the configuration for the _select all_ checkbox in the list header. _table_ which contains data on the table configuration is available as the callback prop.
- cellConfig - Callback prop to return the configuration for the checkboxes in each row. _row_ which contains data on the row configuration is available as the callback prop.

The whole component will be wrapped in our field wrapper. _label_ should be passed for accessibility reasons. Other props for our field wrapper such as _required_ are also available.

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
];

<DataList
  required
  fieldTitle="Form list"
  data={data}
  columns={columns}
  checkboxConfig={{
    id: 'checkbox-row-select-col',
    headerConfig: () => {
      return { label: '', value: 'data-list-checkbox-select-all' };
    },
    cellConfig: (row) => {
      return { label: row.id, value: row.id };
    },
  }}
/>;
```

### Row selection (Radio):

Radio can be added to the list to create a form / editable list via the _radioConfig_ prop. It'll automatically be inserted in the first column of the list. It contains:

- id - id of the form column.
- cellConfig - Callback prop to return the configuration for the radios in each row. _row_ which contains data on the row configuration is available as the callback prop.

The whole component will be wrapped in our field wrapper. _label_ should be passed for accessibility reasons. Other props for our field wrapper such as _required_ are also available.

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';
import RadioGroupField from '@axa-japan/design-system-react/RadioGroupField';

const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'radioExample',
    cell: (info) => info.getValue(),
    header: 'Radio example',
  }),
];

<DataList
  required
  fieldTitle="Form list"
  data={data}
  columns={columns}
  radioConfig={{
    id: 'radio-row-select-col',
    cellConfig: (row) => {
      return { label: '', value: row.id };
    },
  }}
/>;
```

### Managed Row Selection State & default selection:

Even though we will already manage the row selection state for you, it is usually more convenient to manage the state yourself in order to have easy access to the selected row ids that you can use to make API calls or other actions.

Pass the _rowSelection_ state and _onRowSelectionChange_ list option to hoist up the row selection state to your own scope.

By default, the row selection state uses the index of each row as the row identifiers. Row selection state can instead be tracked with a custom unique row id by passing in a custom _getRowId_ function to the the list.

Default selected rows can be done via managed state.

```js
import React, { useState } from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';

const [rowSelection, setRowSelection] = useState({ 1: true });
const data = [
  {
    firstName: 'Jon',
  },
  {
    firstName: 'Kyla',
  },
  {
    firstName: 'Britney',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
];

<DataList
  fieldTitle="Form list"
  data={data}
  columns={columns}
  state={{ rowSelection }}
  onRowSelectionChange={setRowSelection}
  checkboxConfig={{
    id: 'checkbox-row-select-col-controlled',
    headerConfig: () => {
      return { label: '', value: 'data-list-checkbox-select-all-controlled' };
    },
    cellConfig: (row) => {
      return { label: row.id, value: `${row.id}-controlled` };
    },
  }}
/>;
```

## Experimental Features

NOTE: The features below may be subject to breaking changes in the future.

### Compact DataList

By setting the _compact_ prop true, switch to a more compact version of the DataList which is more responsive on smaller viewports. Please use this variant when the DataList is too large for your screen size.

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';
import DataListActionContainer from '@axa-japan/design-system-react/DataList/DataListActionContainer';
import Badge from '@axa-japan/design-system-react/Badge';
import Button from '@axa-japan/design-system-react/Button';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge2',
    cell: (info) => <Badge text={info.column.id} variant="important" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge3',
    cell: (info) => <Badge text={info.column.id} variant="success" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Button',
    header: () => null,
    cell: () => (
      <DataListActionContainer>
        <Button>ボタン</Button>
        <Button>ボタン</Button>
        <Button>削除</Button>
      </DataListActionContainer>
    ),
  }),
];

<DataList data={data} columns={columns} compact />;
```

### Compact row selection (Checkbox)

```js
import React from 'react';
import DataList, { createColumnHelper } from '@axa-japan/design-system-react/DataList';
import Badge from '@axa-japan/design-system-react/Badge';

const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    status: 'relationship',
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    status: 'single',
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    status: 'complicated',
  },
];

const columnHelper = createColumnHelper();
const columns = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Badge2',
    cell: (info) => <Badge text={info.column.id} variant="important" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Badge3',
    cell: (info) => <Badge text={info.column.id} variant="success" />,
    header: () => <span>Badge</span>,
  }),
];

<DataList
  required
  fieldTitle="Form list"
  data={data}
  columns={columns}
  checkboxConfig={{
    id: 'checkbox-row-select-col-compact',
    headerConfig: () => {
      return { label: '全て選択', value: 'data-list-checkbox-select-all-compact' };
    },
    cellConfig: (row) => {
      return { label: '', value: `${row.id}-compact` };
    },
  }}
  compact
/>;
```

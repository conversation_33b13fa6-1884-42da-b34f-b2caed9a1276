import styled from 'styled-components';
import table from '../styles/table';

const DataListContainer = styled.table`
  ${table.container}

  border-spacing: 0;

  &[data-compact] {
    width: 100%;
    display: block;
  }

  /* When table has fill columns, use full width and fixed layout for proper column distribution */
  &[data-has-fill-columns] {
    width: 100%;
    min-width: 100%;
  }
`;

export default DataListContainer;

import React, { forwardRef, useId } from 'react';
import { getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { CustomFeatures } from '../Table/utils';
import FieldBase from '../FieldBase/FieldBase';
import checkHasError from '../utils/checkHasError';
import List from './List';
import CompactList from './CompactList';
import { DataListProps } from './types';
import getFormColumnConfig from '../Table/FormColumn';
import RadioRoot from '../Radio/RadioRoot';

const DataList = forwardRef<HTMLTableElement, DataListProps>(
  (
    {
      data,
      columns,
      fieldTitle,
      invalid,
      errorText,
      required = false,
      showRequiredIndicator,
      id,
      columnWidthOption,
      width,
      compact = false,
      horizontalAlign,
      state,
      checkboxConfig,
      radioConfig,
      getRowId,
      onRowSelectionChange,
    },
    ref,
  ) => {
    const hasError = checkHasError(invalid, errorText);
    const fallbackId = `data-list-id-${useId()}`;
    const controlledRowSelectionProps = state?.rowSelection
      ? {
          state: { rowSelection: state?.rowSelection },
          onRowSelectionChange,
        }
      : undefined;
    const formColumn = getFormColumnConfig({ checkboxConfig, radioConfig });
    const table = useReactTable({
      _features: [CustomFeatures], // pass our custom feature to the list to be instantiated upon creation
      columns: formColumn ? [formColumn, ...columns] : columns,
      data,
      columnWidthOption,
      width,
      horizontalAlign,
      enableMultiRowSelection: !radioConfig,
      getRowId,
      getCoreRowModel: getCoreRowModel(),
      enableSorting: false,
      enableColumnPinning: false,
      enableRowPinning: false,
      ...controlledRowSelectionProps,
    });
    const renderList = (hasCheckboxHeader?: boolean) => <List ref={ref} id={id} table={table} hasCheckboxHeader={hasCheckboxHeader} />;
    const renderCompactList = () => <CompactList ref={ref} id={id} table={table} />;
    if (checkboxConfig) {
      return (
        <FieldBase
          useFieldsetWrapper
          id={id || fallbackId}
          label={fieldTitle}
          showError={hasError}
          errorMessage={errorText}
          required={required}
          showRequiredIndicator={showRequiredIndicator}
        >
          {compact ? renderCompactList() : renderList(true)}
        </FieldBase>
      );
    }
    if (radioConfig) {
      return (
        <FieldBase
          useFieldsetWrapper
          id={id || fallbackId}
          label={fieldTitle}
          showError={hasError}
          errorMessage={errorText}
          required={required}
          showRequiredIndicator={showRequiredIndicator}
        >
          <RadioRoot id={`${fallbackId}:id`} name={`${fallbackId}:name`}>
            {compact ? renderCompactList() : renderList()}
          </RadioRoot>
        </FieldBase>
      );
    }
    return compact ? renderCompactList() : renderList();
  },
);

DataList.displayName = 'DataList';

export default React.memo(DataList);

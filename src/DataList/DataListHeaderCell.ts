import styled, { css } from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import table from '../styles/table';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

export const CellStyles = css`
  ${table.cell}

  padding: ${getSpacingVar(6)};
`;

const DataListHeaderCell = styled.th`
  ${CellStyles}

  padding-top: ${getSpacingVar(4)};
  padding-bottom: ${getSpacingVar(4)};
  white-space: nowrap;
  background-color: ${getColorVar('utilityBackgroundLight')};
  line-height: ${getTypographyVar('smLineHeight')};

  &[data-checkbox-header='true'] {
    font-weight: ${getTypographyVar('defaultFontWeight')};
  }

  &:first-child {
    border-top-left-radius: ${getRadiusVar('sm')};
    border-bottom-left-radius: ${getRadiusVar('sm')};
  }

  &:last-child {
    border-top-right-radius: ${getRadiusVar('sm')};
    border-bottom-right-radius: ${getRadiusVar('sm')};
  }
`;

export default DataListHeaderCell;

import styled, { css } from 'styled-components';
import { getSpacingVar } from '../spacing';
import DataListActionContainer from './DataListActionContainer';
import media from '../Breakpoints/Breakpoints';

export const DataListCompactCellStyles = css`
  display: flex;
  flex-direction: column;
  gap: ${getSpacingVar(1)};
  padding: ${getSpacingVar(2)} 0;
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: fit-content;

  & > * {
    width: fit-content;
  }

  ${media.extraSmallOnly} {
    &:has(${DataListActionContainer}) {
      width: 100%;

      & > * {
        width: 100%;
      }
    }
  }

  &[data-form-column] {
    padding-left: ${getSpacingVar(2)};
  }
`;

const DataListCompactCell = styled.div`
  ${DataListCompactCellStyles}
`;

export default DataListCompactCell;

import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { DataListHeaderStyles } from './DataListCompactHeader';
import { getTypographyVar } from '../typography';

const DataListCompactFormHeader = styled.th`
  ${DataListHeaderStyles}

  display: block;
  width: 100%;
  background-color: ${getColorVar('utilityBackgroundLight')};
  padding: ${getSpacingVar(1)} ${getSpacingVar(2)};
  margin-bottom: ${getSpacingVar(5)};
  font-weight: ${getTypographyVar('defaultFontWeight')};
`;

export default DataListCompactFormHeader;

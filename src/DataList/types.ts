/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AccessorFn,
  AccessorFnColumnDef,
  RowData,
  DisplayColumnDef,
  GroupColumnDef,
  IdentifiedColumnDef,
  AccessorColumnDef,
  DeepKeys,
  DeepValue,
  TableOptions,
  TableState,
  createColumnHelper as DataListCreateColumnHelper,
} from '@tanstack/react-table';
import { CommonColumnDefProps, CustomTableState, TableFieldProps } from '../Table/types';

type OptionalType = 'cell' | 'header' | 'meta';
type RequiredType = 'id';

type DataListDisplayColumnDef<TData extends RowData, TValue = unknown> = Pick<DisplayColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<DisplayColumnDef<TData, TValue>, RequiredType>>;

type DataListGroupColumnDef<TData extends RowData, TValue = unknown> = Pick<GroupColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<GroupColumnDef<TData, TValue>, RequiredType>> & {
    columns?: CustomColumnDef<TData, any>[];
  };

type DataListIdentifiedColumnDef<TData extends RowData, TValue = unknown> = Pick<IdentifiedColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<IdentifiedColumnDef<TData, TValue>, RequiredType>>;

type DataListAccessorColumnDef<TData extends RowData, TValue = unknown> = Pick<AccessorColumnDef<TData, TValue>, OptionalType> &
  Required<Pick<AccessorColumnDef<TData, TValue>, RequiredType>>;

// Create our own ColumnDef by removing unneeded props
type CustomColumnDef<TData extends RowData, TValue = unknown> =
  | DataListDisplayColumnDef<TData, TValue>
  | DataListGroupColumnDef<TData, TValue>
  | DataListAccessorColumnDef<TData, TValue>;

/** Column definitions type, which is responsible for:
   * 1. Building the underlying data model that will be used for everything including sorting, grouping, etc.
     2. Formatting the data model into what will be displayed in the table
     3. Creating header groups, headers
   */
type DataListColumnDef<TData extends RowData, TValue = unknown> = CustomColumnDef<TData, TValue> & CommonColumnDefProps<TData, TValue>;

type CustomColumnHelper<TData extends RowData> = {
  accessor: <
    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,
    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>
      ? TReturn
      : TAccessor extends DeepKeys<TData>
        ? DeepValue<TData, TAccessor>
        : never,
  >(
    accessor: TAccessor,
    column: TAccessor extends AccessorFn<TData> ? DataListDisplayColumnDef<TData, TValue> : DataListIdentifiedColumnDef<TData, TValue>,
  ) => TAccessor extends AccessorFn<TData> ? AccessorFnColumnDef<TData, TValue> : DataListAccessorColumnDef<TData, TValue>;
  display: (column: DataListDisplayColumnDef<TData>) => DataListDisplayColumnDef<TData>;
  group: (column: DataListGroupColumnDef<TData>) => DataListGroupColumnDef<TData>;
};

type DataListProps = {
  /** The array of column definitions to use for the list. */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: DataListColumnDef<any, any>[];
  /** @EXPERIMENTAL Note: Compact DataList is still experimental and may be subjected to breaking changes. Set this prop to true to switch to the compact DataList, which is more responsive. */
  compact?: boolean;
  /** The "state" option can be used to optionally _control_ part of the list state.
   * The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the list.  */
  state?: Pick<TableState, 'rowSelection'>;
} & TableFieldProps &
  Pick<React.ComponentPropsWithoutRef<'table'>, 'id'> &
  Pick<TableOptions<RowData>, 'data' | 'getRowId' | 'onRowSelectionChange'> &
  Pick<CustomTableState, 'horizontalAlign' | 'columnWidthOption' | 'width' | 'checkboxConfig' | 'radioConfig'>;

// Use declaration merging to add our new feature APIs and state types to TanStack Table's existing types.
declare module '@tanstack/react-table' {
  // Override createColumnHelper's return value with our custom ColumnHelper
  function createColumnHelper<TData extends RowData>(): CustomColumnHelper<TData>;
}

// Export for the consumer to use
export { DataListColumnDef, CustomColumnHelper as DataListColumnHelper, DataListProps, DataListCreateColumnHelper as createColumnHelper };

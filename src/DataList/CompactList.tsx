import React, { forwardRef } from 'react';
import { flexRender } from '@tanstack/react-table';
import DataListCompactRow from './DataListCompactRow';
import DataListCompactCell from './DataListCompactCell';
import DataListCompactHeader from './DataListCompactHeader';
import DataListCompactFormHeader from './DataListCompactFormHeader';
import { ListProps } from './List';
import { getCellStyles } from '../Table/utils';
import { SpacingKeysType } from '../spacing';
import DataListContainer from './DataListContainer';
import DataListCompactRowContent from './DataListCompactRowContent';
import DataListCompactFormCell from './DataListCompactFormCell';

const CompactList = forwardRef<HTMLTableElement, ListProps>(({ id, table }, ref) => {
  return (
    <DataListContainer ref={ref} id={id} data-compact>
      {table.getRowModel().rows.map((row) => {
        const headers = table.getFlatHeaders();
        let formHeader;
        let formColumn;
        const rowContent = row.getVisibleCells().map((cell, idx) => {
          const { column } = cell;
          const header = headers[idx];
          const colWidthOption = column.getColumnWidthOption();
          const colWidth = column.getSize() as SpacingKeysType;
          const cellType = 'div';
          // We check if the column defined is a checkbox or radio column, we extract that
          // column and its header so that we can position it separately
          const isFormColumn = column.columnDef.cell?.toString().includes('getToggleSelectedHandler');
          if (isFormColumn) {
            const hasFormHeader = header.column.columnDef.header?.toString().includes('getToggleAllRowsSelectedHandler');
            formHeader =
              header.isPlaceholder || !hasFormHeader ? null : (
                <DataListCompactFormHeader>{flexRender(header.column.columnDef.header, header.getContext())}</DataListCompactFormHeader>
              );
            formColumn = (
              <DataListCompactFormCell
                style={getCellStyles({
                  colWidthOption,
                  colWidth,
                  cellType,
                  table,
                })}
                key={cell.id}
                data-form-column={true}
              >
                {flexRender(column.columnDef.cell, cell.getContext())}
              </DataListCompactFormCell>
            );
            return null;
          }
          return (
            <DataListCompactCell
              role="cell"
              data-col-width-option={colWidthOption}
              style={getCellStyles({ colWidthOption, colWidth, cellType, table })}
              key={cell.id}
            >
              {header.isPlaceholder ? null : (
                <DataListCompactHeader role="columnheader">{flexRender(header.column.columnDef.header, header.getContext())}</DataListCompactHeader>
              )}
              {flexRender(column.columnDef.cell, cell.getContext())}
            </DataListCompactCell>
          );
        });
        return (
          <React.Fragment key={row.id}>
            {row.index === 0 && formHeader ? (
              <thead style={{ display: 'block' }}>
                <tr style={{ display: 'block' }}>{formHeader}</tr>
              </thead>
            ) : null}
            <tbody style={{ display: 'block', width: '100%' }}>
              <DataListCompactRow role="rowgroup">
                {formColumn}
                <DataListCompactRowContent>{rowContent}</DataListCompactRowContent>
              </DataListCompactRow>
            </tbody>
          </React.Fragment>
        );
      })}
    </DataListContainer>
  );
});

CompactList.displayName = 'CompactList';

export default CompactList;

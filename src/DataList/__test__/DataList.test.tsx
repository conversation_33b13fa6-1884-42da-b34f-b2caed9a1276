import React, { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import DataList from '../DataList';
import { data, compactTableColumns, compactCheckboxColumns, radioColumns, multipleFillColumns } from './testData';
import { ColumnWidthOptionState, HorizontalAlignState } from '../../Table/types';

describe('DataList', () => {
  beforeAll(() => {
    (window as any).PointerEvent = MouseEvent;
  });
  it.each([false, true])('renders without crashing with with table variant compact - %s', (prop) => {
    const { getAllByRole, container } = render(<DataList data={data} columns={compactTableColumns} compact={prop} />);
    const columnHeaders = getAllByRole('columnheader');
    expect(columnHeaders[0].textContent).toEqual('default');
    const cells = getAllByRole('cell');
    if (prop === false) {
      expect(cells.length).toEqual(15);
    } else {
      expect(cells.length).toEqual(18);
    }
    expect(container).toMatchSnapshot();
  });
  it.each(['left', 'center', 'right'] as HorizontalAlignState[])('renders with cell horizontalAlign - %s ', (prop) => {
    const { container } = render(<DataList data={data} columns={compactTableColumns} horizontalAlign={prop} />);
    expect(container).toMatchSnapshot();
  });
  it.each(['fit', 'fixed', 'fill'] as ColumnWidthOptionState[])('renders with cell columnWidthOption - %s ', (prop) => {
    const { container } = render(
      <DataList data={data} columns={compactTableColumns} columnWidthOption={prop} width={prop === 'fixed' ? 64 : undefined} />,
    );
    expect(container).toMatchSnapshot();
  });
  it.each([false, true])(
    'renders with checkbox column and controlled state, and check header checkbox function with table variant compact - %s',
    async (prop) => {
      const Wrapper = () => {
        const [rowSelection, setRowSelection] = useState({});
        return (
          <DataList
            required
            fieldTitle="Form list"
            data={data}
            columns={compactCheckboxColumns}
            state={{ rowSelection }}
            onRowSelectionChange={setRowSelection}
            compact={prop}
            checkboxConfig={{
              id: 'checkbox-row-select-col-compact',
              headerConfig: () => {
                return { label: '全て選択', value: 'data-list-checkbox-select-all-compact' };
              },
              cellConfig: (row) => {
                return { label: '', value: `${row.id}-compact` };
              },
            }}
          />
        );
      };
      const { container, queryAllByText } = render(<Wrapper />);
      const label = queryAllByText('Form list');
      expect(label).toHaveLength(1);
      const required = queryAllByText('必須');
      expect(required).toHaveLength(1);
      let checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
      expect(checkboxCells.length).toEqual(4);
      await waitFor(() => {
        checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('unchecked'));
      });
      // check that clicking the header checkbox checks all the checkboxes
      await userEvent.click(checkboxCells[0]);
      checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
      await waitFor(() => {
        checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('checked'));
      });
      // click the first checkbox, and expect it to become unchecked
      await userEvent.click(checkboxCells[1]);
      checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
      await waitFor(() => {
        expect(checkboxCells[1].getAttribute('data-state')).toEqual('unchecked');
      });
      // click the header checkbox again, and expect all checkboxes to be checked
      await userEvent.click(checkboxCells[0]);
      checkboxCells = container.querySelectorAll(`label[data-scope="checkbox"]`);
      await waitFor(() => {
        checkboxCells.forEach((cell) => expect(cell.getAttribute('data-state')).toEqual('checked'));
      });
      expect(container).toMatchSnapshot();
    },
  );
  it.each([false, true])('renders with radio column', (prop) => {
    const { container } = render(
      <DataList
        required
        fieldTitle="Form list"
        data={data}
        columns={radioColumns}
        radioConfig={{
          id: 'radio-row-select-col',
          cellConfig: (row) => {
            return { label: '', value: row.id };
          },
        }}
        compact={prop}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('renders with multiple fill columns and distributes width equally', () => {
    const { container } = render(<DataList data={data} columns={multipleFillColumns} />);

    // Check that the table has the data-has-fill-columns attribute
    const table = container.querySelector('table');
    expect(table).toHaveAttribute('data-has-fill-columns');

    // Check that fill columns have percentage width styles
    const fillCells = container.querySelectorAll('[data-col-width-option="fill"]');
    expect(fillCells.length).toBeGreaterThan(0);

    // Each fill cell should have a width style applied
    fillCells.forEach((cell) => {
      const { style } = cell as HTMLElement;
      expect(style.width).toBeTruthy();
    });

    expect(container).toMatchSnapshot();
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DataList > renders with cell columnWidthOption - fill  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
    data-has-fill-columns="true"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 20%;"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 20%;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with cell columnWidthOption - fit  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with cell columnWidthOption - fixed  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 150px; min-width: 150px; max-width: 150px;"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with cell horizontalAlign - center  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="center"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="center"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="center"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="center"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="center"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="center"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with cell horizontalAlign - left  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with cell horizontalAlign - right  1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="right"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="right"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="right"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="right"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="right"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="right"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with checkbox column and controlled state, and check header checkbox function with table variant compact - false 1`] = `
.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c5[data-compact] {
  width: 100%;
  display: block;
}

.c5[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c13 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c6 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c6 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c6[data-horizontal-align='center'] {
  text-align: center;
}

.c6[data-horizontal-align='right'] {
  text-align: right;
}

.c6[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c6:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c6:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c14 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c14 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c14[data-horizontal-align='center'] {
  text-align: center;
}

.c14[data-horizontal-align='right'] {
  text-align: right;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c8[data-disabled] {
  cursor: not-allowed;
}

.c8[data-disabled]:hover {
  background-color: initial;
}

.c8:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c8[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c8[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c10[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c10[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c10[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c10[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c10[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c10[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover > .c9[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c12 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c12[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r9:"
    >
      Form list
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <table
        class="c5"
      >
        <thead>
          <tr>
            <th
              class="c6"
              colspan="1"
              data-checkbox-header="true"
              data-horizontal-align="left"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::rm::input"
                id="checkbox::rm:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rm::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <span
                  class="c12"
                  data-part="label"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rm::label"
                >
                  全て選択
                </span>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::rm::label"
                  checked=""
                  id="checkbox::rm::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="data-list-checkbox-select-all-compact"
                />
              </label>
            </th>
            <th
              class="c6"
              colspan="1"
              data-horizontal-align="left"
            >
              Checkbox example
            </th>
            <th
              class="c6"
              colspan="1"
              data-horizontal-align="left"
            >
              <span>
                Badge
              </span>
            </th>
            <th
              class="c6"
              colspan="1"
              data-horizontal-align="left"
            >
              <span>
                Badge
              </span>
            </th>
            <th
              class="c6"
              colspan="1"
              data-horizontal-align="left"
            >
              <span>
                Badge
              </span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            class="c13"
          >
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::rn::input"
                id="checkbox::rn:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rn::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::rn::label"
                  checked=""
                  id="checkbox::rn::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="0-compact"
                />
              </label>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              Jon
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge2
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge3
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="c13"
          >
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::ro::input"
                id="checkbox::ro:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::ro::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::ro::label"
                  checked=""
                  id="checkbox::ro::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="1-compact"
                />
              </label>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              Kyla
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge2
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge3
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="c13"
          >
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::rp::input"
                id="checkbox::rp:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::rp::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::rp::label"
                  checked=""
                  id="checkbox::rp::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="2-compact"
                />
              </label>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              Britney
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge2
                </span>
              </div>
            </td>
            <td
              class="c14"
              data-col-width-option="fit"
              data-horizontal-align="left"
            >
              <div
                class="c2"
                style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
              >
                <span
                  class="c3"
                >
                  Badge3
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </fieldset>
</div>
`;

exports[`DataList > renders with checkbox column and controlled state, and check header checkbox function with table variant compact - true 1`] = `
.c11 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c5 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c5[data-compact] {
  width: 100%;
  display: block;
}

.c5[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  border-bottom: var(--ajds-spacing-px) solid var(--ajds-color-utility-stroke-light);
  padding-bottom: var(--ajds-spacing-2);
  width: 100%;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding: var(--ajds-spacing-2) 0;
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.c16 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c16[data-form-column] {
  padding-left: var(--ajds-spacing-2);
}

.c17 {
  text-align: left;
  vertical-align: middle;
  line-height: var(--ajds-line-height);
  font-weight: var(--ajds-font-weight-bold);
  width: 100%;
}

.c6 {
  text-align: left;
  vertical-align: middle;
  line-height: var(--ajds-line-height);
  font-weight: var(--ajds-font-weight-bold);
  display: block;
  width: 100%;
  background-color: var(--ajds-color-utility-background-light);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-5);
  font-weight: var(--ajds-font-weight-default);
}

.c15 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-2) var(--ajds-spacing-6);
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding: var(--ajds-spacing-2) 0;
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.c14 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c14[data-form-column] {
  padding-left: var(--ajds-spacing-2);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c8:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c8[data-disabled] {
  cursor: not-allowed;
}

.c8[data-disabled]:hover {
  background-color: initial;
}

.c8:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c8[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c8[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  border-radius: var(--ajds-radius-xs);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  color: var(--ajds-color-utility-stroke-white);
}

.c10[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  background-color: var(--ajds-color-interactive-active-white);
}

.c10[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  background-color: var(--ajds-color-interactive-active-primary);
}

.c10[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10[data-invalid][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-active-white);
}

.c10[data-invalid][data-state='checked'] {
  background-color: var(--ajds-color-status-danger);
}

.c10[data-disabled] {
  color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c10[data-disabled][data-state='unchecked'] {
  background-color: var(--ajds-color-interactive-disabled-light);
}

.c10[data-disabled][data-state='checked'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover > .c9[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  background-color: var(--ajds-color-interactive-hover-primary);
}

.c12 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-wrap: anywhere;
}

.c12[data-invalid] {
  color: var(--ajds-color-status-danger);
}

.c12[data-disabled] {
  color: var(--ajds-color-interactive-disabled-dark);
}

@media (max-width:599px) {
  .c16:has(.sc-dLMFU) {
    width: 100%;
  }

  .c16:has(.sc-dLMFU) > * {
    width: 100%;
  }
}

@media (max-width:599px) {
  .c14:has(.sc-dLMFU) {
    width: 100%;
  }

  .c14:has(.sc-dLMFU) > * {
    width: 100%;
  }
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:rr:"
    >
      Form list
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <table
        class="c5"
        data-compact="true"
      >
        <thead
          style="display: block;"
        >
          <tr
            style="display: block;"
          >
            <th
              class="c6"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::r18::input"
                id="checkbox::r18:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::r18::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <span
                  class="c12"
                  data-part="label"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::r18::label"
                >
                  全て選択
                </span>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::r18::label"
                  checked=""
                  id="checkbox::r18::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="data-list-checkbox-select-all-compact"
                />
              </label>
            </th>
          </tr>
        </thead>
        <tbody
          style="display: block; width: 100%;"
        >
          <tr
            class="c13"
            role="rowgroup"
          >
            <td
              class="c14"
              data-form-column="true"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::r19::input"
                id="checkbox::r19:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::r19::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::r19::label"
                  checked=""
                  id="checkbox::r19::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="0-compact"
                />
              </label>
            </td>
            <td
              class="c15"
            >
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  Checkbox example
                </div>
                Jon
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge2
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge3
                  </span>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody
          style="display: block; width: 100%;"
        >
          <tr
            class="c13"
            role="rowgroup"
          >
            <td
              class="c14"
              data-form-column="true"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::r1a::input"
                id="checkbox::r1a:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::r1a::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::r1a::label"
                  checked=""
                  id="checkbox::r1a::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="1-compact"
                />
              </label>
            </td>
            <td
              class="c15"
            >
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  Checkbox example
                </div>
                Kyla
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge2
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge3
                  </span>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody
          style="display: block; width: 100%;"
        >
          <tr
            class="c13"
            role="rowgroup"
          >
            <td
              class="c14"
              data-form-column="true"
            >
              <label
                aria-checked="true"
                class="c7 c8"
                data-part="root"
                data-scope="checkbox"
                data-state="checked"
                dir="ltr"
                for="checkbox::r1b::input"
                id="checkbox::r1b:"
              >
                <div
                  aria-hidden="true"
                  class="c9 c10"
                  data-part="control"
                  data-scope="checkbox"
                  data-state="checked"
                  dir="ltr"
                  id="checkbox::r1b::control"
                >
                  <svg
                    class="c11"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
                    />
                  </svg>
                </div>
                <input
                  aria-invalid="false"
                  aria-labelledby="checkbox::r1b::label"
                  checked=""
                  id="checkbox::r1b::input"
                  style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                  type="checkbox"
                  value="2-compact"
                />
              </label>
            </td>
            <td
              class="c15"
            >
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  Checkbox example
                </div>
                Britney
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge2
                  </span>
                </div>
              </div>
              <div
                class="c16"
                data-col-width-option="fit"
                role="cell"
              >
                <div
                  class="c17"
                  role="columnheader"
                >
                  <span>
                    Badge
                  </span>
                </div>
                <div
                  class="c2"
                  style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
                >
                  <span
                    class="c3"
                  >
                    Badge3
                  </span>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </fieldset>
</div>
`;

exports[`DataList > renders with multiple fill columns and distributes width equally 1`] = `
.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

<div>
  <table
    class="c0"
    data-has-fill-columns="true"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          fill
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 320px; min-width: 320px; max-width: 320px;"
        >
          fixed
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          fill
        </th>
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 320px; min-width: 320px; max-width: 320px;"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Jon
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 320px; min-width: 320px; max-width: 320px;"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Kyla
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fixed"
          data-horizontal-align="left"
          style="width: 320px; min-width: 320px; max-width: 320px;"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fill"
          data-horizontal-align="left"
          style="width: 50%;"
        >
          Britney
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders with radio column 1`] = `
.c14 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c6 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c6[data-compact] {
  width: 100%;
  display: block;
}

.c6[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c8 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c7 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c7 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c7[data-horizontal-align='center'] {
  text-align: center;
}

.c7[data-horizontal-align='right'] {
  text-align: right;
}

.c7[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c7:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c7:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c9 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c9 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c9[data-horizontal-align='center'] {
  text-align: center;
}

.c9[data-horizontal-align='right'] {
  text-align: right;
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
}

.c11:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c11[data-disabled] {
  cursor: not-allowed;
}

.c11[data-disabled]:hover {
  background-color: initial;
}

.c11:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c11[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c11[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c13[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c13[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c13[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c13[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c13[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c13[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c13[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c13[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c10:hover > .c12[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c5[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c5[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r1d:"
    >
      Form list
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:data-list-id-:r1c::id:label"
        aria-orientation="vertical"
        class="c5"
        data-orientation="vertical"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:data-list-id-:r1c::id"
        role="radiogroup"
        style="position: relative;"
      >
        <table
          class="c6"
        >
          <thead>
            <tr>
              <th
                class="c7"
                colspan="1"
                data-horizontal-align="left"
              />
              <th
                class="c7"
                colspan="1"
                data-horizontal-align="left"
              >
                Radio example
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c8"
            >
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                <label
                  class="c10 c11"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-list-id-:r1c::id:radio:input:0"
                  id="radio-group:data-list-id-:r1c::id:radio:0"
                >
                  <div
                    aria-hidden="true"
                    class="c12 c13"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-list-id-:r1c::id:radio:control:0"
                  >
                    <svg
                      class="c14"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-list-id-:r1c::id"
                    id="radio-group:data-list-id-:r1c::id:radio:input:0"
                    name="data-list-id-:r1c::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="0"
                  />
                </label>
              </td>
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                Jon
              </td>
            </tr>
            <tr
              class="c8"
            >
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                <label
                  class="c10 c11"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-list-id-:r1c::id:radio:input:1"
                  id="radio-group:data-list-id-:r1c::id:radio:1"
                >
                  <div
                    aria-hidden="true"
                    class="c12 c13"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-list-id-:r1c::id:radio:control:1"
                  >
                    <svg
                      class="c14"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-list-id-:r1c::id"
                    id="radio-group:data-list-id-:r1c::id:radio:input:1"
                    name="data-list-id-:r1c::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="1"
                  />
                </label>
              </td>
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                Kyla
              </td>
            </tr>
            <tr
              class="c8"
            >
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                <label
                  class="c10 c11"
                  data-orientation="vertical"
                  data-part="item"
                  data-scope="radio-group"
                  data-state="unchecked"
                  dir="ltr"
                  for="radio-group:data-list-id-:r1c::id:radio:input:2"
                  id="radio-group:data-list-id-:r1c::id:radio:2"
                >
                  <div
                    aria-hidden="true"
                    class="c12 c13"
                    data-orientation="vertical"
                    data-part="item-control"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    id="radio-group:data-list-id-:r1c::id:radio:control:2"
                  >
                    <svg
                      class="c14"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 12 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        cx="6"
                        cy="6"
                        r="6"
                      />
                    </svg>
                  </div>
                  <input
                    data-ownedby="radio-group:data-list-id-:r1c::id"
                    id="radio-group:data-list-id-:r1c::id:radio:input:2"
                    name="data-list-id-:r1c::name"
                    style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                    type="radio"
                    value="2"
                  />
                </label>
              </td>
              <td
                class="c9"
                data-col-width-option="fit"
                data-horizontal-align="left"
              >
                Britney
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`DataList > renders with radio column 2`] = `
.c15 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c3 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
  margin-bottom: var(--ajds-spacing-1);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c4 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c4 input,
.c4 select,
.c4 button,
.c4 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c4 input {
  overflow: visible;
}

.c4 select {
  text-transform: none;
}

.c4 textarea {
  overflow: auto;
}

.c0 {
  width: 100%;
  min-inline-size: initial;
  margin-inline: 0;
  border-width: 0;
  border-style: none;
  border-color: initial;
  border-image: initial;
  padding-block: 0;
  padding-inline: 0;
  margin: var(--ajds-FieldsetBase-margin-top,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-right,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-bottom,var(--ajds-FieldsetBase-margin,0)) var(--ajds-FieldsetBase-margin-left,var(--ajds-FieldsetBase-margin,0));
}

.c6 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c6[data-compact] {
  width: 100%;
  display: block;
}

.c6[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  border-bottom: var(--ajds-spacing-px) solid var(--ajds-color-utility-stroke-light);
  padding-bottom: var(--ajds-spacing-2);
  width: 100%;
}

.c9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding: var(--ajds-spacing-2) 0;
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.c9 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c9[data-form-column] {
  padding-left: var(--ajds-spacing-2);
}

.c10 {
  text-align: left;
  vertical-align: middle;
  line-height: var(--ajds-line-height);
  font-weight: var(--ajds-font-weight-bold);
  width: 100%;
}

.c8 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-2) var(--ajds-spacing-6);
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  min-height: var(--ajds-spacing-12);
  max-height: var(--ajds-spacing-12);
}

.c12:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c12[data-disabled] {
  cursor: not-allowed;
}

.c12[data-disabled]:hover {
  background-color: initial;
}

.c12:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c12[data-disabled]:has(:focus-visible) {
  box-shadow: initial;
}

.c12[data-invalid]:has(:focus-visible) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: var(--ajds-radius-full);
  padding: var(--ajds-spacing-0-5);
  -webkit-transition: 0.2s all ease;
  transition: 0.2s all ease;
  background-color: var(--ajds-color-utility-background-white);
}

.c14[data-state='unchecked'] {
  border: 2px solid var(--ajds-color-utility-stroke-dark);
  color: var(--ajds-color-interactive-active-white);
}

.c14[data-state='checked'] {
  border: 2px solid var(--ajds-color-interactive-active-primary);
  color: var(--ajds-color-interactive-active-primary);
}

.c14[data-invalid] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c14[data-invalid][data-state='unchecked'] {
  color: var(--ajds-color-interactive-active-white);
}

.c14[data-invalid][data-state='checked'] {
  color: var(--ajds-color-status-danger);
}

.c14[data-disabled] {
  background-color: var(--ajds-color-interactive-disabled-light);
  border: 2px solid var(--ajds-color-interactive-disabled-dark);
}

.c14[data-disabled][data-state='unchecked'] {
  color: var(--ajds-color-interactive-disabled-light);
}

.c14[data-disabled][data-state='checked'] {
  color: var(--ajds-color-interactive-disabled-dark);
}

.c11:hover > .c13[data-state='checked']:not([data-invalid],[data-disabled]) {
  border: 2px solid var(--ajds-color-interactive-hover-primary);
  color: var(--ajds-color-interactive-hover-primary);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  row-gap: var(--ajds-spacing-2);
}

.c5[data-orientation='vertical'] {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c5[data-orientation='horizontal'] {
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
}

@media (max-width:599px) {
  .c9:has(.sc-dLMFU) {
    width: 100%;
  }

  .c9:has(.sc-dLMFU) > * {
    width: 100%;
  }
}

<div>
  <fieldset
    class="c0"
  >
    <legend
      class="c1"
      id="field-legend-:r1g:"
    >
      Form list
      <div
        class="c2"
        style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
      >
        <span
          class="c3"
        >
          必須
        </span>
      </div>
    </legend>
    <div
      class="c4 field-input-wrap"
    >
      <div
        aria-labelledby="radio-group:data-list-id-:r1f::id:label"
        aria-orientation="vertical"
        class="c5"
        data-orientation="vertical"
        data-part="root"
        data-scope="radio-group"
        dir="ltr"
        id="radio-group:data-list-id-:r1f::id"
        role="radiogroup"
        style="position: relative;"
      >
        <table
          class="c6"
          data-compact="true"
        >
          <tbody
            style="display: block; width: 100%;"
          >
            <tr
              class="c7"
              role="rowgroup"
            >
              <td
                class="c8"
              >
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  />
                  <label
                    class="c11 c12"
                    data-orientation="vertical"
                    data-part="item"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    for="radio-group:data-list-id-:r1f::id:radio:input:0"
                    id="radio-group:data-list-id-:r1f::id:radio:0"
                  >
                    <div
                      aria-hidden="true"
                      class="c13 c14"
                      data-orientation="vertical"
                      data-part="item-control"
                      data-scope="radio-group"
                      data-state="unchecked"
                      dir="ltr"
                      id="radio-group:data-list-id-:r1f::id:radio:control:0"
                    >
                      <svg
                        class="c15"
                        fill="none"
                        focusable="false"
                        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                        viewBox="0 0 12 12"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle
                          cx="6"
                          cy="6"
                          r="6"
                        />
                      </svg>
                    </div>
                    <input
                      data-ownedby="radio-group:data-list-id-:r1f::id"
                      id="radio-group:data-list-id-:r1f::id:radio:input:0"
                      name="data-list-id-:r1f::name"
                      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                      type="radio"
                      value="0"
                    />
                  </label>
                </div>
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  >
                    Radio example
                  </div>
                  Jon
                </div>
              </td>
            </tr>
          </tbody>
          <tbody
            style="display: block; width: 100%;"
          >
            <tr
              class="c7"
              role="rowgroup"
            >
              <td
                class="c8"
              >
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  />
                  <label
                    class="c11 c12"
                    data-orientation="vertical"
                    data-part="item"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    for="radio-group:data-list-id-:r1f::id:radio:input:1"
                    id="radio-group:data-list-id-:r1f::id:radio:1"
                  >
                    <div
                      aria-hidden="true"
                      class="c13 c14"
                      data-orientation="vertical"
                      data-part="item-control"
                      data-scope="radio-group"
                      data-state="unchecked"
                      dir="ltr"
                      id="radio-group:data-list-id-:r1f::id:radio:control:1"
                    >
                      <svg
                        class="c15"
                        fill="none"
                        focusable="false"
                        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                        viewBox="0 0 12 12"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle
                          cx="6"
                          cy="6"
                          r="6"
                        />
                      </svg>
                    </div>
                    <input
                      data-ownedby="radio-group:data-list-id-:r1f::id"
                      id="radio-group:data-list-id-:r1f::id:radio:input:1"
                      name="data-list-id-:r1f::name"
                      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                      type="radio"
                      value="1"
                    />
                  </label>
                </div>
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  >
                    Radio example
                  </div>
                  Kyla
                </div>
              </td>
            </tr>
          </tbody>
          <tbody
            style="display: block; width: 100%;"
          >
            <tr
              class="c7"
              role="rowgroup"
            >
              <td
                class="c8"
              >
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  />
                  <label
                    class="c11 c12"
                    data-orientation="vertical"
                    data-part="item"
                    data-scope="radio-group"
                    data-state="unchecked"
                    dir="ltr"
                    for="radio-group:data-list-id-:r1f::id:radio:input:2"
                    id="radio-group:data-list-id-:r1f::id:radio:2"
                  >
                    <div
                      aria-hidden="true"
                      class="c13 c14"
                      data-orientation="vertical"
                      data-part="item-control"
                      data-scope="radio-group"
                      data-state="unchecked"
                      dir="ltr"
                      id="radio-group:data-list-id-:r1f::id:radio:control:2"
                    >
                      <svg
                        class="c15"
                        fill="none"
                        focusable="false"
                        style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                        viewBox="0 0 12 12"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle
                          cx="6"
                          cy="6"
                          r="6"
                        />
                      </svg>
                    </div>
                    <input
                      data-ownedby="radio-group:data-list-id-:r1f::id"
                      id="radio-group:data-list-id-:r1f::id:radio:input:2"
                      name="data-list-id-:r1f::name"
                      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; word-wrap: normal;"
                      type="radio"
                      value="2"
                    />
                  </label>
                </div>
                <div
                  class="c9"
                  data-col-width-option="fit"
                  role="cell"
                >
                  <div
                    class="c10"
                    role="columnheader"
                  >
                    Radio example
                  </div>
                  Britney
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </fieldset>
</div>
`;

exports[`DataList > renders without crashing with with table variant compact - false 1`] = `
.c4 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c5 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c2 td {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
}

.c1 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  padding-top: var(--ajds-spacing-4);
  padding-bottom: var(--ajds-spacing-4);
  white-space: nowrap;
  background-color: var(--ajds-color-utility-background-light);
  line-height: var(--ajds-line-height-sm);
}

.c1 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c1[data-horizontal-align='center'] {
  text-align: center;
}

.c1[data-horizontal-align='right'] {
  text-align: right;
}

.c1[data-checkbox-header='true'] {
  font-weight: var(--ajds-font-weight-default);
}

.c1:first-child {
  border-top-left-radius: var(--ajds-radius-sm);
  border-bottom-left-radius: var(--ajds-radius-sm);
}

.c1:last-child {
  border-top-right-radius: var(--ajds-radius-sm);
  border-bottom-right-radius: var(--ajds-radius-sm);
}

.c3 {
  text-align: left;
  vertical-align: middle;
  padding: var(--ajds-spacing-6);
  overflow-wrap: anywhere;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-horizontal-align='center'] {
  text-align: center;
}

.c3[data-horizontal-align='right'] {
  text-align: right;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c6 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
  >
    <thead>
      <tr>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          default
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        >
          <span>
            Badge
          </span>
        </th>
        <th
          class="c1"
          colspan="1"
          data-horizontal-align="left"
        />
      </tr>
    </thead>
    <tbody>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Jon
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Kyla
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
      <tr
        class="c2"
      >
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          Britney
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge2
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c4"
            style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
          >
            <span
              class="c5"
            >
              Badge3
            </span>
          </div>
        </td>
        <td
          class="c3"
          data-col-width-option="fit"
          data-horizontal-align="left"
        >
          <div
            class="c6"
          >
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="ボタン"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                ボタン
              </span>
            </button>
            <button
              aria-label="削除"
              class="c7"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c8"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                削除
              </span>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`DataList > renders without crashing with with table variant compact - true 1`] = `
.c5 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c6 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

.c0 {
  overflow: auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
  border-spacing: 0;
}

.c0[data-compact] {
  width: 100%;
  display: block;
}

.c0[data-has-fill-columns] {
  width: 100%;
  min-width: 100%;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  border-bottom: var(--ajds-spacing-px) solid var(--ajds-color-utility-stroke-light);
  padding-bottom: var(--ajds-spacing-2);
  width: 100%;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding: var(--ajds-spacing-2) 0;
  white-space: break-spaces;
  overflow-wrap: anywhere;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.c3 > * {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.c3[data-form-column] {
  padding-left: var(--ajds-spacing-2);
}

.c4 {
  text-align: left;
  vertical-align: middle;
  line-height: var(--ajds-line-height);
  font-weight: var(--ajds-font-weight-bold);
  width: 100%;
}

.c2 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-2) var(--ajds-spacing-6);
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c9 svg {
  color: currentColor;
}

.c9:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c9:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c9[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c9:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c9[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c9:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c10 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c10[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (max-width:599px) {
  .c8 {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

@media (max-width:599px) {
  .c3:has(.c7) {
    width: 100%;
  }

  .c3:has(.c7) > * {
    width: 100%;
  }
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <table
    class="c0"
    data-compact="true"
  >
    <tbody
      style="display: block; width: 100%;"
    >
      <tr
        class="c1"
        role="rowgroup"
      >
        <td
          class="c2"
        >
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              default
            </div>
            Jon
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge2
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge3
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            />
            <div
              class="c7 c8"
            >
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="削除"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  削除
                </span>
              </button>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
    <tbody
      style="display: block; width: 100%;"
    >
      <tr
        class="c1"
        role="rowgroup"
      >
        <td
          class="c2"
        >
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              default
            </div>
            Kyla
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge2
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge3
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            />
            <div
              class="c7 c8"
            >
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="削除"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  削除
                </span>
              </button>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
    <tbody
      style="display: block; width: 100%;"
    >
      <tr
        class="c1"
        role="rowgroup"
      >
        <td
          class="c2"
        >
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              default
            </div>
            Britney
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge2
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            >
              <span>
                Badge
              </span>
            </div>
            <div
              class="c5"
              style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
            >
              <span
                class="c6"
              >
                Badge3
              </span>
            </div>
          </div>
          <div
            class="c3"
            data-col-width-option="fit"
            role="cell"
          >
            <div
              class="c4"
              role="columnheader"
            />
            <div
              class="c7 c8"
            >
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="ボタン"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  ボタン
                </span>
              </button>
              <button
                aria-label="削除"
                class="c9"
                style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
                type="button"
              >
                <span
                  class="c10"
                  data-text-variant="false"
                  style="--ajds-ButtonText-text-decoration: none;"
                >
                  削除
                </span>
              </button>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

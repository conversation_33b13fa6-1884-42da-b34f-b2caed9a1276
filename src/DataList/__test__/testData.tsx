import React from 'react';
import Badge from '../../Badge';
import Button from '../../Button';
import DataListActionContainer from '../DataListActionContainer';
import { createColumnHelper, type DataListColumnDef } from '../types';

export type Person = {
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  status: string;
  progress: number;
};

const columnHelper = createColumnHelper<Person>();

export const data = [
  {
    firstName: 'Jon',
    lastName: 'Schaefer',
    age: 15,
    visits: 156,
    progress: 2,
    createdAt: new Date('2024-04-20T08:40:28'),
    status: 'relationship',
    rank: 64,
  },
  {
    firstName: 'Kyla',
    lastName: 'Hayes-Deckow',
    age: 0,
    progress: 28,
    createdAt: new Date('2024-10-14T12:16:20'),
    status: 'single',
    rank: 35,
  },
  {
    firstName: 'Britney',
    lastName: 'Harber',
    age: 0,
    visits: 523,
    progress: 9,
    createdAt: new Date('2023-09-19T19:35:16'),
    status: 'complicated',
    rank: 1,
  },
];

export const compactTableColumns = [
  columnHelper.accessor('firstName', {
    id: 'default',
    cell: (info) => info.getValue(),
    header: 'default',
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge2',
    cell: (info) => <Badge text={info.column.id} variant="important" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.accessor('lastName', {
    id: 'Badge3',
    cell: (info) => <Badge text={info.column.id} variant="success" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Button',
    header: () => null,
    cell: () => (
      <DataListActionContainer>
        <Button>ボタン</Button>
        <Button>ボタン</Button>
        <Button>削除</Button>
      </DataListActionContainer>
    ),
  }),
];

export const cellStyleColumns = [
  columnHelper.accessor('firstName', {
    id: 'firstName',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('lastName', {
    id: 'lastName',
    cell: (info) => info.getValue(),
  }),
];

export const cellWidthColumns: DataListColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'fit',
    cell: (info) => info.getValue(),
    header: 'fit (default)',
  }),
  columnHelper.accessor('firstName', {
    id: 'fixed',
    cell: (info) => info.getValue(),
    header: 'fixed',
    meta: { columnWidthOption: 'fixed', width: 80 },
  }),
  columnHelper.accessor('firstName', {
    id: 'fill',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
];

export const compactCheckboxColumns: DataListColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'checkboxExample',
    cell: (info) => info.getValue(),
    header: 'Checkbox example',
  }),
  columnHelper.display({
    id: 'Badge',
    cell: (info) => <Badge text={info.column.id} variant="info" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Badge2',
    cell: (info) => <Badge text={info.column.id} variant="important" />,
    header: () => <span>Badge</span>,
  }),
  columnHelper.display({
    id: 'Badge3',
    cell: (info) => <Badge text={info.column.id} variant="success" />,
    header: () => <span>Badge</span>,
  }),
];

export const radioColumns: DataListColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'radioExample',
    cell: (info) => info.getValue(),
    header: 'Radio example',
  }),
];

export const multipleFillColumns: DataListColumnDef<Person, string>[] = [
  columnHelper.accessor('firstName', {
    id: 'fill1',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
  columnHelper.accessor('firstName', {
    id: 'fixed',
    cell: (info) => info.getValue(),
    header: 'fixed',
    meta: { columnWidthOption: 'fixed', width: 80 },
  }),
  columnHelper.accessor('firstName', {
    id: 'fill2',
    cell: (info) => info.getValue(),
    header: 'fill',
    meta: { columnWidthOption: 'fill' },
  }),
];

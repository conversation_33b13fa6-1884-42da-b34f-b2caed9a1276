import React from 'react';
import DataList from '../DataList';
import { takeScreenshot } from '../../utils/screenshotTestUtils';
import { data, compactTableColumns, cellStyleColumns, cellWidthColumns, compactCheckboxColumns, radioColumns, multipleFillColumns } from './testData';

describe('DataList', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={compactTableColumns} />, task);
  });

  test('WithHorizontalCenter', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={cellStyleColumns} horizontalAlign="center" />, task);
  });

  test('WithHorizontalRight', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={cellStyleColumns} horizontalAlign="right" />, task);
  });

  test('WithCompactVariant', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={compactTableColumns} compact />, task);
  });

  test('WithColumnWidths', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={cellWidthColumns} />, task);
  });

  test('WithCheckboxColumn', async ({ task }) => {
    await takeScreenshot(
      <DataList
        required
        fieldTitle="Form list"
        data={data}
        columns={compactCheckboxColumns}
        checkboxConfig={{
          id: 'checkbox-row-select-col-compact',
          headerConfig: () => {
            return { label: '全て選択', value: 'data-list-checkbox-select-all-compact' };
          },
          cellConfig: (row) => {
            return { label: '', value: `${row.id}-compact` };
          },
        }}
      />,
      task,
    );
  });

  test('WithCompactCheckboxColumn', async ({ task }) => {
    await takeScreenshot(
      <DataList
        required
        fieldTitle="Form list"
        data={data}
        columns={compactCheckboxColumns}
        checkboxConfig={{
          id: 'checkbox-row-select-col-compact',
          headerConfig: () => {
            return { label: '全て選択', value: 'data-list-checkbox-select-all-compact' };
          },
          cellConfig: (row) => {
            return { label: '', value: `${row.id}-compact` };
          },
        }}
        compact
      />,
      task,
    );
  });

  test('WithRadioColumn', async ({ task }) => {
    await takeScreenshot(
      <DataList
        required
        fieldTitle="Form list"
        data={data}
        columns={radioColumns}
        radioConfig={{
          id: 'radio-row-select-col',
          cellConfig: (row) => {
            return { label: '', value: row.id };
          },
        }}
      />,
      task,
    );
  });

  test('WithMultipleFillColumns', async ({ task }) => {
    await takeScreenshot(<DataList data={data} columns={multipleFillColumns} />, task);
  });
});

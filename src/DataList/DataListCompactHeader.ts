import styled, { css } from 'styled-components';
import { getTypographyVar } from '../typography';

export const DataListHeaderStyles = css`
  text-align: left;
  vertical-align: middle;
  line-height: ${getTypographyVar('lineHeight')};
  font-weight: ${getTypographyVar('boldFontWeight')};
`;

const DataListCompactHeader = styled.div`
  ${DataListHeaderStyles}

  width: 100%;
`;

export default DataListCompactHeader;

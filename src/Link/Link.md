In addition to props above, _<PERSON>_ accepts any default props for HTML _a_ tags.

### Default Link:

Note: You cannot pass something other than a string as children (except if you use asChild prop, see below)

```js
import Link from '@axa-japan/design-system-react/Link';

<Link href="#">Link Text</Link>;
```

### Without Underline Link:

```js
import Link from '@axa-japan/design-system-react/Link';

<Link href="#" underline={false}>
  Without Underline Link Text
</Link>;
```

### White link:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Link from '@axa-japan/design-system-react/Link';

<Stack spacing={2} sx={{ padding: '4', 'background-color': 'utilityBackgroundOcean' }}>
  <Link href="#" color="white">
    White Link
  </Link>
  <Link href="#" color="white" underline={false}>
    White Without Underline Link
  </Link>
</Stack>;
```

### With icon:

```js
import Stack from '@axa-japan/design-system-react/Stack';
import Link from '@axa-japan/design-system-react/Link';
import { AddIcon } from '@axa-japan/design-system-react/Icons';

<Stack spacing={2} direction="column" sx={{ padding: '4' }}>
  <Stack spacing={2}>
    <Link href="#" icon={<AddIcon />}>
      Link With Icon
    </Link>
    <Link href="#" icon={<AddIcon />} iconPosition="left">
      Link With Icon On The Left Side
    </Link>
  </Stack>
  <Stack spacing={2} sx={{ padding: '4', 'background-color': 'utilityBackgroundOcean' }}>
    <Link href="#" color="white" icon={<AddIcon />}>
      White Link With Icon
    </Link>
    <Link href="#" color="white" icon={<AddIcon />} iconPosition="left">
      White Link With Icon On The Left Side
    </Link>
  </Stack>
</Stack>;
```

### AsChild (navigation libraries):

Very useful when using routing libraries such as React Router or NextJS Router.

Important: Please check the code below to see wrong usage of this feature to understand how to not use it.

```js
import Link from '@axa-japan/design-system-react/Link';
import Stack from '@axa-japan/design-system-react/Stack';
import { Link as ReactRouterLink } from 'react-router-dom';

<Stack spacing={2}>
  <Link asChild>
    <ReactRouterLink to="/example-page">React Router Link</ReactRouterLink>
  </Link>
  {/* Invalid example that will not display anything! */}
  <Link asChild>Child component is missing!</Link>
  <Link asChild>
    <ReactRouterLink to="/example-page">
      <div>Extra html tag!</div>
    </ReactRouterLink>
  </Link>
</Stack>;
```

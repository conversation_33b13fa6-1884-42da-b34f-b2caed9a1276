import React from 'react';
import { MemoryRouter, Link as ReactRouterLink } from 'react-router-dom';
import { render } from '../../utils/testUtils';
import Link from '../Link';
import { AddIcon } from '../../Icons';

describe('Link', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Link href="#">link</Link>);
    const testElement = getByText(/link/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  ['blue', 'white'].forEach((color) => {
    test(`renders with color ${color} without crashing`, () => {
      const { getByText, container } = render(
        <Link href="#" color={color as 'blue' | 'white'}>
          Link
        </Link>,
      );
      const testElement = getByText(/link/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test('renders without crashing with underline false', () => {
    const { getByText, container } = render(
      <MemoryRouter>
        <Link href="#" underline={false}>
          link
        </Link>
      </MemoryRouter>,
    );
    const testElement = getByText(/link/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  ['left', 'right'].forEach((iconPosition) => {
    test(`renders with iconPosition ${iconPosition} without crashing`, () => {
      const { getByText, container } = render(
        <Link href="#" icon={<AddIcon />} iconPosition={iconPosition as 'left' | 'right'}>
          Link
        </Link>,
      );
      const testElement = getByText(/link/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test('renders without crashing with an icon', () => {
    const { getByText, container } = render(
      <Link href="#" icon={<AddIcon />}>
        Link
      </Link>,
    );
    const testElement = getByText(/link/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with asChild', () => {
    const { getByText, container } = render(
      <MemoryRouter>
        <Link asChild>
          <ReactRouterLink to="#">Link</ReactRouterLink>
        </Link>
      </MemoryRouter>,
    );
    const testElement = getByText(/link/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

import React from 'react';
import <PERSON> from '../<PERSON>';
import colors from '../../colors';
import { AddIcon } from '../../Icons';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Link', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Link href="#">Default Link</Link>, task);
  });

  test('WithoutUnderline', async ({ task }) => {
    await takeScreenshot(
      <Link href="#" underline={false}>
        Without underline Link
      </Link>,
      task,
    );
  });

  test('White', async ({ task }) => {
    await takeScreenshot(
      <div style={{ backgroundColor: colors.utilityBackgroundOcean, padding: '1rem' }}>
        <Link href="#" color="white">
          White Link
        </Link>
      </div>,
      task,
    );
  });

  test('WithoutUnderlineWhite', async ({ task }) => {
    await takeScreenshot(
      <div style={{ backgroundColor: colors.utilityBackgroundOcean, padding: '1rem' }}>
        <Link href="#" color="white" underline={false}>
          Without underline White Link
        </Link>
      </div>,
      task,
    );
  });

  test('RightIcon', async ({ task }) => {
    await takeScreenshot(
      <Link href="#" icon={<AddIcon />} iconPosition="right">
        Link With Right Icon
      </Link>,
      task,
    );
  });

  test('LeftIcon', async ({ task }) => {
    await takeScreenshot(
      <Link href="#" icon={<AddIcon />} iconPosition="left">
        Link With Left Icon
      </Link>,
      task,
    );
  });
});

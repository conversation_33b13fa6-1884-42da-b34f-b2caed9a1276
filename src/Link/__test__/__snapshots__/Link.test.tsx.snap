// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<PERSON> > renders with color blue without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    Link
  </a>
</div>
`;

exports[`Link > renders with color white without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-white); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    Link
  </a>
</div>
`;

exports[`Link > renders with iconPosition left without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c1 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c2 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row-reverse;"
  >
    Link
    <span
      class="c1"
      style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-0); --ajds-LinkIcon-margin-right: var(--ajds-spacing-1);"
    >
      <svg
        class="c2"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
        />
      </svg>
    </span>
  </a>
</div>
`;

exports[`Link > renders with iconPosition right without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c1 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c2 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    Link
    <span
      class="c1"
      style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
    >
      <svg
        class="c2"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
        />
      </svg>
    </span>
  </a>
</div>
`;

exports[`Link > renders without crashing 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    link
  </a>
</div>
`;

exports[`Link > renders without crashing with an icon 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c1 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c2 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    Link
    <span
      class="c1"
      style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
    >
      <svg
        class="c2"
        fill="none"
        focusable="false"
        style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19 13.0007H13V19.0007H11V13.0007H5V11.0007H11V5.00073H13V11.0007H19V13.0007Z"
        />
      </svg>
    </span>
  </a>
</div>
`;

exports[`Link > renders without crashing with asChild 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="/"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
  >
    Link
  </a>
</div>
`;

exports[`Link > renders without crashing with underline false 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

<div>
  <a
    class="c0"
    href="#"
    style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: none; --ajds-LinkBase-flex-direction: row;"
  >
    link
  </a>
</div>
`;

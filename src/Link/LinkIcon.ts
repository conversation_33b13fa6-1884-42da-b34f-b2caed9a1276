import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'iconPosition'> = {
  iconPosition: {
    right: {
      'margin-left': getSpacingVar(1),
      'margin-right': getSpacingVar(0),
    },
    left: {
      'margin-left': getSpacingVar(0),
      'margin-right': getSpacingVar(1),
    },
  },
};

const { useSx, getSxStyleRules } = sx('LinkIcon', [], variants);

export { useSx };

const LinkIcon = styled.span`
  display: flex;
  align-items: center;
  height: fit-content;

  svg {
    height: ${getSpacingVar(4)};
    width: ${getSpacingVar(4)};
  }

  ${getSxStyleRules()}
`;

export default LinkIcon;

import React from 'react';
import { Slottable } from '@radix-ui/react-slot';
import { LinkBase, useSx, LinkSlotBase } from './LinkBase';
import LinkIcon, { useSx as useLinkIconSx } from './LinkIcon';
import type { MarginSxPropType } from '../sx';

export type LinkProps = {
  /** Children should be string except if you use asChild */
  children: string | React.ReactElement;
  /** Changes the color of the link */
  color?: 'blue' | 'white';
  /** Adds underline to the link */
  underline?: boolean;
  /** Component to use for the icon */
  icon?: React.ReactElement;
  /** Position for the icon */
  iconPosition?: 'left' | 'right';
  /** Make the link act as the provided child, useful for using other routers */
  asChild?: boolean;
  /** Style overrides */
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'a'>;

const Link: React.FC<LinkProps> = ({ children, color = 'blue', underline = true, icon, iconPosition = 'right', asChild = false, sx, ...rest }) => {
  const linkSxOverrides = useSx(sx, { color, underline: String(underline), iconPosition });
  const iconSxOverrides = useLinkIconSx({}, { iconPosition });

  if (asChild) {
    // Don't try to render anything if children are invalid
    if (typeof children === 'string' || typeof children.props.children !== 'string') {
      return null;
    }

    return (
      <LinkSlotBase {...rest} style={linkSxOverrides}>
        <Slottable>{children}</Slottable>
        {icon && <LinkIcon style={iconSxOverrides}>{icon}</LinkIcon>}
      </LinkSlotBase>
    );
  }

  // Don't try to render anything if children is not a string, as it would break accessibility
  if (typeof children !== 'string') {
    return null;
  }

  return (
    <LinkBase {...rest} style={linkSxOverrides}>
      {children}
      {icon && <LinkIcon style={iconSxOverrides}>{icon}</LinkIcon>}
    </LinkBase>
  );
};

export default Link;

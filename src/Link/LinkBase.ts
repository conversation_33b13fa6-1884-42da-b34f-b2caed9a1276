import styled, { css } from 'styled-components';
import { Slot } from '@radix-ui/react-slot';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getTypographyVar } from '../typography';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'color' | 'underline' | 'iconPosition'> = {
  color: {
    blue: {
      color: getColorVar('interactiveActivePrimary'),
      'color:hover': getColorVar('interactiveHoverPrimary'),
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusPrimary')}`,
    },
    white: {
      color: getColorVar('interactiveActiveWhite'),
      'color:hover': getColorVar('interactiveHoverGrey'),
      'outline:focus-visible': `2px solid ${getColorVar('interactiveFocusWhite')}`,
    },
  },
  underline: {
    false: {
      'text-decoration-line': 'none',
    },
    true: {
      'text-decoration-line': 'underline',
    },
  },
  iconPosition: {
    right: {
      'flex-direction': 'row',
    },
    left: {
      'flex-direction': 'row-reverse',
    },
  },
};

const { useSx, getSxStyleRules } = sx('LinkBase', ['margin'], variants);

export { useSx };

export const LinkBaseStyles = css`
  position: relative;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: ${getTypographyVar('defaultFontSize')};
  font-weight: ${getTypographyVar('defaultFontWeight')};
  line-height: ${getTypographyVar('smLineHeight')};
  text-decoration-style: solid;
  text-underline-offset: ${getSpacingVar(1)};
  text-decoration-thickness: ${getSpacingVar('px')};

  svg {
    color: currentColor;
  }

  ${getSxStyleRules()}
`;

export const LinkBase = styled.a`
  ${LinkBaseStyles}
`;

export const LinkSlotBase = styled(Slot)`
  ${LinkBaseStyles}
`;

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Badge > renders with color danger without crashing 1`] = `
.c1 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c2 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-danger); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1) var(--ajds-spacing-0) var(--ajds-spacing-0-5);"
  >
    <svg
      class="c1"
      fill="none"
      focusable="false"
      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
        fill-rule="evenodd"
      />
    </svg>
    <span
      class="c2"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders with color important without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c1 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
  >
    <span
      class="c1"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders with color info without crashing 1`] = `
.c1 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c2 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-information); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1) var(--ajds-spacing-0) var(--ajds-spacing-0-5);"
  >
    <svg
      class="c1"
      fill="none"
      focusable="false"
      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
        fill-rule="evenodd"
      />
    </svg>
    <span
      class="c2"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders with color neutral without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c1 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary); --ajds-BadgeBase-background-color: var(--ajds-color-status-neutral); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
  >
    <span
      class="c1"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders with color success without crashing 1`] = `
.c1 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c2 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-success); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1) var(--ajds-spacing-0) var(--ajds-spacing-0-5);"
  >
    <svg
      class="c1"
      fill="none"
      focusable="false"
      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
      />
    </svg>
    <span
      class="c2"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders with color warning without crashing 1`] = `
.c1 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c2 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary); --ajds-BadgeBase-background-color: var(--ajds-color-status-warning); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1) var(--ajds-spacing-0) var(--ajds-spacing-0-5);"
  >
    <svg
      class="c1"
      fill="none"
      focusable="false"
      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
        fill-rule="evenodd"
      />
    </svg>
    <span
      class="c2"
    >
      BADGE
    </span>
  </div>
</div>
`;

exports[`Badge > renders without crashing 1`] = `
.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-0-5);
  margin: var(--ajds-BadgeBase-margin-top,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-right,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-bottom,var(--ajds-BadgeBase-margin,0)) var(--ajds-BadgeBase-margin-left,var(--ajds-BadgeBase-margin,0));
  color: var(--ajds-BadgeBase-color);
  background-color: var(--ajds-BadgeBase-background-color);
  padding: var(--ajds-BadgeBase-padding);
}

.c1 {
  color: inherit;
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  font-size: var(--ajds-font-size-default);
  word-break: keep-all;
}

<div>
  <div
    class="c0"
    style="--ajds-BadgeBase-color: var(--ajds-color-character-primary-white); --ajds-BadgeBase-background-color: var(--ajds-color-status-important); --ajds-BadgeBase-padding: var(--ajds-spacing-0) var(--ajds-spacing-1);"
  >
    <span
      class="c1"
    >
      BADGE
    </span>
  </div>
</div>
`;

import React from 'react';
import { render } from '../../utils/testUtils';
import Badge from '../Badge';

describe('Badge', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<Badge variant="important" text="BADGE" />);
    const testElement = getByText(/BADGE/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  (['important', 'neutral', 'success', 'danger', 'warning', 'info'] as const).forEach((variant) => {
    test(`renders with color ${variant} without crashing`, () => {
      const { getByText, container } = render(<Badge text="BADGE" variant={variant} hasIcon />);
      const testElement = getByText(/BADGE/i);
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

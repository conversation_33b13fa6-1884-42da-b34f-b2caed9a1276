import React from 'react';
import Badge from '../Badge';
import ErrorIcon from '../../Icons/ErrorIcon';
import CheckIcon from '../../Icons/CheckIcon';
import InformationIcon from '../../Icons/InformationIcon';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Badge', () => {
  test('BadgeImportantHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="important" hasIcon text="BADGE" />, task);
  });

  test('BadgeImportantNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="important" text="BADGE" />, task);
  });

  test('BadgeNeutralHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="neutral" hasIcon text="BADGE" />, task);
  });

  test('BadgeNeutralNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="neutral" text="BADGE" />, task);
  });

  test('BadgeSuccessHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="success" hasIcon text="BADGE" />, task);
  });

  test('BadgeSuccessNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="success" text="BADGE" />, task);
  });

  test('BadgeDangerHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="danger" hasIcon text="BADGE" />, task);
  });

  test('BadgeDangerNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="danger" text="BADGE" />, task);
  });

  test('BadgeWarningHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="warning" hasIcon text="警告" />, task);
  });

  test('BadgeWarningNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="warning" text="警告" />, task);
  });

  test('BadgeInfoHasIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="info" hasIcon text="情報" />, task);
  });

  test('BadgeInfoNoIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="info" text="情報" />, task);
  });

  test('BadgeSuccessOverridingIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="success" hasIcon overridingIcon={<ErrorIcon isOutline />} text="Success" />, task);
  });

  test('BadgeNeutralOverridingIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="neutral" hasIcon overridingIcon={<CheckIcon isStroke />} text="Neutral" />, task);
  });

  test('BadgeImportantOverridingIcon', async ({ task }) => {
    await takeScreenshot(<Badge variant="important" hasIcon overridingIcon={<InformationIcon isOutline />} text="Important" />, task);
  });
});

import React from 'react';
import CheckIcon from '../Icons/CheckIcon';
import ErrorIcon from '../Icons/ErrorIcon';
import InformationIcon from '../Icons/InformationIcon';
import WarningIcon from '../Icons/WarningIcon';
import type { MarginSxPropType } from '../sx';
import { BadgeBase, BadgeVariantType, useSx } from './BadgeBase';
import { BadgeText } from './BadgeText';

const iconRendering = ({ variant, hasIcon, overridingIcon }: { variant?: BadgeVariantType; hasIcon: boolean; overridingIcon?: JSX.Element }) => {
  if (!hasIcon) {
    return null;
  }

  if (overridingIcon) {
    return overridingIcon;
  }

  switch (variant) {
    case 'success':
      return <CheckIcon isStroke />;
    case 'danger':
      return <ErrorIcon isOutline />;
    case 'warning':
      return <WarningIcon isOutline />;
    case 'info':
      return <InformationIcon isOutline />;
    case 'important':
    case 'neutral':
    default:
      return null;
  }
};

export type BadgeProps = {
  /** Text for the badge */
  text: string;
  /** Display the icon */
  hasIcon?: boolean;
  /** Changes the variant of the badge */
  variant: BadgeVariantType;
  /** Style overrides */
  sx?: MarginSxPropType;
  /** Icon replacing the default one if any or adding */
  overridingIcon?: JSX.Element;
} & React.ComponentPropsWithoutRef<'div'>;

const Badge: React.FC<BadgeProps> = ({ children, text, hasIcon = false, variant, sx, overridingIcon, ...rest }) => {
  const iconToRender = iconRendering({ variant, hasIcon, overridingIcon });

  return (
    <BadgeBase {...rest} style={useSx(sx, { variant, hasIcon: iconToRender ? 'true' : 'false' })}>
      {iconToRender}
      <BadgeText>{text}</BadgeText>
    </BadgeBase>
  );
};

export default Badge;

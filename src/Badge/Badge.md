In addition to props below, _<PERSON><PERSON>_ accepts any default props for _div_ HTML tag.

### Variant options:

with `hasIcon: true` and `hasIcon: false` respectively

#### Important

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="important" hasIcon text="プライマリ" />
  <Badge variant="important" text="プライマリ" />
</div>;
```

#### Neutral

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="neutral" hasIcon text="バッジ" />
  <Badge variant="neutral" text="バッジ" />
</div>;
```

#### Success

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="success" hasIcon text="成功" />
  <Badge variant="success" text="成功" />
</div>;
```

#### Danger

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="danger" hasIcon text="エラー" />
  <Badge variant="danger" text="エラー" />
</div>;
```

#### Warning

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="warning" hasIcon text="警告" />
  <Badge variant="warning" text="警告" />
</div>;
```

#### Information

```js
import Badge from '@axa-japan/design-system-react/Badge';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="info" hasIcon text="情報" />
  <Badge variant="info" text="情報" />
</div>;
```

#### Override Icon

```js
import Badge from '@axa-japan/design-system-react/Badge';
import ErrorIcon from '@axa-japan/design-system-react/Icons/ErrorIcon';
import CheckIcon from '@axa-japan/design-system-react/Icons/CheckIcon';
import InformationIcon from '@axa-japan/design-system-react/Icons/InformationIcon';

<div style={{ display: 'flex', gap: 36 }}>
  <Badge variant="success" hasIcon overridingIcon={<ErrorIcon isOutline />} text="Success" />
  <Badge variant="neutral" hasIcon overridingIcon={<CheckIcon isStroke />} text="Neutral" />
  <Badge variant="important" hasIcon overridingIcon={<InformationIcon isOutline />} text="Important" />
</div>;
```

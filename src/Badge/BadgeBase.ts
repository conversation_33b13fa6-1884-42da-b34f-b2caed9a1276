import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

export type BadgeVariantType = 'important' | 'neutral' | 'success' | 'danger' | 'warning' | 'info';

const variants: VariantsType<'variant' | 'hasIcon'> = {
  variant: {
    important: {
      color: getColorVar('characterPrimaryWhite'),
      'background-color': getColorVar('statusImportant'),
    },
    neutral: {
      color: getColorVar('characterPrimary'),
      'background-color': getColorVar('statusNeutral'),
    },
    success: {
      color: getColorVar('characterPrimaryWhite'),
      'background-color': getColorVar('statusSuccess'),
    },
    danger: {
      color: getColorVar('characterPrimaryWhite'),
      'background-color': getColorVar('statusDanger'),
    },
    warning: {
      color: getColorVar('characterPrimary'),
      'background-color': getColorVar('statusWarning'),
    },
    info: {
      color: getColorVar('characterPrimaryWhite'),
      'background-color': getColorVar('statusInformation'),
    },
  },
  hasIcon: {
    true: {
      padding: `${getSpacingVar(0)} ${getSpacingVar(1)} ${getSpacingVar(0)} ${getSpacingVar(0.5)}`,
    },
    false: {
      padding: `${getSpacingVar(0)} ${getSpacingVar(1)}`,
    },
  },
};

const { useSx, getSxStyleRules } = sx('BadgeBase', ['margin'], variants);

export { useSx };

export const BadgeBase = styled.div`
  display: inline-flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  gap: ${getSpacingVar(0.5)};
  ${getSxStyleRules()};
`;

export default BadgeBase;

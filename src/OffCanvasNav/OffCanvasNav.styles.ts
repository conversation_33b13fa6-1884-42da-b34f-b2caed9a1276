import styled from 'styled-components';
import { getColorVar } from '../colors';
import media from '../Breakpoints/Breakpoints';

export const OffCanvasNavBase = styled.nav`
  border-top: 1px solid ${getColorVar('grey300')};
  bottom: 0;
  display: block;
  overflow: hidden scroll;
  position: fixed;
  right: 0;
  transform: translate3d(100%, 0, 0);
  transition: transform 0.2s ease;
  width: 100%;
  z-index: 100;

  ${media.smallOnly} {
    background-color: ${getColorVar('utilityBackgroundWhite')};
    top: 60px;
  }

  ${media.mediumOnly} {
    top: 70px;
  }

  &[data-is-open='true'] {
    transform: translate3d(0, 0, 0);
  }
`;

export const OffCanvasNavOverlay = styled.div`
  background-color: ${getColorVar('grey800')};
  left: 0;
  opacity: 0;
  position: fixed;
  top: 70px;
  transition: opacity 0.5s ease;
  z-index: 100;

  &[data-is-open='true'] {
    opacity: 0.5;
    height: 100%;
    width: 100%;
  }
`;

export const OffCanvasNavInner = styled.div`
  position: relative;

  ${media.mediumOnly} {
    background-color: ${getColorVar('utilityBackgroundWhite')};
    border-left: 1px solid ${getColorVar('grey300')};
    margin-left: auto;
    min-height: 100%;
    width: 49%;
  }
`;

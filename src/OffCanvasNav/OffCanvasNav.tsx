import React, { useRef, useEffect } from 'react';
import { disableBodyScroll, enableBodyScroll } from 'body-scroll-lock';
import { OffCanvasNavBase, OffCanvasNavOverlay, OffCanvasNavInner } from './OffCanvasNav.styles';

export type OffCanvasNavProps = {
  /** Flag to switch the icon if the menu is open */
  isNavOpen: boolean;
} & React.ComponentPropsWithoutRef<'div'>;

const OffCanvasNav: React.FC<React.PropsWithChildren<OffCanvasNavProps>> = ({ isNavOpen, children, ...rest }) => {
  const targetRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (targetRef.current) {
      if (isNavOpen) {
        disableBodyScroll(targetRef.current);
      } else {
        enableBodyScroll(targetRef.current);
      }
    }
  }, [isNavOpen]);

  return (
    <>
      <OffCanvasNavOverlay data-is-open={isNavOpen} />
      <OffCanvasNavBase ref={targetRef} data-is-open={isNavOpen} {...rest}>
        <OffCanvasNavInner>{children}</OffCanvasNavInner>
      </OffCanvasNavBase>
    </>
  );
};

export default OffCanvasNav;

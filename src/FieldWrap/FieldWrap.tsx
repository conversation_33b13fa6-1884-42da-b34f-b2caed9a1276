import React from 'react';
import { FieldWrapBase, useSx } from './FieldWrapBase';
import type { MarginSxPropType } from '../sx';

type FieldWrapProps = {
  /** Style overrides */
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'div'>;

const FieldWrap: React.FC<FieldWrapProps> = ({ children, sx, style, ...rest }) => {
  return (
    <FieldWrapBase {...rest} style={{ ...useSx(sx), ...style }}>
      {children}
    </FieldWrapBase>
  );
};

export default FieldWrap;

import React from 'react';
import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import Tooltip from '../Tooltip';
import Button from '../../Button/Button';

// Mock the ResizeObserver
const ResizeObserverMock = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Stub the global ResizeObserver
vi.stubGlobal('ResizeObserver', ResizeObserverMock);

describe('Tooltip', () => {
  // test case for tooltip with children
  test('renders component for tooltip without crashing', () => {
    const { getByTestId, container } = render(
      <Tooltip text="test tooltip">
        <Button data-testid="tooltip-trigger">test button</Button>
      </Tooltip>,
    );
    const testElement = getByTestId('tooltip-trigger');
    expect(testElement).toBeInTheDocument();
    expect(testElement).toHaveTextContent('test button');
    expect(container).toMatchSnapshot();
  });

  // Test hover behavior
  test('shows tooltip text on hover', async () => {
    const { getByText, getByTestId, container } = render(
      <Tooltip text="test tooltip">
        <Button>example</Button>
      </Tooltip>,
    );
    const tooltipTrigger = getByText('example');

    await userEvent.hover(tooltipTrigger);

    await waitFor(() => {
      expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('open');
    });

    await userEvent.unhover(tooltipTrigger);

    await waitFor(() => {
      expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('closed');
    });

    expect(container).toMatchSnapshot();
  });

  // Create tests for each placement
  ['top', 'bottom'].forEach((placement) => {
    test(`renders without crashing with placement - ${placement}`, async () => {
      const { getByText, getByTestId, container } = render(
        // @ts-ignore
        <Tooltip text="test tooltip" placement={placement}>
          <Button>example</Button>
        </Tooltip>,
      );
      const tooltipTrigger = getByText('example');

      await userEvent.hover(tooltipTrigger);

      await waitFor(() => {
        expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('open');
      });

      await userEvent.unhover(tooltipTrigger);

      await waitFor(() => {
        expect(getByTestId('tooltip-content').getAttribute('data-state')).toBe('closed');
      });

      expect(container).toMatchSnapshot();
    });
  });
});

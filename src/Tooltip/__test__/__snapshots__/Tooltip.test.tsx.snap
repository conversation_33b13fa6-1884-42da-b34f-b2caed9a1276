// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Tooltip > renders component for tooltip without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c3 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c4 {
  --arrow-background: var(--ajds-color-interactive-active-grey);
}

.c2 {
  background-color: var(--ajds-color-interactive-active-grey);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-xs);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2) var(--ajds-spacing-1) var(--ajds-spacing-2);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-label="test button"
    class="c0"
    data-part="trigger"
    data-scope="tooltip"
    data-state="closed"
    data-testid="tooltip-trigger"
    dir="ltr"
    id="tooltip::r0::trigger"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      test button
    </span>
  </button>
  <div
    data-part="positioner"
    data-scope="tooltip"
    dir="ltr"
    id="tooltip::r0::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(0, -100vh, 0); z-index: var(--z-index);"
  >
    <div
      class="c2"
      data-part="content"
      data-scope="tooltip"
      data-state="closed"
      data-testid="tooltip-content"
      dir="ltr"
      hidden=""
      id="tooltip::r0::content"
      role="tooltip"
      style="pointer-events: none;"
    >
      <div
        class="c3"
        data-part="arrow"
        data-scope="tooltip"
        data-testid="tooltip-arrow"
        dir="ltr"
        id="tooltip::r0::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1);"
      >
        <div
          class="c4"
          data-part="arrow-tip"
          data-scope="tooltip"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit;"
        />
      </div>
      <span
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        test tooltip
      </span>
    </div>
  </div>
</div>
`;

exports[`Tooltip > renders without crashing with placement - bottom 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c3 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c4 {
  --arrow-background: var(--ajds-color-interactive-active-grey);
}

.c2 {
  background-color: var(--ajds-color-interactive-active-grey);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-xs);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2) var(--ajds-spacing-1) var(--ajds-spacing-2);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-label="example"
    class="c0"
    data-part="trigger"
    data-scope="tooltip"
    data-state="closed"
    dir="ltr"
    id="tooltip::r3::trigger"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      example
    </span>
  </button>
  <div
    data-part="positioner"
    data-scope="tooltip"
    dir="ltr"
    id="tooltip::r3::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(var(--x), var(--y), 0); z-index: var(--z-index); --transform-origin: top center; --reference-width: 0px; --available-width: -16px; --available-height: -16px; --x: 0px; --y: 8px;"
  >
    <div
      class="c2"
      data-part="content"
      data-placement="bottom"
      data-scope="tooltip"
      data-state="closed"
      data-testid="tooltip-content"
      dir="ltr"
      id="tooltip::r3::content"
      role="tooltip"
      style="pointer-events: none;"
    >
      <div
        class="c3"
        data-part="arrow"
        data-scope="tooltip"
        data-testid="tooltip-arrow"
        dir="ltr"
        id="tooltip::r3::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1); left: 0px; bottom: calc(100% + var(--arrow-offset));"
      >
        <div
          class="c4"
          data-part="arrow-tip"
          data-scope="tooltip"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit; transform: rotate(45deg);"
        />
      </div>
      <span
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        test tooltip
      </span>
    </div>
  </div>
</div>
`;

exports[`Tooltip > renders without crashing with placement - top 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c3 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c4 {
  --arrow-background: var(--ajds-color-interactive-active-grey);
}

.c2 {
  background-color: var(--ajds-color-interactive-active-grey);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-xs);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2) var(--ajds-spacing-1) var(--ajds-spacing-2);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-label="example"
    class="c0"
    data-part="trigger"
    data-scope="tooltip"
    data-state="closed"
    dir="ltr"
    id="tooltip::r2::trigger"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      example
    </span>
  </button>
  <div
    data-part="positioner"
    data-scope="tooltip"
    dir="ltr"
    id="tooltip::r2::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(var(--x), var(--y), 0); z-index: var(--z-index); --transform-origin: bottom center; --reference-width: 0px; --available-width: -16px; --available-height: -16px; --x: 0px; --y: -8px;"
  >
    <div
      class="c2"
      data-part="content"
      data-placement="top"
      data-scope="tooltip"
      data-state="closed"
      data-testid="tooltip-content"
      dir="ltr"
      id="tooltip::r2::content"
      role="tooltip"
      style="pointer-events: none;"
    >
      <div
        class="c3"
        data-part="arrow"
        data-scope="tooltip"
        data-testid="tooltip-arrow"
        dir="ltr"
        id="tooltip::r2::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1); left: 0px; top: calc(100% + var(--arrow-offset));"
      >
        <div
          class="c4"
          data-part="arrow-tip"
          data-scope="tooltip"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit; transform: rotate(225deg);"
        />
      </div>
      <span
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        test tooltip
      </span>
    </div>
  </div>
</div>
`;

exports[`Tooltip > shows tooltip text on hover 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c3 {
  --arrow-size: var(--ajds-spacing-1-5);
}

.c4 {
  --arrow-background: var(--ajds-color-interactive-active-grey);
}

.c2 {
  background-color: var(--ajds-color-interactive-active-grey);
  color: var(--ajds-color-character-primary-white);
  border-radius: var(--ajds-radius-xs);
  padding: var(--ajds-spacing-1) var(--ajds-spacing-2) var(--ajds-spacing-1) var(--ajds-spacing-2);
}

.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-label="example"
    class="c0"
    data-part="trigger"
    data-scope="tooltip"
    data-state="closed"
    dir="ltr"
    id="tooltip::r1::trigger"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      example
    </span>
  </button>
  <div
    data-part="positioner"
    data-scope="tooltip"
    dir="ltr"
    id="tooltip::r1::popper"
    style="position: absolute; isolation: isolate; min-width: max-content; top: 0px; left: 0px; transform: translate3d(var(--x), var(--y), 0); z-index: var(--z-index); --transform-origin: top center; --reference-width: 0px; --available-width: -16px; --available-height: -16px; --x: 0px; --y: 8px;"
  >
    <div
      class="c2"
      data-part="content"
      data-placement="bottom"
      data-scope="tooltip"
      data-state="closed"
      data-testid="tooltip-content"
      dir="ltr"
      id="tooltip::r1::content"
      role="tooltip"
      style="pointer-events: none;"
    >
      <div
        class="c3"
        data-part="arrow"
        data-scope="tooltip"
        data-testid="tooltip-arrow"
        dir="ltr"
        id="tooltip::r1::arrow"
        style="position: absolute; width: var(--arrow-size); height: var(--arrow-size); --arrow-size-half: calc(var(--arrow-size) / 2); --arrow-offset: calc(var(--arrow-size-half) * -1); left: 0px; bottom: calc(100% + var(--arrow-offset));"
      >
        <div
          class="c4"
          data-part="arrow-tip"
          data-scope="tooltip"
          dir="ltr"
          style="background: var(--arrow-background); top: 0px; left: 0px; width: 100%; height: 100%; position: absolute; z-index: inherit; transform: rotate(45deg);"
        />
      </div>
      <span
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        test tooltip
      </span>
    </div>
  </div>
</div>
`;

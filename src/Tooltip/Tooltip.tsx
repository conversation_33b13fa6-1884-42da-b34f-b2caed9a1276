import { Tooltip as ArkTooltip } from '@ark-ui/react';
import React from 'react';
import Text from '../Text/index';
import { ArkTooltipArrow } from './ArkTooltipArrow';
import { ArkTooltipArrowTip } from './ArkTooltipArrowTip';
import { ArkTooltipContent } from './ArkTooltipContent';

export type TooltipProps = {
  /** Tooltip can wrap any other component, even plaintext */
  children: React.ReactNode;
  /** The tooltip's text */
  text: string;
  /** Control tooltip placement */
  placement?: 'top' | 'bottom';
} & React.ComponentPropsWithoutRef<'div'>;

const Tooltip: React.FC<TooltipProps> = ({ children, text, placement = 'bottom' }) => {
  return (
    <ArkTooltip.Root positioning={{ placement }}>
      <ArkTooltip.Trigger asChild>{children}</ArkTooltip.Trigger>
      <ArkTooltip.Positioner>
        <ArkTooltipContent data-testid="tooltip-content">
          <ArkTooltipArrow data-testid="tooltip-arrow">
            <ArkTooltipArrowTip />
          </ArkTooltipArrow>
          <Text as="span" color="primary-white">
            {text}
          </Text>
        </ArkTooltipContent>
      </ArkTooltip.Positioner>
    </ArkTooltip.Root>
  );
};

export default Tooltip;

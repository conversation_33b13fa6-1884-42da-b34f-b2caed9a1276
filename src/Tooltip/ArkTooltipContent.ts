import styled from 'styled-components';
import { Tooltip as ArkTooltip } from '@ark-ui/react';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const backgroundColor = getColorVar('interactiveActiveGrey');

export const ArkTooltipContent = styled(ArkTooltip.Content)`
  background-color: ${backgroundColor};
  color: ${getColorVar('characterPrimaryWhite')};
  border-radius: ${getRadiusVar('xs')};
  padding: ${getSpacingVar(1)} ${getSpacingVar(2)} ${getSpacingVar(1)} ${getSpacingVar(2)};
`;

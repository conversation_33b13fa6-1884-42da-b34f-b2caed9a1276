import React from 'react';
import Toast from '../Toast';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Toast', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Toast description="This is a toast description" buttonText="Button" buttonAction={() => {}} />, task);
  });

  test('LongDescription', async ({ task }) => {
    await takeScreenshot(
      <Toast
        description="This is a very very very very very very very very very very very very very very long toast description"
        buttonText="Button"
        buttonAction={() => {}}
      />,
      task,
    );
  });
});

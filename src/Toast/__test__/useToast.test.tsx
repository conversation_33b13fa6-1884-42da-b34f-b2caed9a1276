import React from 'react';
import { fireEvent } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import useToast from '../useToast';

const TestComponent: React.FC = () => {
  const triggerToast = useToast();
  return <button onClick={() => triggerToast({ description: 'This is a toast description' })}>button</button>;
};

const CustomizedTestComponent: React.FC = () => {
  const triggerToast = useToast();
  return (
    <button
      onClick={() => triggerToast({ description: 'This is a customized toast description', buttonText: 'Custom Button', buttonAction: () => {} })}
    >
      button
    </button>
  );
};

describe('useToast', () => {
  test('triggers Toast without crashing', () => {
    const { getByText } = render(<TestComponent />);
    const buttonElement = getByText(/button/i);
    fireEvent.click(buttonElement);
    const testElement = getByText('This is a toast description');
    expect(testElement).toBeInTheDocument();
  });
  test('triggers customized Toast without crashing', () => {
    const { getByText } = render(<CustomizedTestComponent />);
    const buttonElement = getByText(/button/i);
    fireEvent.click(buttonElement);
    const testElement = getByText('This is a customized toast description');
    const testButtonElement = getByText('Custom Button');
    expect(testElement).toBeInTheDocument();
    expect(testButtonElement).toBeInTheDocument();
  });
});

import React from 'react';
import userEvent from '@testing-library/user-event';
import { render } from '../../utils/testUtils';
import Toast from '../Toast';
import useToast from '../useToast';
import Button from '../../Button';

describe('Toast', () => {
  test('renders without crashing', () => {
    const { getByRole, getByText, container } = render(
      <Toast description="This is a toast description" buttonText="toast" buttonAction={() => {}} />,
    );
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(getByText('This is a toast description')).toBeInTheDocument();
    expect(getByText('toast')).toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });

  test('renders without crashing via trigger', async () => {
    const TriggerContainer: React.FC = () => {
      const triggerToast = useToast();
      return (
        <Button
          onClick={() =>
            triggerToast({
              description: 'This is a toast description',
              buttonText: 'toast',
              buttonAction: () => {},
            })
          }
        >
          Click me
        </Button>
      );
    };

    const { getByRole, getByText, container } = render(<TriggerContainer />);
    const triggerButton = getByRole('button');
    expect(triggerButton).toBeInTheDocument();
    await userEvent.click(triggerButton);

    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(getByText('This is a toast description')).toBeInTheDocument();
    expect(getByText('toast')).toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Toast > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  min-height: var(--ajds-spacing-14);
  color: var(--ajds-color-character-primary-white);
  background-color: var(--ajds-color-grey-800);
  box-shadow: var(--ajds-shadow-small);
  border-radius: var(--ajds-radius-sm);
  width: min(350px,100vw - var(--ajds-spacing-4));
}

.c1 {
  padding: var(--ajds-spacing-4) var(--ajds-spacing-0) var(--ajds-spacing-0) var(--ajds-spacing-4);
  max-width: 334px;
}

.c3 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c3 svg {
  color: currentColor;
}

.c3:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c3:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c3[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c3:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c3[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c3:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c4 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c4[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c2 {
  padding: var(--ajds-spacing-1) var(--ajds-spacing-3);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-width: 100%;
}

@media (hover:hover) {
  .c3:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c3:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <div
    aria-live="polite"
    class="c0"
    role="alert"
  >
    <p
      class="c1"
    >
      This is a toast description
    </p>
    <div
      class="c2"
    >
      <button
        aria-label="toast"
        class="c3"
        style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-grey-200); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
        type="button"
      >
        <span
          class="c4"
          data-text-variant="true"
          style="--ajds-ButtonText-text-decoration: underline;"
        >
          toast
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`Toast > renders without crashing via trigger 1`] = `
.c0 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c0 svg {
  color: currentColor;
}

.c0:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c0:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c0[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c0:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c0[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c0:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c1 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c1[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

@media (hover:hover) {
  .c0:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c0:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <button
    aria-label="Click me"
    class="c0"
    style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
    type="button"
  >
    <span
      class="c1"
      data-text-variant="false"
      style="--ajds-ButtonText-text-decoration: none;"
    >
      Click me
    </span>
  </button>
</div>
`;

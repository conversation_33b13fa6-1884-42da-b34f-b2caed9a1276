import React, { forwardRef, useRef } from 'react';
import ToastBase from './ToastBase';
import ToastText from './ToastText';
import Button from '../Button/Button';
import { ToastButtonWrapper } from './ToastButtonWrapper';

export type ToastProps = {
  /** Description text of the Toast */
  description: string;
  /** Button text of the Toast */
  buttonText: string;
  /** Button action of the Toast */
  buttonAction: () => void;
} & React.ComponentPropsWithRef<'div'>;

const Toast = forwardRef<HTMLDivElement, ToastProps>(({ description, buttonText, buttonAction, ...rest }, ref) => {
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  return (
    <ToastBase ref={ref} role="alert" aria-live="polite" aria-describedby={descriptionRef?.current?.id} {...rest}>
      <ToastText ref={descriptionRef}>{description}</ToastText>
      <ToastButtonWrapper>
        <Button variant="text" color="white" onClick={buttonAction}>
          {buttonText}
        </Button>
      </ToastButtonWrapper>
    </ToastBase>
  );
});

Toast.displayName = 'Toast';

export default React.memo(Toast);

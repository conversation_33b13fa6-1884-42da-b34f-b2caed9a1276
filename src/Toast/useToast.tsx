import React from 'react';
import { createStandaloneToast } from '@chakra-ui/toast';
import Toast from './Toast';
import useScreenSize from '../hooks/useScreenSize';

export type TriggerToastProps = {
  /** Description text of the Toast */
  description: string;
  /** Button text of the Toast, default is "閉じる" */
  buttonText?: string;
  /** Button action of the Toast, default is close action */
  buttonAction?: () => void;
} & React.ComponentPropsWithRef<'div'>;

const useToast = () => {
  const { toast } = createStandaloneToast();
  const screenSize = useScreenSize();

  const triggerToast = ({ description, buttonText, buttonAction, ...rest }: TriggerToastProps) => {
    // Close all toasts before opening a new one
    toast.closeAll();
    toast({
      duration: 5000,
      position: screenSize === 'small' ? 'bottom' : 'bottom-right',
      containerStyle: {
        minWidth: 'initial',
      },
      render: (props) => (
        <Toast description={description} buttonText={buttonText || '閉じる'} buttonAction={buttonAction || props.onClose} {...rest} />
      ),
    });
  };

  return triggerToast;
};

export default useToast;

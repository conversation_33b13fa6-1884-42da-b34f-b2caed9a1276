In addition to props above, the _Toast_ or the trigger returned from the _useToast_ hook accepts any default props for _div_ HTML tags.

### Default toast:

```js
import Button from '@axa-japan/design-system-react/Button';
import { useToast } from '@axa-japan/design-system-react/Toast';

const triggerToast = useToast();

<Button onClick={() => triggerToast({ description: 'This is a toast description' })}>Click me</Button>;
```

### With long text:

```js
import Button from '@axa-japan/design-system-react/Button';
import { useToast } from '@axa-japan/design-system-react/Toast';

const triggerToast = useToast();

<Button
  onClick={() =>
    triggerToast({ description: 'This is a very very very very very very very very very very very very very very long toast description' })
  }
>
  Click me
</Button>;
```

### With customized button:

```js
import Button from '@axa-japan/design-system-react/Button';
import { useToast } from '@axa-japan/design-system-react/Toast';

const triggerToast = useToast();

<Button
  onClick={() =>
    triggerToast({
      description: 'Click the button to say hello',
      buttonText: 'Hello',
      buttonAction: () => alert('Hello there'),
    })
  }
>
  Click me
</Button>;
```

### Exposing Ref

A ref to the Toast's container is exposed.

```js
import React, { useRef } from 'react';
import Button from '@axa-japan/design-system-react/Button';
import { useToast } from '@axa-japan/design-system-react/Toast';

const triggerToast = useToast();
const ref = useRef(null);

<Button
  onClick={() =>
    triggerToast({
      description: 'Click the button to say hello',
      buttonText: 'Hello',
      buttonAction: () => alert('Hello there'),
      ref,
    })
  }
>
  Click me
</Button>;
```

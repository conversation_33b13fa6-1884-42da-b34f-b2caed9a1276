import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getShadowVar } from '../shadows';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const ToastBase = styled.div`
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  min-height: ${getSpacingVar(14)};
  color: ${getColorVar('characterPrimaryWhite')};
  background-color: ${getColorVar('grey800')};
  box-shadow: ${getShadowVar('smallShadow')};
  border-radius: ${getRadiusVar('sm')};

  /* Defined in absolute pixels to match current design"} */
  width: min(350px, 100vw - ${getSpacingVar(4)});
`;

export default ToastBase;

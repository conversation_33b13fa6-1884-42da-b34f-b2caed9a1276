import React from 'react';
import { FieldErrorStyle } from './FieldErrorStyle';
import ErrorIcon from '../Icons/ErrorIcon';
import { FieldErrorIconContainer } from './FieldErrorIconContainer';

type FieldErrorProps = {
  hasError?: boolean;
  errorMessage?: string;
} & React.ComponentPropsWithoutRef<'div'>;

const FieldError: React.FC<FieldErrorProps> = ({ hasError, errorMessage, ...rest }) => {
  if (hasError && errorMessage) {
    return (
      <FieldErrorStyle role="alert" aria-live="polite" {...rest}>
        <FieldErrorIconContainer>
          <ErrorIcon size="small" />
        </FieldErrorIconContainer>
        {errorMessage}
      </FieldErrorStyle>
    );
  }
  return null;
};

export default FieldError;

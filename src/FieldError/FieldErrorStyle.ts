import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getTypographyVar } from '../typography';
import { getSpacingVar } from '../spacing';

export const FieldErrorStyle = styled.div`
  display: flex;
  align-items: flex-start;
  font-size: ${getTypographyVar('defaultFontSize')};
  line-height: 150%;
  width: 100%;
  column-gap: ${getSpacingVar(1)};
  color: ${getColorVar('statusDanger')};
  white-space: pre-line;
`;

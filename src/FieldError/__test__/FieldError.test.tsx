import React from 'react';
import { render } from '../../utils/testUtils';
import FieldError from '../FieldError';

describe('FieldError', () => {
  test('renders error without crashing', () => {
    const { getByText, container } = render(
      <FieldError hasError={true} errorMessage="Error">
        Field Description
      </FieldError>,
    );
    const testElement = getByText('Error');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

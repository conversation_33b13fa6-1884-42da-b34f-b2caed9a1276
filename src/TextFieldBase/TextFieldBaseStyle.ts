import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import input from '../styles/input';
import sx, { VariantsType } from '../sx';

const variants: VariantsType<'width'> = {
  width: {
    fill: {
      'max-width': '100%',
    },
    20: {
      'max-width': getSpacingVar(20),
    },
  },
};

const { useSx, getSxStyleRules } = sx('TextFieldBase', [], variants);

export { useSx };

export const TextFieldBaseStyle = styled.input`
  ${input.base}
  ${input.baseBorder}

  &:focus:not(:disabled) {
    ${input.focusBoxShadow}
  }

  &:disabled {
    ${input.disabledBorder}
  }

  min-width: ${getSpacingVar(20)};
  padding: ${getSpacingVar(3)} ${getSpacingVar(3.5)};
  text-overflow: ellipsis;
  flex: 1 0 0;

  ${getSxStyleRules()};
`;

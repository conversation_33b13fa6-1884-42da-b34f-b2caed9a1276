import React from 'react';
import { render } from '../../utils/testUtils';
import TextFieldBase from '../TextFieldBase';

describe('TextFieldBase', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<TextFieldBase />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((disabled) => {
    test(`renders prop disabled ${disabled}, without crashing`, () => {
      const { getByRole, container } = render(<TextFieldBase disabled={disabled} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((hasError) => {
    test(`renders prop hasError ${hasError}, without crashing`, () => {
      const { getByRole, container } = render(<TextFieldBase hasError={hasError} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

import React from 'react';
import TextFieldBase from '../TextFieldBase';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('TextFieldBase', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<TextFieldBase />, task);
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <div style={{ marginLeft: '5px' }}>
        <TextFieldBase />
      </div>,
      task,
      {
        interactionSelector: 'input[type="text"]',
        interactionType: 'focus',
      },
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(<TextFieldBase hasError />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<TextFieldBase disabled />, task);
  });
});

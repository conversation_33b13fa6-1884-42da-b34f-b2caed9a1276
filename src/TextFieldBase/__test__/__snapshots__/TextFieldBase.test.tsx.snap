// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TextFieldBase > renders prop disabled false, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c1[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c1:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c1:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <input
    aria-invalid="false"
    class="c0 c1"
    style="--ajds-TextFieldBase-max-width: 100%;"
    type="text"
  />
</div>
`;

exports[`TextFieldBase > renders prop disabled true, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c1[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c1:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c1:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <input
    aria-invalid="false"
    class="c0 c1"
    disabled=""
    style="--ajds-TextFieldBase-max-width: 100%;"
    type="text"
  />
</div>
`;

exports[`TextFieldBase > renders prop hasError false, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c1[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c1:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c1:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <input
    aria-invalid="false"
    class="c0 c1"
    style="--ajds-TextFieldBase-max-width: 100%;"
    type="text"
  />
</div>
`;

exports[`TextFieldBase > renders prop hasError true, without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c1[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c1:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c1:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <input
    aria-invalid="true"
    class="c0 c1"
    style="--ajds-TextFieldBase-max-width: 100%;"
    type="text"
  />
</div>
`;

exports[`TextFieldBase > renders without crashing 1`] = `
.c1 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  min-width: var(--ajds-spacing-20);
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  text-overflow: ellipsis;
  -webkit-flex: 1 0 0;
  -ms-flex: 1 0 0;
  flex: 1 0 0;
  max-width: var(--ajds-TextFieldBase-max-width);
}

.c1::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c1:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c0:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c1:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c1[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c1:focus:not(:disabled) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c1:disabled {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

<div>
  <input
    aria-invalid="false"
    class="c0 c1"
    style="--ajds-TextFieldBase-max-width: 100%;"
    type="text"
  />
</div>
`;

import React, { forwardRef } from 'react';
import { TextFieldBaseStyle, useSx } from './TextFieldBaseStyle';

const inputWidthValues = ['20'] as const;

export type InputWidth = (typeof inputWidthValues)[number];

export type TextFieldBaseProps = {
  /** Width of the actual input, using predefined values chosen from our spacing/sizing scale (20) */
  inputWidth?: InputWidth;
  /** Flag for showing error state */
  hasError?: boolean;
} & React.ComponentPropsWithRef<'input'>;

const TextFieldBase = forwardRef<HTMLInputElement, TextFieldBaseProps>(({ hasError = false, type = 'text', inputWidth, ...rest }, ref) => {
  const cleansedInputWidth = inputWidth && inputWidthValues.includes(inputWidth) ? inputWidth : 'fill';
  return <TextFieldBaseStyle ref={ref} type={type} aria-invalid={hasError} {...rest} style={useSx({}, { width: cleansedInputWidth })} />;
});

TextFieldBase.displayName = 'TextFieldBase';

export default TextFieldBase;

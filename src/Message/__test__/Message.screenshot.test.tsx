import React from 'react';
import Message from '../Message';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Message', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Message title="default">Message Contents</Message>, task);
  });

  test('Success', async ({ task }) => {
    await takeScreenshot(
      <Message status="success" title="success">
        Message Contents
      </Message>,
      task,
    );
  });

  test('Danger', async ({ task }) => {
    await takeScreenshot(
      <Message status="danger" title="danger">
        Message Contents
      </Message>,
      task,
    );
  });

  test('Warning', async ({ task }) => {
    await takeScreenshot(
      <Message status="warning" title="warning">
        Message Contents
      </Message>,
      task,
    );
  });

  test('Neutral', async ({ task }) => {
    await takeScreenshot(
      <Message status="neutral" title="neutral">
        Message Contents
      </Message>,
      task,
    );
  });

  test('WithoutTitle', async ({ task }) => {
    await takeScreenshot(<Message>Message Contents</Message>, task);
  });

  test('WithoutCloseButton', async ({ task }) => {
    await takeScreenshot(
      <Message hasCloseButton={false} title="Title">
        Message Contents
      </Message>,
      task,
    );
  });
});

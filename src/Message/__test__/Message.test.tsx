import React from 'react';
import { render } from '../../utils/testUtils';
import Message from '../Message';

const statuses = ['information', 'success', 'danger', 'warning', 'neutral'] as const;
describe('Message', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<Message title="message text">Message Component</Message>);
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  statuses.forEach((status) => {
    test(`renders with status ${status} without crashing`, () => {
      const { getByRole, container } = render(
        <Message status={status} title="message text">
          Message Component
        </Message>,
      );
      const testElement = getByRole('alert');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

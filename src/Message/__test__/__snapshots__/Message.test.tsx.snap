// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Message > renders with status danger without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-status-danger-light); --ajds-MessageBase-border-color: var(--ajds-color-status-danger); --ajds-MessageBase-color: var(--ajds-color-status-danger);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            message text
          </p>
          <div
            class="c7"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c8"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c9"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c10"
        >
          <svg
            class="c3"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

exports[`Message > renders with status information without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-status-information-light); --ajds-MessageBase-border-color: var(--ajds-color-status-information); --ajds-MessageBase-color: var(--ajds-color-status-information);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            message text
          </p>
          <div
            class="c7"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c8"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c9"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c10"
        >
          <svg
            class="c3"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

exports[`Message > renders with status neutral without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c9 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c4 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-utility-background-white); --ajds-MessageBase-border-color: var(--ajds-color-status-neutral-dark); --ajds-MessageBase-color: var(--ajds-color-status-neutral-dark);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <div
          class="c3"
        >
          <p
            class="c4"
          >
            message text
          </p>
          <div
            class="c5"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c6"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c7"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c8"
        >
          <svg
            class="c9"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

exports[`Message > renders with status success without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-status-success-light); --ajds-MessageBase-border-color: var(--ajds-color-status-success); --ajds-MessageBase-color: var(--ajds-color-status-success);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            message text
          </p>
          <div
            class="c7"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c8"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c9"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c10"
        >
          <svg
            class="c3"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

exports[`Message > renders with status warning without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-status-warning-light); --ajds-MessageBase-border-color: var(--ajds-color-status-warning); --ajds-MessageBase-color: var(--ajds-color-status-warning-dark);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            message text
          </p>
          <div
            class="c7"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c8"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c9"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c10"
        >
          <svg
            class="c3"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

exports[`Message > renders without crashing 1`] = `
.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c9 svg {
  color: currentColor;
}

.c9[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c10 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

@media (hover:hover) {
  .c9:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c9:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-background-color: var(--ajds-color-status-information-light); --ajds-MessageBase-border-color: var(--ajds-color-status-information); --ajds-MessageBase-color: var(--ajds-color-status-information);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 3.00098C7.032 3.00098 3 7.03298 3 12.001C3 16.969 7.032 21.001 12 21.001C16.968 21.001 21 16.969 21 12.001C21 7.03298 16.968 3.00098 12 3.00098ZM11.1 16.5011V11.1011H12.9V16.5011H11.1ZM11.1 7.50098V9.30098H12.9V7.50098H11.1Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            message text
          </p>
          <div
            class="c7"
          >
            Message Component
          </div>
        </div>
      </div>
    </div>
    <button
      aria-label="閉じる"
      class="c8"
      style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
      type="button"
    >
      <div
        class="c9"
        style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
      >
        <span
          class="c10"
        >
          <svg
            class="c3"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
            />
          </svg>
        </span>
      </div>
    </button>
  </div>
</div>
`;

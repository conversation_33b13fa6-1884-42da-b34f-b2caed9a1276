A message is used to provide additional information to the user in context.
They are designed to show status that describes the message’s importance.

### Message status:

```js
import React, { useState } from 'react';
import Message from '@axa-japan/design-system-react/Message';
import Button from '@axa-japan/design-system-react/Button';

<div>
  <Message title="information">入力内容をご確認ください。</Message>
  <br />
  <Message title="danger" status="danger">
    入力内容をご確認ください。
  </Message>
  <br />
  <Message title="success" status="success">
    入力内容をご確認ください。
  </Message>
  <br />
  <Message title="warning" status="warning">
    入力内容をご確認ください。
  </Message>
  <br />
  <Message title="neutral" status="neutral">
    入力内容をご確認ください。
  </Message>
</div>;
```

### Without Close Button:

```js
import React, { useState } from 'react';
import Message from '@axa-japan/design-system-react/Message';

<Message title="タイトル" hasCloseButton={false}>
  説明文章 <br />
  説明文章 <br />
  説明文章
</Message>;
```

### Without Title:

```js
import React, { useState } from 'react';
import Message from '@axa-japan/design-system-react/Message';

<Message>入力内容をご確認ください。</Message>;
```

### Show Message by trigger (controlled):

```js
import React, { useState } from 'react';
import Message from '@axa-japan/design-system-react/Message';
import Button from '@axa-japan/design-system-react/Button';

function MessageExample() {
  const [showMessage, setShowMessage] = useState(false);

  const handleClose = (value) => {
    // Message Close handler code here
    setShowMessage(value);
  };

  return (
    <div>
      <Message onClose={handleClose} title="タイトル" showMessage={showMessage} sx={{ 'margin-bottom': 2 }}>
        説明文章 <br />
        説明文章 <br />
        説明文章
      </Message>
      <Button onClick={() => setShowMessage(true)}>Message Trigger</Button>
    </div>
  );
}

<MessageExample />;
```

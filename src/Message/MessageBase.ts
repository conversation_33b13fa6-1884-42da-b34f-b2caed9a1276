import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const variants: VariantsType<'status'> = {
  status: {
    information: {
      'background-color': getColorVar('statusInformationLight'),
      'border-color': getColorVar('statusInformation'),
      color: getColorVar('statusInformation'),
    },
    success: {
      'background-color': getColorVar('statusSuccessLight'),
      'border-color': getColorVar('statusSuccess'),
      color: getColorVar('statusSuccess'),
    },
    danger: {
      'background-color': getColorVar('statusDangerLight'),
      'border-color': getColorVar('statusDanger'),
      color: getColorVar('statusDanger'),
    },
    warning: {
      'background-color': getColorVar('statusWarningLight'),
      'border-color': getColorVar('statusWarning'),
      color: getColorVar('statusWarningDark'),
    },
    neutral: {
      'background-color': getColorVar('utilityBackgroundWhite'),
      'border-color': getColorVar('statusNeutralDark'),
      color: getColorVar('statusNeutralDark'),
    },
  },
};

const { useSx, getSxStyleRules } = sx('MessageBase', ['margin'], variants);

export { useSx };

const MessageBase = styled.div`
  border: 1px solid;
  margin-bottom: ${getSpacingVar(2)};
  padding: ${getSpacingVar(2)} ${getSpacingVar(2)} ${getSpacingVar(2)} ${getSpacingVar(4)};
  display: none;
  border-radius: ${getRadiusVar('sm')};

  &[data-show='true'] {
    display: flex;
  }
  ${getSxStyleRules()}
`;

export default MessageBase;

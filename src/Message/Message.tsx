import React, { useEffect, useState } from 'react';
import { MarginSxPropType } from '../sx';
import MessageBase, { useSx } from './MessageBase';
import InformationIcon from '../Icons/InformationIcon';
import ErrorIcon from '../Icons/ErrorIcon';
import CheckIcon from '../Icons/CheckIcon';
import WarningIcon from '../Icons/WarningIcon';
import MessageDescription from './MessageDescription';
import MessageIconWrapper from './MessageIconWrapper';
import MessageContent from './MessageContent';
import CloseButton from '../CloseButton';
import MessageText from './MessageText';
import MessageBody from './MessageBody';
import MessageTitle from './MessageTitle';

export type MessageProps = {
  /** Message status */
  status?: 'information' | 'danger' | 'success' | 'warning' | 'neutral';
  /** Message title text */
  title?: string;
  /** Display the message component */
  showMessage?: boolean;
  /** Function called when Message close button is clicked */
  onClose?: (show: boolean) => void;
  /** Display the Close Button */
  hasCloseButton?: boolean;
  /** Message body */
  children: React.ReactNode;
  /** Style overrides */
  sx?: MarginSxPropType;
} & React.ComponentPropsWithoutRef<'div'>;

type MessageStatusType = 'information' | 'danger' | 'success' | 'warning' | 'neutral';

const iconRendering = (status: MessageStatusType) => {
  switch (status) {
    case 'information':
      return <InformationIcon size="medium" />;
    case 'danger':
      return <ErrorIcon size="medium" />;
    case 'success':
      return <CheckIcon size="medium" isStroke />;
    case 'warning':
      return <WarningIcon size="medium" />;
    case 'neutral':
    default:
      return null;
  }
};

const Message: React.FC<MessageProps> = ({
  children,
  onClose,
  hasCloseButton = true,
  status = 'information',
  title,
  showMessage = true,
  sx,
  ...rest
}) => {
  const [show, setShow] = useState(showMessage);
  const iconToRender = iconRendering(status);

  useEffect(() => {
    setShow(showMessage);
  }, [showMessage]);

  const onMessageClose = () => {
    if (onClose) {
      onClose(false);
    }
    setShow(false);
  };

  return (
    <MessageBase data-show={show} style={useSx(sx, { status })} {...rest} role="alert">
      <MessageBody>
        {iconToRender && <MessageIconWrapper>{iconToRender}</MessageIconWrapper>}
        <MessageContent>
          <MessageText>
            {title && <MessageTitle>{title}</MessageTitle>}
            <MessageDescription>{children}</MessageDescription>
          </MessageText>
        </MessageContent>
      </MessageBody>
      {hasCloseButton && <CloseButton color="grey" onClick={onMessageClose} />}
    </MessageBase>
  );
};

export default React.memo(Message);

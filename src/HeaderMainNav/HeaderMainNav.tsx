import React from 'react';
import Header<PERSON>ogo from '../HeaderLogo';
import { HeaderMainNavBase, HeaderMainNavInner, HeaderMainNavNavLinks } from './HeaderMainNav.styles';

export type HeaderMainNavProps = {
  /** URL for where you want the header logo link to go to */
  headerLogoTo?: string;
  /** Custom image for header logo */
  headerLogoImage?: React.ReactElement;
  /** Choose to use react router for header logo */
  headerLogoUseRouter?: boolean;
} & React.ComponentPropsWithoutRef<'div'>;

const HeaderMainNav: React.FC<React.PropsWithChildren<HeaderMainNavProps>> = ({
  headerLogoTo = '/',
  headerLogoImage,
  headerLogoUseRouter = true,
  children,
  ...rest
}) => (
  <HeaderMainNavBase {...rest}>
    <HeaderMainNavInner>
      <HeaderLogo useRouter={headerLogoUseRouter} to={headerLogoTo} logo={headerLogoImage} />
      <HeaderMainNavNavLinks>{children}</HeaderMainNavNavLinks>
    </HeaderMainNavInner>
  </HeaderMainNavBase>
);

export default HeaderMainNav;

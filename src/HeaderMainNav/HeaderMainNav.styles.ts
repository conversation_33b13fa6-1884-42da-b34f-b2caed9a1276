import styled from 'styled-components';
import { getColorVar } from '../colors';
import media from '../Breakpoints/Breakpoints';

export const HeaderMainNavBase = styled.div`
  background-color: ${getColorVar('utilityBackgroundWhite')};
  height: 70px;
  margin-top: 12px;
  position: relative;

  ${media.mediumDown} {
    display: none;
    margin-top: 0;
  }
`;

export const HeaderMainNavInner = styled.div`
  display: flex;
  flex-flow: row nowrap;
  height: 100%;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;

  ${media.largeUp} {
    padding: 0 16px;
  }
`;

export const HeaderMainNavNavLinks = styled.nav`
  display: flex;
  font-size: 0;
  height: 100%;
  justify-content: flex-end;
  list-style: none;
  padding-left: 0;
  transition:
    visibility 0s ease 0s,
    opacity 0.2s ease 0.2s;
  width: 100%;
`;

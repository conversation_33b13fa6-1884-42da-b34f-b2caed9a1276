import React, { useCallback, useState } from 'react';
import { Pagination as ArkPagination, PaginationRootBaseProps } from '@ark-ui/react';
import PaginationRoot from './PaginationRoot';
import PaginationEllipsis from './PaginationEllipsis';
import ChevronIcon from '../Icons/ChevronIcon';
import PaginationItem from './PaginationItem';
import { PaginationList } from './PaginationList';
import { PaginationItemContainer } from './PaginationItemContainer';
import PaginationPrevTrigger from './PaginationPrevTrigger';
import PaginationNextTrigger from './PaginationNextTrigger';

export type PaginationProps = {
  /** Set the number of total items you want to display */
  totalItems: number;
  /** Set the current page displayed */
  currentPage?: number;
  /** Link component to be rendered for each item. Use if you want to render links instead of buttons */
  renderAs?: (currentPage: number) => React.ReactElement;
  /** function to change the page number */
  onPageChange?: (pageNumber: number) => void;
} & Pick<PaginationRootBaseProps, 'defaultPage' | 'pageSize'>;

const Pagination: React.FC<PaginationProps> = ({ totalItems, currentPage, defaultPage, pageSize = 10, renderAs, onPageChange }) => {
  const [width, setWidth] = useState<number | null>(null);
  const maxCurrentPage = Math.ceil(totalItems / pageSize);
  const displaySinglePage = width && width <= (maxCurrentPage < 10000 ? 303 : 320);
  const paginationRef = useCallback((node: HTMLElement) => {
    if (node !== null) {
      setWidth(node.getBoundingClientRect().width);
    }
  }, []);
  const adjustSiblingCount = () => {
    if (!width) return 1;
    if (maxCurrentPage < 10000) {
      if (width >= 496) return 2;
      if (width >= 400) return 1;
    } else {
      if (width >= 530) return 2;
      if (width >= 425) return 1;
    }
    return 0;
  };
  return (
    <PaginationRoot
      ref={paginationRef}
      type={renderAs ? 'link' : 'button'}
      page={currentPage ? Math.min(currentPage, maxCurrentPage) : undefined}
      defaultPage={defaultPage ? Math.min(defaultPage, maxCurrentPage) : undefined}
      count={totalItems}
      pageSize={pageSize}
      siblingCount={adjustSiblingCount()}
      onPageChange={onPageChange ? (details) => onPageChange(details.page) : undefined}
    >
      <PaginationList>
        <PaginationItemContainer>
          <PaginationPrevTrigger data-testid="pagination_prev">
            <ChevronIcon rotation="180" />
          </PaginationPrevTrigger>
        </PaginationItemContainer>
        <ArkPagination.Context>
          {(pagination) =>
            pagination.pages.map((page, index) => {
              const isPageType = page.type === 'page';
              const isPageSelected = isPageType && pagination.page === page.value;

              const renderPaginationItem = () =>
                isPageType ? (
                  <PaginationItem {...page} asChild={!!renderAs}>
                    {renderAs ? renderAs(page.value) : page.value}
                  </PaginationItem>
                ) : null;

              if (displaySinglePage) {
                return isPageSelected ? (
                  <PaginationItemContainer key={index} aria-selected={isPageSelected}>
                    {renderPaginationItem()}
                  </PaginationItemContainer>
                ) : null;
              }

              return isPageType ? (
                <PaginationItemContainer key={index} aria-selected={isPageSelected}>
                  {renderPaginationItem()}
                </PaginationItemContainer>
              ) : (
                <PaginationEllipsis key={index} index={index}>
                  &#8230;
                </PaginationEllipsis>
              );
            })
          }
        </ArkPagination.Context>
        <PaginationItemContainer>
          <PaginationNextTrigger data-testid="pagination_next">
            <ChevronIcon />
          </PaginationNextTrigger>
        </PaginationItemContainer>
      </PaginationList>
    </PaginationRoot>
  );
};

export default Pagination;

import styled from 'styled-components';
import { Pagination } from '@ark-ui/react';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

const PaginationItem = styled(Pagination.Item)`
  all: unset;
  min-width: ${getSpacingVar(10)};
  height: ${getSpacingVar(10)};
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: ${getRadiusVar('sm')};
  font-size: ${getTypographyVar('defaultFontSize')};
  color: ${getColorVar('interactiveActivePrimary')};
  font-weight: ${getTypographyVar('defaultFontWeight')};
  line-height: ${getTypographyVar('smLineHeight')};
  cursor: pointer;

  &:disabled {
    cursor: default;

    svg {
      fill: ${getColorVar('interactiveDisabledDark')};
    }
  }

  &[data-selected] {
    cursor: default;
    color: ${getColorVar('characterAccent')};
    font-weight: ${getTypographyVar('boldFontWeight')};
  }

  :hover {
    background-color: ${getColorVar('interactiveHoverGreyTransparent')};
  }

  :focus-visible:not(:disabled) {
    box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
  }
`;

export default PaginationItem;

### Default Pagination (controlled):

You can control how many pages are displayed by specifying _totalItems_ and _pageSize_, where the total number of pages are calculated as _totalItems_ / _pageSize_.

```js
import React, { useState } from 'react';
import Pagination from '@axa-japan/design-system-react/Pagination';

const Component = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const onPageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  return <Pagination totalItems={24} currentPage={currentPage} pageSize={2} onPageChange={onPageChange} />;
};

<Component />;
```

### Default Pagination (uncontrolled):

```js
import React, { useState } from 'react';
import Pagination from '@axa-japan/design-system-react/Pagination';

<Pagination totalItems={24} defaultPage={2} pageSize={2} />;
```

### Pagination responsiveness:

Pagination is responsive and adjusts automatically depending on its container size.

```js
import React, { useState } from 'react';
import Pagination from '@axa-japan/design-system-react/Pagination';

<>
  <div style={{ width: '496px' }}>
    <Pagination totalItems={24} defaultPage={7} pageSize={2} />
  </div>
  <div style={{ width: '400px' }}>
    <Pagination totalItems={24} defaultPage={7} pageSize={2} />
  </div>
  <div style={{ width: '304px' }}>
    <Pagination totalItems={24} defaultPage={7} pageSize={2} />
  </div>
  <div style={{ width: '144px' }}>
    <Pagination totalItems={24} defaultPage={7} pageSize={2} />
  </div>
</>;
```

### Custom pagination item:

Pagination items are _button_ by default. _renderAs_ prop can be use to render other components such as _Links_.

```js
import React, { useState } from 'react';
import Pagination from '@axa-japan/design-system-react/Pagination';
import { Link as ReactRouterLink } from 'react-router-dom';

<Pagination
  totalItems={24}
  defaultPage={7}
  pageSize={2}
  renderAs={(currentPage) => <ReactRouterLink to={`/items/${currentPage}`}>{currentPage}</ReactRouterLink>}
/>;
```

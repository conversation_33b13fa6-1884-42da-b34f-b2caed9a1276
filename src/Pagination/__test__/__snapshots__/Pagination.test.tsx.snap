// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Pagination > renders custom pagination items without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-11);
  color: var(--ajds-color-character-secondary);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  all: unset;
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: var(--ajds-radius-sm);
  font-size: var(--ajds-font-size-default);
  color: var(--ajds-color-interactive-active-primary);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  cursor: pointer;
}

.c5:disabled {
  cursor: default;
}

.c5:disabled svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c5[data-selected] {
  cursor: default;
  color: var(--ajds-color-character-accent);
  font-weight: var(--ajds-font-weight-bold);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c2 {
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-11);
  margin: var(--ajds-spacing-1) var(--ajds-spacing-1) 0 var(--ajds-spacing-1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.c2[aria-selected='true'] {
  border-bottom: 4px solid var(--ajds-color-character-accent);
}

.c3 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c3[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c3:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c7[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

<div>
  <nav
    aria-label="pagination"
    class="c0"
    data-part="root"
    data-scope="pagination"
    dir="ltr"
    id="pagination::r1:"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <button
          aria-label="previous page"
          class="c3"
          data-part="prev-trigger"
          data-scope="pagination"
          data-testid="pagination_prev"
          dir="ltr"
          id="pagination::r1::prev"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <a
          aria-label="page 1"
          class="c5"
          data-index="1"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          href="/items/1"
          id="pagination::r1::item:1"
        >
          1
        </a>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r1::ellipsis:1"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <a
          aria-label="page 6"
          class="c5"
          data-index="6"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          href="/items/6"
          id="pagination::r1::item:6"
        >
          6
        </a>
      </div>
      <div
        aria-selected="true"
        class="c2"
      >
        <a
          aria-current="page"
          aria-label="page 7"
          class="c5"
          data-index="7"
          data-part="item"
          data-scope="pagination"
          data-selected=""
          dir="ltr"
          href="/items/7"
          id="pagination::r1::item:7"
        >
          7
        </a>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <a
          aria-label="page 8"
          class="c5"
          data-index="8"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          href="/items/8"
          id="pagination::r1::item:8"
        >
          8
        </a>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r1::ellipsis:5"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <a
          aria-label="last page, page 10"
          class="c5"
          data-index="10"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          href="/items/10"
          id="pagination::r1::item:10"
        >
          10
        </a>
      </div>
      <div
        class="c2"
      >
        <button
          aria-label="next page"
          class="c7"
          data-part="next-trigger"
          data-scope="pagination"
          data-testid="pagination_next"
          dir="ltr"
          id="pagination::r1::next"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
    </div>
  </nav>
</div>
`;

exports[`Pagination > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-11);
  color: var(--ajds-color-character-secondary);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  all: unset;
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: var(--ajds-radius-sm);
  font-size: var(--ajds-font-size-default);
  color: var(--ajds-color-interactive-active-primary);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  cursor: pointer;
}

.c5:disabled {
  cursor: default;
}

.c5:disabled svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c5[data-selected] {
  cursor: default;
  color: var(--ajds-color-character-accent);
  font-weight: var(--ajds-font-weight-bold);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c2 {
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-11);
  margin: var(--ajds-spacing-1) var(--ajds-spacing-1) 0 var(--ajds-spacing-1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.c2[aria-selected='true'] {
  border-bottom: 4px solid var(--ajds-color-character-accent);
}

.c3 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c3[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c3:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c7[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

<div>
  <nav
    aria-label="pagination"
    class="c0"
    data-part="root"
    data-scope="pagination"
    dir="ltr"
    id="pagination::r0:"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <button
          aria-label="previous page"
          class="c3"
          data-part="prev-trigger"
          data-scope="pagination"
          data-testid="pagination_prev"
          dir="ltr"
          id="pagination::r0::prev"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 1"
          class="c5"
          data-index="1"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r0::item:1"
          type="button"
        >
          1
        </button>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r0::ellipsis:1"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 6"
          class="c5"
          data-index="6"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r0::item:6"
          type="button"
        >
          6
        </button>
      </div>
      <div
        aria-selected="true"
        class="c2"
      >
        <button
          aria-current="page"
          aria-label="page 7"
          class="c5"
          data-index="7"
          data-part="item"
          data-scope="pagination"
          data-selected=""
          dir="ltr"
          id="pagination::r0::item:7"
          type="button"
        >
          7
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 8"
          class="c5"
          data-index="8"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r0::item:8"
          type="button"
        >
          8
        </button>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r0::ellipsis:5"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="last page, page 10"
          class="c5"
          data-index="10"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r0::item:10"
          type="button"
        >
          10
        </button>
      </div>
      <div
        class="c2"
      >
        <button
          aria-label="next page"
          class="c7"
          data-part="next-trigger"
          data-scope="pagination"
          data-testid="pagination_next"
          dir="ltr"
          id="pagination::r0::next"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
    </div>
  </nav>
</div>
`;

exports[`Pagination > should navigate with chevron 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-11);
  color: var(--ajds-color-character-secondary);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  all: unset;
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: var(--ajds-radius-sm);
  font-size: var(--ajds-font-size-default);
  color: var(--ajds-color-interactive-active-primary);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  cursor: pointer;
}

.c5:disabled {
  cursor: default;
}

.c5:disabled svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c5[data-selected] {
  cursor: default;
  color: var(--ajds-color-character-accent);
  font-weight: var(--ajds-font-weight-bold);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c2 {
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-11);
  margin: var(--ajds-spacing-1) var(--ajds-spacing-1) 0 var(--ajds-spacing-1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.c2[aria-selected='true'] {
  border-bottom: 4px solid var(--ajds-color-character-accent);
}

.c3 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c3[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c3:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c7[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

<div>
  <nav
    aria-label="pagination"
    class="c0"
    data-part="root"
    data-scope="pagination"
    dir="ltr"
    id="pagination::r2:"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <button
          aria-label="previous page"
          class="c3"
          data-disabled=""
          data-part="prev-trigger"
          data-scope="pagination"
          data-testid="pagination_prev"
          dir="ltr"
          disabled=""
          id="pagination::r2::prev"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
      <div
        aria-selected="true"
        class="c2"
      >
        <button
          aria-current="page"
          aria-label="page 1"
          class="c5"
          data-index="1"
          data-part="item"
          data-scope="pagination"
          data-selected=""
          dir="ltr"
          id="pagination::r2::item:1"
          type="button"
        >
          1
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 2"
          class="c5"
          data-index="2"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r2::item:2"
          type="button"
        >
          2
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 3"
          class="c5"
          data-index="3"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r2::item:3"
          type="button"
        >
          3
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 4"
          class="c5"
          data-index="4"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r2::item:4"
          type="button"
        >
          4
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 5"
          class="c5"
          data-index="5"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r2::item:5"
          type="button"
        >
          5
        </button>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r2::ellipsis:5"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="last page, page 7"
          class="c5"
          data-index="7"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r2::item:7"
          type="button"
        >
          7
        </button>
      </div>
      <div
        class="c2"
      >
        <button
          aria-label="next page"
          class="c7"
          data-part="next-trigger"
          data-scope="pagination"
          data-testid="pagination_next"
          dir="ltr"
          id="pagination::r2::next"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
    </div>
  </nav>
</div>
`;

exports[`Pagination > should navigate with page number 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ajds-spacing-11);
  color: var(--ajds-color-character-secondary);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  all: unset;
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: var(--ajds-radius-sm);
  font-size: var(--ajds-font-size-default);
  color: var(--ajds-color-interactive-active-primary);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  cursor: pointer;
}

.c5:disabled {
  cursor: default;
}

.c5:disabled svg {
  fill: var(--ajds-color-interactive-disabled-dark);
}

.c5[data-selected] {
  cursor: default;
  color: var(--ajds-color-character-accent);
  font-weight: var(--ajds-font-weight-bold);
}

.c5:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c5:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c2 {
  min-width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-11);
  margin: var(--ajds-spacing-1) var(--ajds-spacing-1) 0 var(--ajds-spacing-1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.c2[aria-selected='true'] {
  border-bottom: 4px solid var(--ajds-color-character-accent);
}

.c3 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c3[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c3:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c3:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c7 {
  all: unset;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: var(--ajds-spacing-1);
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
}

.c7[data-disabled] {
  cursor: default;
  color: var(--ajds-color-interactive-disabled-dark);
}

.c7:hover {
  background-color: var(--ajds-color-interactive-hover-grey-transparent);
}

.c7:focus-visible:not(:disabled) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

<div>
  <nav
    aria-label="pagination"
    class="c0"
    data-part="root"
    data-scope="pagination"
    dir="ltr"
    id="pagination::r3:"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <button
          aria-label="previous page"
          class="c3"
          data-part="prev-trigger"
          data-scope="pagination"
          data-testid="pagination_prev"
          dir="ltr"
          id="pagination::r3::prev"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(180deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 1"
          class="c5"
          data-index="1"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r3::item:1"
          type="button"
        >
          1
        </button>
      </div>
      <div
        class="c6"
        data-part="ellipsis"
        data-scope="pagination"
        dir="ltr"
        id="pagination::r3::ellipsis:1"
      >
        …
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 3"
          class="c5"
          data-index="3"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r3::item:3"
          type="button"
        >
          3
        </button>
      </div>
      <div
        aria-selected="false"
        class="c2"
      >
        <button
          aria-label="page 4"
          class="c5"
          data-index="4"
          data-part="item"
          data-scope="pagination"
          dir="ltr"
          id="pagination::r3::item:4"
          type="button"
        >
          4
        </button>
      </div>
      <div
        aria-selected="true"
        class="c2"
      >
        <button
          aria-current="page"
          aria-label="last page, page 5"
          class="c5"
          data-index="5"
          data-part="item"
          data-scope="pagination"
          data-selected=""
          dir="ltr"
          id="pagination::r3::item:5"
          type="button"
        >
          5
        </button>
      </div>
      <div
        class="c2"
      >
        <button
          aria-label="next page"
          class="c7"
          data-disabled=""
          data-part="next-trigger"
          data-scope="pagination"
          data-testid="pagination_next"
          dir="ltr"
          disabled=""
          id="pagination::r3::next"
          type="button"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.332 6.00073L9 7.41073L13.3266 12.0007L9 16.5907L10.332 18.0007L16 12.0007L10.332 6.00073Z"
            />
            ;
          </svg>
        </button>
      </div>
    </div>
  </nav>
</div>
`;

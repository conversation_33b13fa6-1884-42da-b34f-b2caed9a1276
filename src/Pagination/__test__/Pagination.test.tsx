import React, { useState } from 'react';
import { render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Pagination from '../Pagination';

type ComponentsProps = {
  totalItems: number;
  defaultPage: number;
  pageSize: number;
};

const Component: React.FC<ComponentsProps> = ({ totalItems, defaultPage, pageSize }) => {
  const [currentPage, setCurrentPage] = useState(defaultPage);
  const onPageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return <Pagination totalItems={totalItems} currentPage={currentPage} pageSize={pageSize} onPageChange={onPageChange} />;
};

describe('Pagination', () => {
  test('renders without crashing', () => {
    const { getAllByText, queryByText, getByText, container } = render(<Pagination totalItems={20} currentPage={7} pageSize={2} />);

    expect(getAllByText('…').length).toEqual(2);
    expect(queryByText('0')).not.toBeInTheDocument();
    expect(getByText('1')).toBeInTheDocument();
    expect(queryByText('2')).not.toBeInTheDocument();
    expect(queryByText('3')).not.toBeInTheDocument();
    expect(queryByText('4')).not.toBeInTheDocument();
    expect(queryByText('5')).not.toBeInTheDocument();
    expect(getByText('6')).toBeInTheDocument();
    expect(getByText('7')).toHaveAttribute('data-selected', '');
    expect(getByText('8')).toBeInTheDocument();
    expect(queryByText('9')).not.toBeInTheDocument();
    expect(queryByText('10')).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test('renders custom pagination items without crashing', () => {
    const { getAllByText, queryByText, getByText, container } = render(
      <Pagination totalItems={20} currentPage={7} pageSize={2} renderAs={(currentPage) => <a href={`/items/${currentPage}`}>{currentPage}</a>} />,
    );

    expect(getAllByText('…').length).toEqual(2);
    expect(queryByText('0')).not.toBeInTheDocument();
    expect(getByText('1')).toBeInTheDocument();
    expect(getByText('1').closest('a')).toHaveAttribute('href', '/items/1');
    expect(queryByText('2')).not.toBeInTheDocument();
    expect(queryByText('3')).not.toBeInTheDocument();
    expect(queryByText('4')).not.toBeInTheDocument();
    expect(queryByText('5')).not.toBeInTheDocument();
    expect(getByText('6')).toBeInTheDocument();
    expect(getByText('6').closest('a')).toHaveAttribute('href', '/items/6');
    expect(getByText('7')).toHaveAttribute('data-selected', '');
    expect(getByText('7').closest('a')).toHaveAttribute('href', '/items/7');
    expect(getByText('8')).toBeInTheDocument();
    expect(getByText('8').closest('a')).toHaveAttribute('href', '/items/8');
    expect(queryByText('9')).not.toBeInTheDocument();
    expect(queryByText('10')).toBeInTheDocument();
    expect(getByText('10').closest('a')).toHaveAttribute('href', '/items/10');
    expect(container).toMatchSnapshot();
  });

  test('should navigate with chevron', async () => {
    const { getByTestId, getByText, container } = render(<Component totalItems={14} defaultPage={1} pageSize={2} />);

    const last = getByTestId('pagination_prev');
    const next = getByTestId('pagination_next');

    expect(getByText('1')).toHaveAttribute('data-selected');

    await userEvent.click(next);

    expect(getByText('1')).not.toHaveAttribute('data-selected');
    expect(getByText('2')).toHaveAttribute('data-selected');

    await userEvent.click(last);
    expect(getByText('1')).toHaveAttribute('data-selected');
    expect(getByText('2')).not.toHaveAttribute('data-selected');
    expect(container).toMatchSnapshot();
  });

  test('should navigate with page number', async () => {
    const { getByText, container } = render(<Component totalItems={13} defaultPage={1} pageSize={3} />);

    expect(getByText('1')).toHaveAttribute('data-selected');

    await userEvent.click(getByText('3'));

    expect(getByText('1')).not.toHaveAttribute('data-selected');
    expect(getByText('3')).toHaveAttribute('data-selected');

    await userEvent.click(getByText('5'));
    expect(getByText('5')).toHaveAttribute('data-selected');
    expect(getByText('3')).not.toHaveAttribute('data-selected');
    expect(container).toMatchSnapshot();
  });
});

import React from 'react';
import Pagination from '../Pagination';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Pagination', () => {
  test('LargePagination', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '496px' }}>
        <Pagination totalItems={24} defaultPage={7} pageSize={2} />
      </div>,
      task,
    );
  });

  test('LargePagination10000Items', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '530px' }}>
        <Pagination totalItems={24000} defaultPage={11998} pageSize={2} />
      </div>,
      task,
    );
  });

  test('MediumPagination', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '400px' }}>
        <Pagination totalItems={24} defaultPage={7} pageSize={2} />
      </div>,
      task,
    );
  });

  test('MediumPagination10000Items', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '425px' }}>
        <Pagination totalItems={24000} defaultPage={11998} pageSize={2} />
      </div>,
      task,
    );
  });

  test('SmallPagination', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '304px' }}>
        <Pagination totalItems={24} defaultPage={7} pageSize={2} />
      </div>,
      task,
    );
  });

  test('SmallPagination10000Items', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '321px' }}>
        <Pagination totalItems={24000} defaultPage={11998} pageSize={2} />
      </div>,
      task,
    );
  });

  test('ExtraSmallPagination', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '144px' }}>
        <Pagination totalItems={24} defaultPage={7} pageSize={2} />
      </div>,
      task,
    );
  });

  test('ExtraSmallPagination10000Items', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '152px' }}>
        <Pagination totalItems={24000} defaultPage={11998} pageSize={2} />
      </div>,
      task,
    );
  });
});

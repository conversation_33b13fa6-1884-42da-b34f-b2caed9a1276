import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';

export const PaginationItemContainer = styled.div`
  min-width: ${getSpacingVar(10)};
  height: ${getSpacingVar(11)};
  margin: ${getSpacingVar(1)} ${getSpacingVar(1)} 0 ${getSpacingVar(1)};

  &[aria-selected='true'] {
    border-bottom: 4px solid ${getColorVar('characterAccent')};
  }

  user-select: none;
`;

In addition to props above, &lt;TextAreaField&gt; accepts any default props for _textarea_ HTML tags.

### Default TextAreaField:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="basic-textarea" label="Basic TextArea" />;
```

### Required:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="required-textarea" label="Required TextArea" required />;
```

### Without label:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="no-label" />;
```

### Disabled:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="disabled" label="Disabled TextArea" disabled />;
```

### With placeholder:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="placeholder-textarea" label="Placeholder TextArea" placeholder="example example example" />;
```

### With default value:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="default-value-textarea" label="Default value TextArea" defaultValue="example example example" />;
```

### With error:

```js
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

<TextAreaField name="error-textarea" label="TextArea with error" errorMessage="example example example" />;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the underlying &lt;textarea&gt; component.

```js
import React, { useRef } from 'react';
import TextAreaField from '@axa-japan/design-system-react/TextAreaField';

const ref = useRef(null);
<TextAreaField ref={ref} label="Ref" />;
```

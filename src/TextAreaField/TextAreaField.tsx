import React, { forwardRef, useId } from 'react';
import { TextAreaWrapper } from '../TextArea/TextAreaWrapper';
import TextArea from '../TextArea/TextArea';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import checkHasError from '../utils/checkHasError';

export type TextAreaFieldProps = CommonFieldBaseProps & Omit<React.ComponentPropsWithoutRef<'textarea'>, 'rows'>;

const TextAreaField = forwardRef<HTMLTextAreaElement, TextAreaFieldProps>(
  ({ id, name, label, showError, errorMessage, required = false, disabled = false, sx, showRequiredIndicator, ...rest }, ref) => {
    const fallbackId = `text-area-id-${useId()}`;
    const hasError = checkHasError(showError, errorMessage);
    return (
      <FieldBase
        id={id || name || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <TextAreaWrapper aria-invalid={hasError} aria-disabled={disabled}>
          <TextArea
            ref={ref}
            id={id || name || fallbackId}
            disabled={disabled}
            hasError={hasError}
            name={name}
            required={required}
            {...rest}
            style={undefined}
          />
        </TextAreaWrapper>
      </FieldBase>
    );
  },
);

TextAreaField.displayName = 'TextAreaField';

export default React.memo(TextAreaField);

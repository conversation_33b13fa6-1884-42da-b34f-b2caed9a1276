// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TextAreaField > renders prop disabled false, without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-invalid="false"
        class="c2"
      >
        <textarea
          aria-invalid="false"
          class="c3 c4"
          id="text-area-id-:r2:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaField > renders prop disabled true, without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="true"
        aria-invalid="false"
        class="c2"
      >
        <textarea
          aria-invalid="false"
          class="c3 c4"
          disabled=""
          id="text-area-id-:r1:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaField > renders prop showError false, without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-invalid="false"
        class="c2"
      >
        <textarea
          aria-invalid="false"
          class="c3 c4"
          id="text-area-id-:r4:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaField > renders prop showError true, without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-invalid="true"
        class="c2"
      >
        <textarea
          aria-invalid="true"
          class="c3 c4"
          id="text-area-id-:r3:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaField > renders prop sx without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
    style="--ajds-FieldWrapBase-margin: var(--ajds-spacing-1);"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-invalid="false"
        class="c2"
      >
        <textarea
          aria-invalid="false"
          class="c3 c4"
          id="text-area-id-:r5:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaField > renders without crashing 1`] = `
.c2 {
  border-radius: var(--ajds-radius-sm);
  border: 1px solid var(--ajds-color-utility-stroke-dark);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.c2[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c2:focus-within:not([aria-disabled='true']) {
  box-shadow: 0 0 0 2px var(--ajds-color-interactive-focus-white), 0 0 0 4px var(--ajds-color-interactive-focus-primary);
}

.c2[aria-disabled='true'] {
  border: 1px solid var(--ajds-color-interactive-disabled-dark);
}

.c4 {
  background-color: var(--ajds-color-interactive-active-white);
  border-radius: var(--ajds-radius-sm);
  border: none;
  outline: none;
  width: 100%;
  max-width: 100%;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-3-5);
  resize: none;
}

.c4::-webkit-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::-moz-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:-ms-input-placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4::placeholder {
  color: var(--ajds-color-interactive-placeholder);
}

.c4:hover:not(:disabled) {
  background-color: var(--ajds-color-interactive-hover-grey);
}

.field-input-wrap:not(:hover) .c3:hover:not(:disabled) {
  background-color: var(--ajds-color-utility-background-white);
}

.c4:disabled {
  background-color: var(--ajds-color-interactive-disabled-light);
  color: var(--ajds-color-interactive-disabled-dark);
  cursor: not-allowed;
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c1 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c1 input,
.c1 select,
.c1 button,
.c1 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c1 input {
  overflow: visible;
}

.c1 select {
  text-transform: none;
}

.c1 textarea {
  overflow: auto;
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1 field-input-wrap"
    >
      <div
        aria-disabled="false"
        aria-invalid="false"
        class="c2"
      >
        <textarea
          aria-invalid="false"
          class="c3 c4"
          id="text-area-id-:r0:"
          rows="6"
        />
      </div>
    </div>
  </div>
</div>
`;

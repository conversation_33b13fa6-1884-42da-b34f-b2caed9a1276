import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../TextAreaField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('TextAreaField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<TextAreaField label="default" />, task);
  });

  test('DefaultFocusWithRequired', async ({ task }) => {
    await takeScreenshot(
      <div style={{ margin: '5px' }}>
        <TextAreaField label="focus with required" required />
      </div>,
      task,
      {
        interactionSelector: 'textarea',
        interactionType: 'focus',
      },
    );
  });

  test('Required', async ({ task }) => {
    await takeScreenshot(<TextAreaField required label="required" />, task);
  });

  test('NoLabel', async ({ task }) => {
    await takeScreenshot(<TextAreaField />, task);
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(<TextAreaField disabled label="disabled" />, task);
  });

  test('Placeholder', async ({ task }) => {
    await takeScreenshot(<TextAreaField label="placeholder" placeholder="some placeholder text" />, task);
  });

  test('DefaultValue', async ({ task }) => {
    await takeScreenshot(<TextAreaField label="default value" placeholder="Default value" />, task);
  });

  test('Error', async ({ task }) => {
    await takeScreenshot(<TextAreaField label="error" errorMessage="danger danger danger!!!" />, task);
  });
});

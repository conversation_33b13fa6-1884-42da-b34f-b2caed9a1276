import React from 'react';
import { render } from '../../utils/testUtils';
import Text<PERSON>reaField from '../TextAreaField';

describe('TextAreaField', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<TextAreaField />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  [true, false].forEach((disabled) => {
    test(`renders prop disabled ${disabled}, without crashing`, () => {
      const { getByRole, container } = render(<TextAreaField disabled={disabled} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  [true, false].forEach((showError) => {
    test(`renders prop showError ${showError}, without crashing`, () => {
      const { getByRole, container } = render(<TextAreaField showError={showError} />);
      const testElement = getByRole('textbox');
      expect(testElement).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders prop sx without crashing`, () => {
    const { getByRole, container } = render(<TextAreaField sx={{ margin: 1 }} />);
    const testElement = getByRole('textbox');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

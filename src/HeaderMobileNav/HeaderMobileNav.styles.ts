import styled from 'styled-components';
import { getColorVar } from '../colors';

import media from '../Breakpoints/Breakpoints';

export const HeaderMobileNavBase = styled.div`
  background-color: ${getColorVar('utilityBackgroundWhite')};
  margin-top: 0;
  position: relative;

  ${media.smallOnly} {
    height: 60px;
    padding: 0 14px;
  }

  ${media.mediumOnly} {
    height: 70px;
    padding: 0 40px;
  }

  ${media.largeUp} {
    display: none;
  }
`;

export const HeaderMobileNavInner = styled.div`
  align-items: center;
  display: flex;
  flex-flow: row nowrap;
  height: 100%;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1440px;
`;

import React, { useState } from 'react';
import OffCanvasNav from '../OffCanvasNav';
import Header<PERSON>ogo from '../HeaderLogo';
import OffCanvasNavButton from '../OffCanvasNavButton';
import { HeaderMobileNavBase, HeaderMobileNavInner } from './HeaderMobileNav.styles';

export type HeaderMobileNavProps = {
  /** URL for where you want the header logo link to go to */
  headerLogoTo?: string;
  /** Custom image for header logo */
  headerLogoImage?: React.ReactElement;
  /** Choose to use react router for header logo */
  headerLogoUseRouter?: boolean;
} & React.ComponentPropsWithoutRef<'div'>;

const HeaderMobileNav: React.FC<React.PropsWithChildren<HeaderMobileNavProps>> = ({
  headerLogoTo = '/',
  headerLogoImage,
  headerLogoUseRouter = true,
  children,
  ...rest
}) => {
  const [isNavOpen, setIsNavOpen] = useState(false);

  return (
    <HeaderMobileNavBase {...rest}>
      <HeaderMobileNavInner>
        <HeaderLogo useRouter={headerLogoUseRouter} to={headerLogoTo} logo={headerLogoImage} />
        <OffCanvasNavButton isNavOpen={isNavOpen} onClick={(): void => setIsNavOpen(!isNavOpen)} />
        <OffCanvasNav isNavOpen={isNavOpen}>{children}</OffCanvasNav>
      </HeaderMobileNavInner>
    </HeaderMobileNavBase>
  );
};

export default HeaderMobileNav;

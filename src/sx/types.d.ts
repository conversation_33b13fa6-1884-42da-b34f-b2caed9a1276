import { BackgroundColorSxPropType, ValidBackgroundColorValue } from './attributes/backgroundColor';
import type { SpacingKeysType } from '../spacing';
import type { ValidMarginValue, MarginSxPropType } from './attributes/margin';
import type { ValidPaddingValue, PaddingSxPropType } from './attributes/padding';

/**
 * Valid values for sx prop.
 */
export type ValidPropValueType = ValidPaddingValue | ValidMarginValue | ValidBackgroundColorValue;

/**
 * Combination type of all the prop names.
 */
export type ValidPropNameType = keyof PaddingSxPropType | keyof MarginSxPropType | keyof BackgroundColorSxPropType;

/**
 * Type for all the props that change the look of a component.
 * For example, a Button might have a color prop, as well as a size prop.
 * VariantPropNames should be a union of all the prop names.
 */
export type VariantsType<VariantPropNames> = Record<
  VariantPropNames,
  {
    [key: string]: Record<string, string | string[]>;
  }
>;

/**
 * Type for sx prop for components.
 */
export type SxPropType = Partial<PaddingSxPropType & MarginSxPropType & BackgroundColorSxPropType>;

export type SxRulesType = Array<'padding' | 'margin' | 'background-color'>;

/**
 * Valid values for margin attributes set with sx prop
 */
export type MarginSizesType = SpacingKeysType;

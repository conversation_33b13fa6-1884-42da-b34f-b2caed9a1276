import sx from '../sx';
import { getColorVar } from '../../colors';
import type { VariantsType } from '../types';

describe('getSxStyleRules', () => {
  test('it will return the rules for background-color property', () => {
    const { getSxStyleRules } = sx('MyComponent', ['background-color']);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
            background-color: var(--ajds-MyComponent-background-color);
          "
      `);
  });

  test('it will return the rules for padding property', () => {
    const { getSxStyleRules } = sx('MyComponent', ['padding']);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
            padding: var(--ajds-MyComponent-padding-top, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-right, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-bottom, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-left, var(--ajds-MyComponent-padding, 0));
          "
      `);
  });

  test('it will return the rules for margin property', () => {
    const { getSxStyleRules } = sx('MyComponent', ['margin']);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
            margin: var(--ajds-MyComponent-margin-top, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-right, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-bottom, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-left, var(--ajds-MyComponent-margin, 0));
          "
      `);
  });

  test('it will return the rules for multiple properties', () => {
    const { getSxStyleRules } = sx('MyComponent', ['margin', 'padding']);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
            margin: var(--ajds-MyComponent-margin-top, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-right, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-bottom, var(--ajds-MyComponent-margin, 0)) var(--ajds-MyComponent-margin-left, var(--ajds-MyComponent-margin, 0));
          
            padding: var(--ajds-MyComponent-padding-top, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-right, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-bottom, var(--ajds-MyComponent-padding, 0)) var(--ajds-MyComponent-padding-left, var(--ajds-MyComponent-padding, 0));
          "
      `);
  });

  test('it will return an empty string, if no sxRules are passed', () => {
    const { getSxStyleRules } = sx('MyComponent');
    expect(getSxStyleRules()).toEqual('');
  });

  test('it will handle normal attributes', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          'background-color': getColorVar('white'),
        },
        variant2: {
          'background-color': getColorVar('utilityBackgroundOcean'),
        },
      },
    };
    const { getSxStyleRules } = sx('MyComponent', [], variants);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
                    background-color: var(--ajds-MyComponent-background-color);
                  "
      `);
  });

  test('it will handle attributes with pseudo classes', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          'background-color:hover': getColorVar('white'),
          'background-color:disabled': getColorVar('grey500'),
        },
        variant2: {
          'background-color:hover': getColorVar('interactiveHoverPrimary'),
        },
      },
    };
    const { getSxStyleRules } = sx('MyComponent', [], variants);
    expect(getSxStyleRules()).toMatchInlineSnapshot(`
        "
            @media (hover: hover) {
              &:hover {
                background-color: var(--ajds-MyComponent-background-color-hover);
              }
            }
            
          &:disabled {
            background-color: var(--ajds-MyComponent-background-color-disabled);
          }"
      `);
  });

  test('it will return an empty string, if passed sxRule is not valid', () => {
    // @ts-ignore passing in bad input
    const { getSxStyleRules } = sx('MyComponent', ['marginn']);
    expect(getSxStyleRules()).toEqual('');
  });
});

import sx from '../sx';
import type { ValidPropNameType } from '../types';

describe('setDefaultSxValue', () => {
  test(`it will return background-color rules for background-color`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['background-color']);
    expect(setDefaultSxValue(`background-color` as ValidPropNameType, 'utilityBackgroundGrey')).toEqual(
      `--ajds-MyComponent-background-color: var(--ajds-color-utility-background-grey);`,
    );
  });

  ['top', 'right', 'bottom', 'left'].forEach((side) => {
    test(`it will return a single margin rule for margin ${side}`, () => {
      const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
      expect(setDefaultSxValue(`margin-${side}` as ValidPropNameType, 1)).toEqual(`--ajds-MyComponent-margin-${side}: var(--ajds-spacing-1);`);
    });
  });

  test(`it will return margin rules for margin`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    expect(setDefaultSxValue(`margin` as ValidPropNameType, 1)).toEqual(`--ajds-MyComponent-margin: var(--ajds-spacing-1);`);
  });

  test(`it will return empty string for margin if passed an invalid value`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    // @ts-ignore passing an invalid value
    expect(setDefaultSxValue(`margin` as ValidPropNameType, 9000)).toEqual('');
  });

  ['top', 'right', 'bottom', 'left'].forEach((side) => {
    test(`it will return a single padding rule for padding ${side}`, () => {
      const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
      expect(setDefaultSxValue(`padding-${side}` as ValidPropNameType, 1)).toEqual(`--ajds-MyComponent-padding-${side}: var(--ajds-spacing-1);`);
    });
  });

  test(`it will return padding rules for padding`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    expect(setDefaultSxValue(`padding` as ValidPropNameType, 1)).toEqual(`--ajds-MyComponent-padding: var(--ajds-spacing-1);`);
  });

  test(`it will return empty string for padding if passed an invalid value`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    // @ts-ignore passing an invalid value
    expect(setDefaultSxValue(`padding` as ValidPropNameType, 9000)).toEqual('');
  });

  test(`it will return responsive rules if passed an array of values for padding`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    expect(setDefaultSxValue('padding' as ValidPropNameType, [1, 2, 3])).toMatchInlineSnapshot(`
        "
            (max-width: 899px) {
              --ajds-MyComponent-padding: var(--ajds-spacing-1);
            }
            
            (min-width: 900px) {
              --ajds-MyComponent-padding: var(--ajds-spacing-2);
            }
            
            (min-width: 1280px) {
              --ajds-MyComponent-padding: var(--ajds-spacing-3);
            }
            "
      `);
  });

  test(`it will return responsive rules if passed an array of values for margin`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    expect(setDefaultSxValue('margin' as ValidPropNameType, [1, 2, 3])).toMatchInlineSnapshot(`
        "
            (max-width: 899px) {
              --ajds-MyComponent-margin: var(--ajds-spacing-1);
            }
            
            (min-width: 900px) {
              --ajds-MyComponent-margin: var(--ajds-spacing-2);
            }
            
            (min-width: 1280px) {
              --ajds-MyComponent-margin: var(--ajds-spacing-3);
            }
            "
      `);
  });

  test(`it will return a single rule, if passed an array of 1 value`, () => {
    const { setDefaultSxValue } = sx('MyComponent', ['margin', 'padding']);
    expect(setDefaultSxValue('margin' as ValidPropNameType, [1])).toEqual('--ajds-MyComponent-margin: var(--ajds-spacing-1);');
  });
});

import { renderHook } from '@testing-library/react';
import sx from '../sx';
import { getColorVar } from '../../colors';
import type { VariantsType, SxPropType } from '../types';
import { getSpacingVar } from '../../spacing';

describe('useSx', () => {
  test('it will return background color rule when `background-color` is passed', () => {
    let sxProp: SxPropType = {
      'background-color': 'utilityBackgroundGrey',
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-background-color': 'var(--ajds-color-utility-background-grey)',
    });

    sxProp = {
      'background-color': 'utilityBackgroundOcean',
    };
    const { result: result2 } = renderHook(() => useSx(sxProp));
    expect(result2.current).toMatchObject({
      '--ajds-MyComponent-background-color': 'var(--ajds-color-utility-background-ocean)',
    });
  });

  test('it will return margin rules for all sides when `margin` is passed', () => {
    let sxProp: SxPropType = {
      margin: 0,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-0)',
    });

    sxProp = {
      margin: 1,
    };
    const { result: result2 } = renderHook(() => useSx(sxProp));
    expect(result2.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-1)',
    });
  });

  test('it will merge multiple individual margin declarations', () => {
    const sxProp: SxPropType = {
      'margin-top': 0,
      'margin-bottom': 1,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin-bottom': 'var(--ajds-spacing-1)',
      '--ajds-MyComponent-margin-top': 'var(--ajds-spacing-0)',
    });
  });

  test('it will merge multiple individual margin declarations with `margin`', () => {
    let sxProp: SxPropType = {
      margin: 0,
      'margin-top': 1,
      'margin-bottom': 2,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-0)',
      '--ajds-MyComponent-margin-top': 'var(--ajds-spacing-1)',
      '--ajds-MyComponent-margin-bottom': 'var(--ajds-spacing-2)',
    });

    sxProp = {
      margin: 0,
      'margin-top': 1,
      'margin-bottom': 2,
    };

    const { result: result2 } = renderHook(() => useSx(sxProp));
    expect(result2.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-0)',
      '--ajds-MyComponent-margin-bottom': 'var(--ajds-spacing-2)',
      '--ajds-MyComponent-margin-top': 'var(--ajds-spacing-1)',
    });
  });

  test('it will both margin and padding rules', () => {
    let sxProp: SxPropType = {
      margin: 0,
      padding: 1,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-0)',
      '--ajds-MyComponent-padding': 'var(--ajds-spacing-1)',
    });

    sxProp = {
      'margin-left': 0,
      'padding-top': 1.5,
      'padding-bottom': 2,
    };

    const { result: result2 } = renderHook(() => useSx(sxProp));
    expect(result2.current).toMatchObject({
      '--ajds-MyComponent-margin-left': 'var(--ajds-spacing-0)',
      '--ajds-MyComponent-padding-top': 'var(--ajds-spacing-1-5)',
      '--ajds-MyComponent-padding-bottom': 'var(--ajds-spacing-2)',
    });
  });

  test('it will return an empty object, if no sx prop is passed', () => {
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx());
    expect(result).toMatchObject({});
  });

  test('it will build responsive rules if passed an array', () => {
    const sxProp: SxPropType = {
      'margin-top': [0, 1, 2],
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin-top': 'var(--ajds-spacing-0)',
    });
  });

  test('it will build responsive rules if several attributes, if passed an array', () => {
    const sxProp: SxPropType = {
      'margin-top': [0, 1, 2],
      'padding-top': [0, 1, 2],
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin-top': 'var(--ajds-spacing-0)',
      '--ajds-MyComponent-padding-top': 'var(--ajds-spacing-0)',
    });
  });

  test('it will ignore an invalid value for sx prop', () => {
    const sxProp: SxPropType = {
      // @ts-ignore margin should not have a value of 100
      margin: 100,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({});
  });

  test('it will ignore an invalid prop name for sx prop', () => {
    const sxProp: SxPropType = {
      // @ts-ignore "margins" is an invalid prop name
      margins: 1,
    };
    const { useSx } = sx('MyComponent');
    const { result } = renderHook(() => useSx(sxProp));
    expect(result.current).toMatchObject({});
  });

  test('it will build rules for a variant', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          color: getColorVar('axaBlue400'),
          'background-color': getColorVar('white'),
        },
        variant2: {
          color: getColorVar('white'),
          'background-color': getColorVar('axaBlue400'),
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant1' }));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-background-color': 'var(--ajds-color-white)',
      '--ajds-MyComponent-color': 'var(--ajds-color-axa-blue-400)',
    });
  });

  test('it will build responsive rules for a variant', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          padding: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant1' }));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-padding': 'var(--ajds-spacing-1)',
    });
  });

  test('it will merge responsive rules for a variant', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
          margin: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          padding: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
          margin: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant1' }));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-1)',
      '--ajds-MyComponent-padding': 'var(--ajds-spacing-1)',
    });
  });

  test('it will merge responsive rules for across variants', () => {
    const variants: VariantsType<'someVariant' | 'someVariant2'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          padding: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
      someVariant2: {
        variant1: {
          margin: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          margin: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant1', someVariant2: 'variant2' }));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-margin': 'var(--ajds-spacing-2)',
      '--ajds-MyComponent-padding': 'var(--ajds-spacing-1)',
    });
  });

  test('it will return a single rules if only one value is provided', () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1)],
        },
        variant2: {
          padding: [getSpacingVar(2)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant1' }));
    expect(result.current).toMatchObject({
      '--ajds-MyComponent-padding': 'var(--ajds-spacing-1)',
    });
  });

  test('it will return empty rules if there are no variants', () => {
    const { useSx } = sx('MyComponent', []);
    const { result } = renderHook(() => useSx({}));
    expect(result.current).toMatchObject({});
  });

  test("it will ignore props that aren't a valid variant", () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          padding: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    // @ts-ignore intentionally passing a bad type
    const { result } = renderHook(() => useSx({}, { someVariant2: 'variant1' }));
    expect(result.current).toMatchObject({});
  });

  test("it will ignore values for props that aren't valid for a variant", () => {
    const variants: VariantsType<'someVariant'> = {
      someVariant: {
        variant1: {
          padding: [getSpacingVar(1), getSpacingVar(2), getSpacingVar(3)],
        },
        variant2: {
          padding: [getSpacingVar(2), getSpacingVar(3), getSpacingVar(4)],
        },
      },
    };
    const { useSx } = sx('MyComponent', [], variants);
    const { result } = renderHook(() => useSx({}, { someVariant: 'variant3' }));
    expect(result.current).toMatchObject({});
  });
});

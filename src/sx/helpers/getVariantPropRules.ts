import getResponsiveValue from './getResponsiveValue';
import type { VariantsType } from '../types';

/**
 * Processes a variant prop, building an object of css rules
 * @returns An object to use for style prop
 */
function getVariantPropRules<VariantPropNames extends string>(
  componentName: string,
  screenSize: 'small' | 'medium' | 'large',
  variantsConfig?: VariantsType<VariantPropNames>,
  variants?: Record<VariantPropNames, string>,
): Record<string, string | Record<string, string>> {
  if (!(variantsConfig && variants)) {
    return {};
  }

  // Loop through each of the variant props, and look for matching React prop
  return (Object.keys(variants) as VariantPropNames[]).reduce((acc: Record<string, string | Record<string, string>>, variantsKey) => {
    const modifierConfig = variantsConfig[variantsKey];
    // Return early if there is no config for the prop variant
    if (!modifierConfig || !modifierConfig[variants[variantsKey]]) {
      return acc;
    }

    const variant = modifierConfig[variants[variantsKey]];
    // Loop through each property in the variant set by the prop, and build the css vars for that variant
    Object.keys(variant).forEach((variantRuleKey) => {
      // Normalize variant rule key for CSS variable naming
      // Handle square brackets and other special characters properly
      const normalizedVariantRuleKey = variantRuleKey
        .replace(/:/g, '-')
        .replace(/\[/g, '-')
        .replace(/\]/g, '-')
        .replace(/"/g, '')
        .replace(/=/g, '-')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-') // Replace multiple consecutive dashes with single dash
        .replace(/^-|-$/g, ''); // Remove leading/trailing dashes
      const ruleName = `--ajds-${componentName}-${normalizedVariantRuleKey}`;
      const ruleValue = variant[variantRuleKey];
      if (Array.isArray(ruleValue)) {
        acc[ruleName] = getResponsiveValue(ruleValue, screenSize);
      } else {
        acc[ruleName] = ruleValue;
      }
    });
    return acc;
  }, {});
}

export default getVariantPropRules;

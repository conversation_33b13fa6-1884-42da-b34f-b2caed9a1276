import { getMarginStyleRule } from '../attributes/margin';
import { getPaddingStyleRules } from '../attributes/padding';
import { getBackgroundColorStyleRule } from '../attributes/backgroundColor';
import type { SxRulesType } from '../types';

const noop = () => '';

/**
 * Map for getting style attributes for sx props.
 */
const sxPropStyleMap = {
  'background-color': getBackgroundColorStyleRule,
  padding: getPaddingStyleRules,
  margin: getMarginStyleRule,
};

/**
 * Processes sx rules, building the css rules for overrides with sx prop
 * @returns css rule set
 */
function getSxCssRules(componentName: string, sxRules?: SxRulesType): string {
  return sxRules
    ? sxRules.reduce((acc, sxRule) => {
        return acc + (sxPropStyleMap[sxRule] || noop)(componentName);
      }, '')
    : '';
}

export default getSxCssRules;

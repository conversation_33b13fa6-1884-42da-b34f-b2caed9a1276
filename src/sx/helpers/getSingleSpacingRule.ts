import { getSpacingVar } from '../../spacing';
import type { SpacingKeysType } from '../../spacing';

/**
 *
 * Creates a namespaced css variable declaration.
 * @returns css variable declaration
 */
function getSingleSpacingRule(componentName: string, value: SpacingKeysType, propName: string): Record<string, string> {
  return { [`--ajds-${componentName}-${propName}`]: getSpacingVar(value) };
}

export default getSingleSpacingRule;

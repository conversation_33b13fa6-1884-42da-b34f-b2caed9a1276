import { mediaQueries } from '../../Breakpoints/Breakpoints';

/**
 * Process an array value for a sx prop.
 * @returns A responsive rule set for an sx prop
 */
function createResponsiveRule<T>(ruleName: string, values: T[], getVarFromValue: (value: T) => string) {
  // You could technically pass an array with just one value, in which case it's not really responsive
  if (values.length === 1) {
    return `${ruleName}: ${getVarFromValue(values[0])};`;
  }

  // Or else loop through array, and build responsive rules
  return values.reduce((acc, value, index) => {
    return `${acc}
    ${mediaQueries[index]} {
      ${ruleName}: ${getVarFromValue(value)};
    }
    `;
  }, '');
}

export default createResponsiveRule;

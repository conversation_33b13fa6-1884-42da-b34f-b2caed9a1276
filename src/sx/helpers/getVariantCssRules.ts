import getPseudoClassRule from './getPseudoClassRule';
import type { VariantsType } from '../types';

function getRule(componentName: string, variantRuleKey: string): string {
  // Normalize variant rule key for CSS variable naming
  // Handle square brackets and other special characters properly
  const normalizedVariantRuleKey = variantRuleKey
    .replace(/:/g, '-')
    .replace(/\[/g, '-')
    .replace(/\]/g, '-')
    .replace(/"/g, '')
    .replace(/=/g, '-')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-') // Replace multiple consecutive dashes with single dash
    .replace(/^-|-$/g, ''); // Remove leading/trailing dashes

  // Namespaced css variable to set variant rule values via style prop in the component
  const variantRuleValue = `var(--ajds-${componentName}-${normalizedVariantRuleKey})`;

  // Handle special case for pseudo classes
  return variantRuleKey.includes(':')
    ? getPseudoClassRule(variantRuleKey, variantRuleValue)
    : `
            ${variantRuleKey}: ${variantRuleValue};
          `;
}

/**
 * Processes variant rules, building the css rules for overrides with variant prop
 * @returns css rule set
 */
function getVariantCssRules<VariantPropNames>(componentName: string, variants?: VariantsType<VariantPropNames>): string {
  if (!variants) {
    return '';
  }

  return (Object.keys(variants) as VariantPropNames[]).reduce((acc, variantKey) => {
    let rules = '';
    const variantConfig = variants[variantKey];
    // Handle case when incorrect
    if (!variantConfig) {
      return acc;
    }
    // First variant in the configuration object will be the default
    const firstVariant = variantConfig[Object.keys(variantConfig)[0]];
    Object.keys(firstVariant).forEach((variantRuleKey) => {
      rules += getRule(componentName, variantRuleKey);
    });
    return acc + rules;
  }, '');
}

export default getVariantCssRules;

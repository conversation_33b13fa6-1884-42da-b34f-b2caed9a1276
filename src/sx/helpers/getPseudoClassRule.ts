/**
 * Processes rules like `background-color:hover` to make them valid css.
 * @returns Rule set for pseudo classes (like :hover/:focus) and attribute selectors (like [aria-disabled="true"])
 */
function getPseudoClassRule(rule: string, value: string) {
  const [ruleToUse, effect] = rule.split(':');

  // Handle attribute selectors (e.g., [aria-disabled="true"])
  if (effect && effect.includes('[') && effect.includes(']')) {
    return `
  &${effect} {
    ${ruleToUse}: ${value};
  }`;
  }

  if (rule.includes('hover')) {
    // @media (hover: hover) prevents hover styles on mobile devices
    return `
    @media (hover: hover) {
      &:hover {
        ${ruleToUse}: ${value};
      }
    }
    `;
  }
  return `
  &:${effect} {
    ${ruleToUse}: ${value};
  }`;
}

export default getPseudoClassRule;

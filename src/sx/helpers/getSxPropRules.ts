import getResponsiveValue from './getResponsiveValue';
import { setOverrideMarginValueMap } from '../attributes/margin';
import { setOverridePaddingValueMap } from '../attributes/padding';
import { setOverrideBackgroundColorValueMap } from '../attributes/backgroundColor';
import type { SxPropType } from '../types';

const noop = () => ({});

/**
 * Processes a sx prop, building an object of css rules
 * @returns An object to use for style prop
 */
function getSxPropRules(
  componentName: string,
  screenSize: 'small' | 'medium' | 'large',
  sxProp?: SxPropType,
): Record<string, string | Record<string, string>> {
  /**
   * Map for getting the override rules from sx prop
   */
  const sxOverrideStyleMap = {
    ...setOverrideBackgroundColorValueMap,
    ...setOverrideMarginValueMap,
    ...setOverridePaddingValueMap,
  };

  // TODO: Fix Typescript errors
  return sxProp
    ? (Object.keys(sxProp) as Array<keyof SxPropType>).reduce(
        (acc, propName) => {
          const propValue = sxProp[propName];

          // Disregard any prop/value that isn't valid
          if (propValue === undefined || propValue === null) {
            return acc;
          }

          // Get responsive styles for array values
          if (Array.isArray(propValue)) {
            return {
              ...acc,
              // @ts-ignore Temporarily disable this annoying TS error
              ...(sxOverrideStyleMap[propName] || noop)(`${componentName}`, getResponsiveValue(propValue, screenSize), propName),
            };
          }

          // Get single style
          return {
            ...acc,
            // @ts-ignore Temporarily disable this annoying TS error
            ...(sxOverrideStyleMap[propName] || noop)(`${componentName}`, propValue, propName),
          };
        },
        {} as Record<string, string | Record<string, string>>,
      )
    : {};
}

export default getSxPropRules;

import { getColorVar } from '../../colors';
import getSingleColorRule from '../helpers/getSingleColorRule';
import { type ValidPropValueType } from '../types';

export type BackgroundColorType =
  | 'utilityBackgroundWhite'
  | 'utilityBackgroundLight'
  | 'utilityBackgroundOcean'
  | 'utilityBackgroundGrey'
  | 'statusInformationLight'
  | 'statusSuccessLight'
  | 'statusDangerLight'
  | 'statusWarningLight';

/**
 * Valid values of background-color sx prop
 */
export type ValidBackgroundColorValue = BackgroundColorType;

/**
 * Valid names of background-color sx prop
 */
export type BackgroundColorPropName = 'background-color';

/**
 * Object type of background-color sx prop key/values pairs
 */
export type BackgroundColorSxPropType = Partial<Record<BackgroundColorPropName, ValidBackgroundColorValue>>;

/**
 * All the valid values for Background Color sx prop
 */
const validBackgroundColorValue: BackgroundColorType[] = [
  'utilityBackgroundWhite',
  'utilityBackgroundLight',
  'utilityBackgroundOcean',
  'utilityBackgroundGrey',
];

/**
 * Gets the style rule for background-color
 * @returns css overrides rule for background-color
 */
export function getBackgroundColorStyleRule(componentName: string) {
  return `
    background-color: var(--ajds-${componentName}-background-color);
  `;
}

/**
 * Helper function to set default background-color
 * @returns default css style rule for background-color
 */
function setDefaultBackgroundColorValue(componentName: string, propName: string, propValue: ValidPropValueType) {
  if (!validBackgroundColorValue.includes(propValue as ValidBackgroundColorValue) && !Array.isArray(propValue)) {
    return '';
  }
  return `--ajds-${componentName}-${propName}: ${getColorVar(propValue as ValidBackgroundColorValue)};`;
}

/**
 * Map for dynamically creating default background-color style rules
 */
export const setDefaultBackgroundColorValueMap: Record<BackgroundColorPropName, typeof setDefaultBackgroundColorValue> = {
  'background-color': setDefaultBackgroundColorValue,
};

/**
 * Map for dynamically creating default background-color style rule overrides
 */
export const setOverrideBackgroundColorValueMap: Record<BackgroundColorPropName, typeof getSingleColorRule> = {
  'background-color': getSingleColorRule,
};

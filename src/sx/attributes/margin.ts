import { spacingKeys, getSpacingVar, SpacingKeysType } from '../../spacing';
import type { MarginSizesType, ValidPropValueType } from '../types';
import getSingleSpacingRule from '../helpers/getSingleSpacingRule';
import createResponsiveRule from '../helpers/createResponsiveRule';

/**
 * Valid values of margin sx prop
 */
export type ValidMarginValue = MarginSizesType | MarginSizesType[];

/**
 * Valid names of margin sx prop
 */
export type MarginPropName = 'margin' | 'margin-top' | 'margin-right' | 'margin-bottom' | 'margin-left';

/**
 * Object type of margin sx prop key/values pairs
 */
export type MarginSxPropType = Partial<Record<MarginPropName, ValidMarginValue>>;

/**
 * All the valid values for margin sx prop
 */
export const validMarginValue: MarginSizesType[] = [...spacingKeys, 'auto'];

/**
 * Gets the style rule for margin
 * @returns css overrides rule for margin
 */
export function getMarginStyleRule(componentName: string) {
  return `
    margin: var(--ajds-${componentName}-margin-top, var(--ajds-${componentName}-margin, 0)) var(--ajds-${componentName}-margin-right, var(--ajds-${componentName}-margin, 0)) var(--ajds-${componentName}-margin-bottom, var(--ajds-${componentName}-margin, 0)) var(--ajds-${componentName}-margin-left, var(--ajds-${componentName}-margin, 0));
  `;
}

/**
 * Helper function to set default margin
 * @returns default css style rule for margin
 */
function setDefaultMarginValue<T extends string | number>(componentName: string, propName: T, propValue: ValidPropValueType) {
  if (!spacingKeys.includes(propValue as SpacingKeysType) && !Array.isArray(propValue)) {
    return '';
  }
  const ruleName = `--ajds-${componentName}-${propName}`;
  if (Array.isArray(propValue)) {
    return createResponsiveRule(ruleName, propValue, getSpacingVar);
  }
  return `--ajds-${componentName}-${propName}: ${getSpacingVar(propValue as SpacingKeysType)};`;
}

/**
 * Map for dynamically creating default margin style rules
 */
export const setDefaultMarginValueMap: Record<MarginPropName, typeof setDefaultMarginValue> = {
  margin: setDefaultMarginValue,
  'margin-bottom': setDefaultMarginValue,
  'margin-left': setDefaultMarginValue,
  'margin-right': setDefaultMarginValue,
  'margin-top': setDefaultMarginValue,
};

/**
 * Map for dynamically creating default margin style rule overrides
 */
export const setOverrideMarginValueMap: Record<MarginPropName, typeof getSingleSpacingRule> = {
  margin: getSingleSpacingRule,
  'margin-bottom': getSingleSpacingRule,
  'margin-left': getSingleSpacingRule,
  'margin-right': getSingleSpacingRule,
  'margin-top': getSingleSpacingRule,
};

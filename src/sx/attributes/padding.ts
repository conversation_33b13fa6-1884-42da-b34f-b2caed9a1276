import { spacingKeys, getSpacingVar } from '../../spacing';
import type { SpacingKeysType } from '../../spacing';
import type { ValidPropValueType } from '../types';
import getSingleSpacingRule from '../helpers/getSingleSpacingRule';
import createResponsiveRule from '../helpers/createResponsiveRule';

/**
 * Valid values of padding sx prop
 */
export type ValidPaddingValue = SpacingKeysType | SpacingKeysType[];

/**
 * Valid names of padding sx prop
 */
export type PaddingPropName = 'padding' | 'padding-top' | 'padding-right' | 'padding-bottom' | 'padding-left';

/**
 * Object type of padding sx prop key/values pairs
 */
export type PaddingSxPropType = Partial<Record<PaddingPropName, ValidPaddingValue>>;

/**
 * Gets the style rule for padding
 * @returns css overrides rule for padding
 */
export function getPaddingStyleRules(componentName: string) {
  return `
    padding: var(--ajds-${componentName}-padding-top, var(--ajds-${componentName}-padding, 0)) var(--ajds-${componentName}-padding-right, var(--ajds-${componentName}-padding, 0)) var(--ajds-${componentName}-padding-bottom, var(--ajds-${componentName}-padding, 0)) var(--ajds-${componentName}-padding-left, var(--ajds-${componentName}-padding, 0));
  `;
}

/**
 * Helper function to set default padding
 * @returns default css style rule for padding
 */
function setDefaultPaddingValue<T extends string | number>(componentName: string, propName: T, propValue: ValidPropValueType) {
  if (!spacingKeys.includes(propValue as SpacingKeysType) && !Array.isArray(propValue)) {
    return '';
  }
  const ruleName = `--ajds-${componentName}-${propName}`;
  if (Array.isArray(propValue)) {
    return createResponsiveRule(ruleName, propValue, getSpacingVar);
  }

  return `--ajds-${componentName}-${propName}: ${getSpacingVar(propValue as SpacingKeysType)};`;
}

/**
 * Map for dynamically creating default padding style rules
 */
export const setDefaultPaddingValueMap: Record<PaddingPropName, typeof setDefaultPaddingValue> = {
  padding: setDefaultPaddingValue,
  'padding-bottom': setDefaultPaddingValue,
  'padding-left': setDefaultPaddingValue,
  'padding-right': setDefaultPaddingValue,
  'padding-top': setDefaultPaddingValue,
};

/**
 * Map for dynamically creating default padding style rule overrides
 */
export const setOverridePaddingValueMap: Record<PaddingPropName, typeof getSingleSpacingRule> = {
  padding: getSingleSpacingRule,
  'padding-bottom': getSingleSpacingRule,
  'padding-left': getSingleSpacingRule,
  'padding-right': getSingleSpacingRule,
  'padding-top': getSingleSpacingRule,
};

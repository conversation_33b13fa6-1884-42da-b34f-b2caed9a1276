# AXA Styled System

## What is it?

This library does 2 things:

- Provides an easy way for consumers of the components to customize the styles of a component with a standard set of guideline compliant options.
- Provides an easy way for component authors to handle CSS that changes based on the value of a prop (AKA variants).

## Why does it exist?

Styled Components is great, but sometimes you want to modify a component very slightly, such as add some top margin, and the only way to do it is to create a new styled component that customizes the existing one. This results in more CSS getting shipped to the browser if you're server side rendering (one class for the default styles, another for the modified version), and more JS shipped to the browser (and more JS code that has to run).

For with styles that change based on props, we also have a couple of problems. We have to pass the props into the styled component, and clutter the code with some logic (using not that pretty syntax, with callbacks to access the props). Also it's the same situation with more CSS classes being generated, more JS code shipped to and running on the browser.

There is a package called (Styled System)[https://styled-system.com/] which solves the problems of easily allowing library consumers to modify a component, and more easily manage styles that change based on props. But that means adding another dependency, which means shipping/running more code in the browser. Styled System also does a bit more than we might actually need.

Inspired by (CSS Variables for React Devs)[https://www.joshwcomeau.com/css/css-variables-for-react-devs/], this library takes the ideas of Styled System, but applies the techniques from the _CSS Variables_ article, to create:

- Components that will always only have one CSS class
- Styles that change based on props managed with CSS variables

Which allows us to:

- Ship less and run less code in the browser
- Improving performance
- Provide a great developer experience for both component authors and consumers.

## How does it work?

The best way to explain is to start from the bottom up.

For example, a Button component which has different variants based on a color prop, and allows for direct customization of margin, can look like this:

```tsx
// ...other imports
import Button from '@axa-japan/design-system-react/Button';
// ...other code

// This button will have the orange color variant, and margin of 0.5rem all around, with margin-right of 0.5rem on small screens and 1rem for medium up.
<Button color="orange" sx={{ margin: 1, 'margin-right': [1, 2] }}>
  Click me!
</Button>;
```

Implementing this component would look something like this:

```tsx
// in src/Button/Button.tsx
import React from 'react';
import ButtonBase, { useSx } from './Button.styles';
import { SxProp } from '../sx';

export type ButtonProps = {
  // ...other props
  color?: 'blue' | 'orange';
  sx?: SxPropType;
};

const Button: React.FC<ButtonProps> = (props) => {
  const { color, sx, children, ...rest } = props;
  return (
    // important to put {...rest} BEFORE style={useSx(sx)}
    <ButtonBase {...rest} style={useSx(sx, { color })}>
      {children}
    </ButtonBase>
  );
};
export default Button;
```

We will see where useSx comes from next.

useSx will return an object with CSS variables. These CSS variables will override the default margin using the sx prop in our example. As well as CSS variables for all the properties which control variants based on the color prop (such as background color, font color, et cetera).

Finally, here is the implementation of the ButtonBase styled component:

```tsx
// in src/Button/Button.styles.ts
import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';

// Define an object with all the style rules for the different variants.
// Pass the key names as a union to VariantsType to get better type checking.
const variants: VariantsType<'color'> = {
  color: {
    orange: {
      'background-color': '#d24723',
      'color': '#fafafa',
    },
    blue: {
      'background-color': '#00008f',
      'color': '#fff',
    },
  }
}

/*
 * Call sx with:
 * - The name of the component
 * - Array of properties you want to have customizable with sx prop (pass an empty array if you don't want customization)
 * - Variants object (omit if the component has no variants)
 */
const { useSx, getSxStyleRules, setDefaultSxValue } = sx('Button', ['margin', 'padding'], variants);

const ButtonBase = styled.button`
  /*
   * Set the default value for variables used for padding/margin controlled with sx prop
   * Since we're using css variables, we need to set the default value for the variable, not for the property.
   */
  ${setDefaultSxValue('padding-top': 1)}
  ${setDefaultSxValue('padding-bottom': 1)}
  ${setDefaultSxValue('padding-left': 2)}
  ${setDefaultSxValue('padding-right': 2)}

  /*  ...other style rules  */

  /*
   * Add the properties for padding/margin using var() CSS function.
   * This will set the value to the override value, falling back to 0.
   */
  ${getSxStyleRules()}
`

// Export this to use in the component file.
export { useSx };

export default ButtonBase
```

## Low-level details

Calling `sx` will return three helper functions:

- setDefaultSxValue
- getSxStyleRules
- useSx

As an example, imagine you want to use `sx` to allow component consumers to easily add some margin to a component. Inside a file called `MyComponent.styles.ts` you would call the `sx` function like so:

```ts
const { setDefaultSxValue, getSxStyleRules, useSx } = sx('MyComponent', ['margin']);
```

Let's look at each one of these functions in detail.

### setDefaultSxValue

Continuing with our example, if you call `setDefaultSxValue` at the top of your styled component like so:

<!-- prettier-ignore-start -->
```ts
const MyComponent = styled.div`
  ${setDefaultSxValue('margin', 1)}
  
  /*  ...other style rules  */
`;
```
<!-- prettier-ignore-end -->

It will return:

```css
--ajds-MyComponent-margin: var(--ajds-spacing-1);
```

End result would be:

```ts
const MyComponent = styled.div`
  --ajds-MyComponent-margin: var(--ajds-spacing-1);

  /*  ...other style rules  */
`;
```

This will set the default value of the variable used for controlling margin. We always want to use standard values for these variables from the design system, using CSS variables that are globally available using the `BaseStyles` component.

### getSxStyleRules

Continuing with our example, when you call `getSxStyleRules` at the bottom of your styled component like so:

```ts
const MyComponent = styled.div`
  ${setDefaultSxValue('margin', 1)}

  /*  ...other style rules  */

  ${getSxStyleRules()}
`;
```

It will return:

<!-- prettier-ignore-start -->
```css
margin: var(--ajds-MyComponent-margin-top, var(--ajds-MyComponent-margin, 0)) 
        var(--ajds-MyComponent-margin-right, var(--ajds-MyComponent-margin, 0))
        var(--ajds-MyComponent-margin-bottom, var(--ajds-MyComponent-margin, 0)) 
        var(--ajds-MyComponent-margin-left, var(--ajds-MyComponent-margin, 0));
```
<!-- prettier-ignore-end -->

End result would be:

<!-- prettier-ignore-start -->
```ts
const MyComponent = styled.div`
  --ajds-MyComponent-margin: var(--ajds-spacing-1);

  /*  ...other style rules  */

  margin: var(--ajds-MyComponent-margin-top, var(--ajds-MyComponent-margin, 0)) 
          var(--ajds-MyComponent-margin-right, var(--ajds-MyComponent-margin, 0))
          var(--ajds-MyComponent-margin-bottom, var(--ajds-MyComponent-margin, 0)) 
          var(--ajds-MyComponent-margin-left, var(--ajds-MyComponent-margin, 0));
`;
```
<!-- prettier-ignore-end -->

This will add the margin CSS rule to your component, with the value set to CSS variables, using the var() CSS function.

The var() CSS function accepts a CSS variable name as the first argument, with an optional second argument to use as a fallback value (if the fallback value is also a CSS variable, you must also wrap that variable in the var() CSS function).

In our example:

- First, it will look `--ajds-MyComponent-margin-top` (set with either `setDefaultSxValue('margin-top': <some value>)` or the `sx` prop using `useSx`)
- If `--ajds-MyComponent-margin-top` is not set, next it will look for `--ajds-MyComponent-margin` (set with either `setDefaultSxValue('margin': <some value>)` or the `sx` prop using `useSx`)
- If neither of those two variables exist, next it will fallback to the default value of 0.

### useSx

Continuing with our example, in order to allow component consumers to actually modify the margin using the `sx` prop, you need to use `useSx`.

First, you will need to export `useSx` from your styled component file, along with the styled component itself, like so:

```ts
const { setDefaultSxValue, getSxStyleRules, useSx } = sx('MyComponent', ['margin']);

const MyComponentBase = styled.div`
  ${setDefaultSxValue('margin', 1)}

  /*  ...other style rules  */

  ${getSxStyleRules()}
`;

export default MyComponentBase;

export { useSx };
```

Then in your component file, you will need to call `useSx` passing in the `sx` prop as an argument, and use that to set the `style` prop on `MyComponentBase`, like so:

```tsx
import MyComponentBase, { useSx } from './MyComponentBase.ts';
import type { MarginSxPropType } from '../sx';

type MyComponentProps = {
  // ...other props for the component
  sx?: MarginSxPropType;
};

const MyComponent: React.FC<MyComponentProps> = ({ sx, children, ...rest }) => {
  return (
    <MyComponentBase {...rest} style={useSx(sx)}>
      {children}
    </MyComponentBase>
  );
};
```

In our example case, we only want `margin` to be customized with the `sx` prop, which is why we declare in our props type `sx?: MarginSxPropType;`. If you wanted to allow all available customizations, you can use `sx?: SxPropType;` exported from the `sx` module.

## Handling responsiveness

Sometimes, you want a different value, either for your `margin` or `padding` handled with the `sx` prop, or even for some CSS property of your variants. Lucky for you, it's very easy to do!

Instead of passing a single value, simply pass an array of values. The first value in the array will apply to small screen sizes (or all screen sizes if you supply an array of only one value). The second value in the array will apply to medium screen sizes (also large if you supply an array of only two values). The third value in the array will apply to large screen sizes.

For example, a Button component which has different variants based on a color prop, and allows for direct customization of margin, can look like this:

```tsx
// ...other imports
import Button from '@axa-japan/design-system-react/Button';
// ...other code

// This button will have the orange color variant, and margin of 0.5rem all around, with margin-right of 0.5rem on small screens and 1rem for medium up.
<Button color="orange" sx={{ margin: 1, 'margin-right': [1, 2] }}>
  Click me!
</Button>;
```

If you a variant with responsive values can look like this:

```tsx
// This variant config is for one prop called `size`.
// The prop will set responsive font sizes.
const variants: VariantsType<'size'> = {
  size: {
    small: {
      'font-size': ['14px', '16px', '18px'],
    },
    large: {
      'font-size': ['18px', '20px', '22px'],
    },
  },
};
```

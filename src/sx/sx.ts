import { setDefaultMarginValueMap } from './attributes/margin';
import { setDefaultPaddingValueMap } from './attributes/padding';
import { setDefaultBackgroundColorValueMap } from './attributes/backgroundColor';
import getSxPropRules from './helpers/getSxPropRules';
import getVariantPropRules from './helpers/getVariantPropRules';
import getSxCssRules from './helpers/getSxCssRules';
import getVariantCssRules from './helpers/getVariantCssRules';
import type { ValidPropValueType, ValidPropNameType, VariantsType, SxPropType, SxRulesType } from './types';
import useScreenSize from '../hooks/useScreenSize';

/**
 * Combination map for setting default style attributes.
 */
const sxDefaultStyleMap: Record<string, (componentName: string, propName: string, value: ValidPropValueType) => string> = {
  ...setDefaultBackgroundColorValueMap,
  ...setDefaultMarginValueMap,
  ...setDefaultPaddingValueMap,
};

/**
 * Returns helper functions for dealing with style overrides using sx prop.
 *
 * @example
 *
 * // in src/Button/Button.styles.ts
 *
 * import styled from 'styled-components';
 * import sx from '../sx';
 *
 * // Call sx with the name of the component
 * const { useSx, getSxStyleRules, setDefaultSxValue } = sx('Button');
 *
 * const ButtonBase = styled.button`
 *   // Set the variables used for padding/margin
 *   ${setDefaultSxValue('padding-top': 1)}
 *   ${setDefaultSxValue('padding-bottom': 1)}
 *   ${setDefaultSxValue('padding-left': 2)}
 *   ${setDefaultSxValue('padding-right': 2)}
 *
 *   // ...other style rules
 *
 *   // Adds the properties for padding/margin,
 *   // setting the value to the override value, falling back to default value, falling back to 0.
 *   ${getSxStyleRules()}
 * `
 *
 * export { useSx };
 * export default ButtonBase
 *
 * // in src/Button/Button.tsx
 *
 * import React from 'react';
 * import ButtonBase, { useSx } from './Button.styles';
 * import { SxProp } from '../sx';
 *
 * export type ButtonProps = {
 *   // ...other props
 *   sx: SxPropType;
 * };
 *
 * const Button: React.FC<ButtonProps> = (props) => {
 *   const { sx, children, ...rest } = props;
 *
 *   // useSx will return an object with css variable overrides
 *   // important to put {...rest} BEFORE style={useSx(sx)}
 *   return (
 *     <ButtonBase {...rest} style={useSx(sx)}>{children}</ButtonBase>
 *   );
 * }
 *
 * export default Button;
 *
 * // in your application
 *
 * import Button from '@axa-japan/design-system-react/Button';
 *
 * // ...other code
 *
 * // This button will have margin of 0.5rem all around, with margin-right of 1rem.
 * <Button sx={{ margin: 1, 'margin-right': 2 }}>Click me!</Button>
 */
function sx<VariantPropNames extends string>(
  componentName: string,
  sxRules?: SxRulesType,
  variants?: VariantsType<VariantPropNames>,
): {
  getSxStyleRules(): string;
  setDefaultSxValue(propName: ValidPropNameType, propValue: ValidPropValueType): string;
  useSx(sxProp?: SxPropType, variant?: Record<VariantPropNames, string | undefined>): Record<string, string | Record<string, string>>;
} {
  /**
   *
   * @returns Object to use as style prop for a component, to override styles using sx prop.
   */
  function useSx(sxProp?: SxPropType, variant?: Record<VariantPropNames, string>) {
    const screenSize = useScreenSize();
    // Get the style overrides for the sx prop
    const sxPropRules = getSxPropRules(componentName, screenSize, sxProp);

    // Get the style overrides for the variant prop
    const variantPropRules = getVariantPropRules<VariantPropNames>(componentName, screenSize, variants, variant);

    // Return the merge of the two rule sets
    return {
      ...sxPropRules,
      ...variantPropRules,
    };
  }

  function getSxStyleRules() {
    // Get the css rules for sx the prop
    const sxCssRules = getSxCssRules(componentName, sxRules);

    // Get the css rules for variant the prop
    const variantCssRules = getVariantCssRules<VariantPropNames>(componentName, variants);

    // Return the merge of the css rules
    return sxCssRules + variantCssRules;
  }

  function setDefaultSxValue(propName: ValidPropNameType, propValue: ValidPropValueType) {
    // Return the css var for the component/prop/value combination
    return sxDefaultStyleMap[propName](componentName, propName, propValue);
  }

  return {
    getSxStyleRules,
    setDefaultSxValue,
    useSx,
  };
}

export default sx;

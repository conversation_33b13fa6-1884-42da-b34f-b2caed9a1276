import React, { forwardRef } from 'react';
import { DatePickerProps as RACDatePickerProps, DateValue, I18nProvider } from 'react-aria-components';
import { CalendarDate } from '@internationalized/date';
import {
  DEFAULT_START_YEAR,
  DEFAULT_END_YEAR,
  DEFAULT_START_MONTH,
  DEFAULT_END_MONTH,
  DEFAULT_START_DAY,
  DEFAULT_END_DAY,
} from '../DatePickerField/constants';
import Calendar, { CommonCalendarProps } from '../Calendar/Calendar';
import DatePickerDialog from './DatePickerDialog';
import DatePickerBase from './DatePickerBase';
import DatePickerPopover from './DatePickerPopover';

export type DatePickerProps = {
  /** For react testing library tests */
  'data-testid'?: string;
} & CommonCalendarProps &
  Pick<React.ComponentPropsWithoutRef<'input'>, 'id' | 'name' | 'disabled' | 'form' | 'onBlur' | 'onChange' | 'onFocus' | 'onKeyDown' | 'onKeyUp'> &
  Omit<
    RACDatePickerProps<DateValue>,
    | 'pageBehavior'
    | 'placeholderValue'
    | 'hourCycle'
    | 'granularity'
    | 'hideTimeZone'
    | 'shouldForceLeadingZeros'
    | 'children'
    | 'className'
    | 'style'
    | 'isReadOnly'
    | 'shouldCloseOnSelect'
    | 'slot'
    | 'onBlur'
    | 'onChange'
    | 'onFocus'
    | 'onFocusChange'
    | 'onKeyDown'
    | 'onKeyUp'
  >;

const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(({ minValue, maxValue, holidays = [], ...rest }, ref) => {
  return (
    <I18nProvider locale="ja-JP">
      <DatePickerBase
        ref={ref}
        shouldForceLeadingZeros={true}
        minValue={minValue ?? new CalendarDate(DEFAULT_START_YEAR, DEFAULT_START_MONTH, DEFAULT_START_DAY)}
        maxValue={maxValue ?? new CalendarDate(DEFAULT_END_YEAR, DEFAULT_END_MONTH, DEFAULT_END_DAY)}
        {...rest}
      >
        <DatePickerPopover>
          <DatePickerDialog>
            <Calendar holidays={holidays} />
          </DatePickerDialog>
        </DatePickerPopover>
      </DatePickerBase>
    </I18nProvider>
  );
});

DatePicker.displayName = 'DatePicker';

export default React.memo(DatePicker);

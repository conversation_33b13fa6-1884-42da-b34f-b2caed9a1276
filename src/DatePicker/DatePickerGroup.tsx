/*
  This component is a low-level implementation of React-aria-component's <Group> component. The group component contains the TextField
  and Calendar button and receives props from <DatePickerBase> component. Mainly receives, accessibility attributes, and ref.
*/

import React, { ReactNode, forwardRef } from 'react';
import { GroupContext, GroupProps, useContextProps } from 'react-aria-components';
import DataPickerGroupBase from './DatePickerGroupBase';

const DatePickerGroup = forwardRef<HTMLDivElement, GroupProps>((groupProp, groupRef) => {
  const [props, ref] = useContextProps(groupProp, groupRef, GroupContext);
  const {
    isInvalid,
    id,
    role,
    slot,
    children,
    'aria-describedby': ariaDescribedBy,
    'aria-disabled': ariaDisabled,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledBy,
  } = props;
  return (
    <DataPickerGroupBase
      ref={ref}
      id={id}
      role={role}
      slot={slot || undefined}
      aria-invalid={isInvalid}
      aria-describedby={ariaDescribedBy}
      aria-disabled={ariaDisabled}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
    >
      {children as ReactNode}
    </DataPickerGroupBase>
  );
});

DatePickerGroup.displayName = 'DatePickerGroup';

export default React.memo(DatePickerGroup);

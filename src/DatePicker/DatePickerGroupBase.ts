import styled from 'styled-components';
import input from '../styles/input';
import { getColorVar } from '../colors';

const DataPickerGroupBase = styled.div`
  ${input.base}
  ${input.baseBorder}

  &:focus-within:not([aria-disabled='true'], :has(button:focus)) {
    ${input.focusBoxShadow}
  }

  &[aria-disabled='true'] {
    ${input.disabledBorder}
    background-color: ${getColorVar('interactiveDisabledLight')};
    color: ${getColorVar('interactiveDisabledDark')};
    cursor: not-allowed;
  }

  display: flex;
`;

export default DataPickerGroupBase;

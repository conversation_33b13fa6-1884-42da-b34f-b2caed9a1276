import React, { useContext } from 'react';
import * as Popover from '@radix-ui/react-popover';
import { DatePickerStateContext } from 'react-aria-components';
import { getZIndexVar } from '../zIndex';
import DatePickerTextField, { DatePickerState } from './DatePickerTextField';
import { CalendarIcon } from '../Icons';
import DataPickerGroup from './DatePickerGroup';
import IconButton from '../IconButton';
import { DATE_PICKER_ICON_BUTTON_LABEL } from '../DatePickerField/constants';

const DatePickerPopover: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { isOpen, setOpen, isDisabled, inputRef, popoverRef } = (useContext(DatePickerStateContext) as DatePickerState) || {};
  return (
    <Popover.Root open={isOpen} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <DataPickerGroup>
          <DatePickerTextField />
          <IconButton
            aria-label={DATE_PICKER_ICON_BUTTON_LABEL}
            data-testid={DATE_PICKER_ICON_BUTTON_LABEL}
            icon={<CalendarIcon />}
            color="grey"
            // We exclude the Calendar button from Tab focus & focus on the Textfield initially
            tabIndex={-1}
            disabled={isDisabled}
            onClick={() => {
              setOpen(!isOpen);
              // We need to wait for the Calendar to render before trying to focus on calendar cell
              setTimeout(() => {
                (popoverRef.current?.querySelector('div[tabindex="0"][role="button"]') as HTMLDivElement)?.focus();
              }, 0);
            }}
          />
        </DataPickerGroup>
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          ref={popoverRef}
          // Focus on the TextField when user presses escape in he Calendar
          onEscapeKeyDown={(e) => {
            e.preventDefault();
            inputRef.current?.focus();
          }}
          sideOffset={4}
          collisionPadding={{ left: 8, right: 8 }}
          onOpenAutoFocus={(e) => e.preventDefault()}
          style={{ zIndex: getZIndexVar('overlay') }}
        >
          {children}
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default React.memo(DatePickerPopover);

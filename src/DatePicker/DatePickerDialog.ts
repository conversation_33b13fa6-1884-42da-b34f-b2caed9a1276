import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';
import { getShadowVar } from '../shadows';
import media from '../Breakpoints/Breakpoints';
import { getRadiusVar } from '../radius';

const DatePickerDialog = styled.div`
  background-color: ${getColorVar('utilityBackgroundWhite')};
  padding: ${getSpacingVar(1)};
  border-radius: ${getRadiusVar('sm')};
  box-shadow: ${getShadowVar('mediumShadow')};
  outline: none;

  ${media.minimumSupportedUp} {
    padding: ${getSpacingVar(2)};
  }
`;

export default DatePickerDialog;

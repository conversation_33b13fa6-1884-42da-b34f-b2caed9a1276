/*
  This component is a low-level implementation of React-aria-component's <DateField> component. However, we choose to make our own
  implementation of the TextField as React-aria's does not fit our use-case. Receives props from <DatePickerBase>. Guides, parses
  the user input, and updates the global DatePicker state when input is valid.
*/

import React, { RefObject, forwardRef, useContext } from 'react';
import { DateFieldContext, DateInputRenderProps, DatePickerStateContext, useContextProps } from 'react-aria-components';
import { DatePickerState as RACDatePickerState } from 'react-stately';
import { DateValue } from 'react-aria';
import { CalendarDate } from '@internationalized/date';
import ReactInputMask from 'react-input-mask';
import DatePickerInput from './DatePickerInput';
import {
  DEFAULT_START_YEAR,
  DEFAULT_END_YEAR,
  DEFAULT_START_MONTH,
  DEFAULT_END_MONTH,
  DEFAULT_START_DAY,
  DEFAULT_END_DAY,
} from '../DatePickerField/constants';
import isTouchScreenDevice from '../utils/isTouchScreenDevice';
import useMediaQuery from '../hooks/useMediaQuery';
import { extraSmallMaxPx } from '../Breakpoints/Breakpoints';

type Selection = {
  start: number;
  end: number;
};

type InputState = {
  value: string;
  selection: Selection | null;
  enteredString?: string;
};

// These are the custom properties we add to react-aria's DatePickerState
export type DatePickerState = RACDatePickerState & {
  textFieldInput: string;
  isDisabled: boolean;
  inputRef: RefObject<HTMLInputElement>;
  popoverRef: RefObject<HTMLDivElement>;
  setTextFieldInput: React.Dispatch<React.SetStateAction<string | undefined>>;
  setFocusedDatePickerDate: React.Dispatch<React.SetStateAction<DateValue | undefined>>;
};

type DatePickerTextFieldProps = {
  /** For react testing library tests */
  'data-testid'?: string;
} & Partial<DateInputRenderProps> &
  Pick<
    React.ComponentPropsWithoutRef<'input'>,
    'id' | 'name' | 'aria-label' | 'aria-describedby' | 'onBlur' | 'onChange' | 'onFocus' | 'onKeyDown' | 'onKeyUp'
  >;

const DatePickerTextField = forwardRef<HTMLDivElement, DatePickerTextFieldProps>((datePickerTextFieldProps, datePickerTextFieldRef) => {
  const [props, ref] = useContextProps(datePickerTextFieldProps, datePickerTextFieldRef, DateFieldContext);
  const { id, 'aria-label': ariaLabel, 'aria-describedby': ariaDescribedBy, isDisabled, onBlur, onChange, onFocus, onKeyDown, onKeyUp } = props;
  const { value, setValue, textFieldInput, setTextFieldInput, setOpen, setFocusedDatePickerDate, popoverRef } =
    (useContext(DatePickerStateContext) as DatePickerState) || {};
  // Temporary until future breakpoints refactor
  const isExtraSmall = useMediaQuery(`(max-width: ${extraSmallMaxPx})`);
  const isNotInteger = (int?: string) => Number.isNaN(Number(int));
  // Function to update global DatePicker state if TextField input is valid
  const processDate = (input: string): DateValue | null => {
    // Split date segments, e.g. 2024/05/27 and process separately
    const [yearSegment = '', monthSegment = '', daySegment = ''] = input.split('/');
    // Validate the year, month, day segments, set as undefined if invalid
    const yearToUpdate = Number(yearSegment) < DEFAULT_START_YEAR || Number(yearSegment) > DEFAULT_END_YEAR ? undefined : Number(yearSegment);
    const monthToUpdate = Number(monthSegment) < DEFAULT_START_MONTH || Number(monthSegment) > DEFAULT_END_MONTH ? undefined : Number(monthSegment);
    const dayToUpdate = Number(daySegment) < DEFAULT_START_DAY || Number(daySegment) > DEFAULT_END_DAY ? undefined : Number(daySegment);
    // If inputs are valid create object for updating global DatePicker state & focus the date on the Calendar
    if (yearToUpdate && monthToUpdate && dayToUpdate) {
      const dateToUpdate = new CalendarDate(yearToUpdate, monthToUpdate, dayToUpdate);
      setFocusedDatePickerDate(dateToUpdate);
      return dateToUpdate;
    }
    return value;
  };
  // Function to handle full-width number input
  const handleFullWidthNumberInput = (nextState: InputState) => {
    let { value: nextValue, selection } = nextState;
    const { enteredString } = nextState;
    const selectionStart = selection?.start || 0;
    // This conditional is to catch when user inputs which are full-width numbers and not inputting text when cursor is at the end
    if (isNotInteger(enteredString) && !isNotInteger(enteredString?.normalize('NFKC')) && selectionStart < 10) {
      const formattedInput = enteredString?.normalize('NFKC');
      const enteredStringLen = enteredString?.length || 0;
      const newCursorPosition = selectionStart + enteredStringLen;
      // Normalize the number to half-width and add it to the masked input
      nextValue = `${nextValue.slice(0, selection?.start)}${formattedInput}${nextValue.slice(selectionStart + enteredStringLen)}`;
      // Update the cursor position
      selection = { start: newCursorPosition, end: newCursorPosition };
    }
    return {
      value: nextValue,
      selection,
    };
  };
  return (
    <DatePickerInput
      data-testid={props['data-testid']}
      mask="9999/99/99"
      maskPlaceholder="----/--/--"
      id={id}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-haspopup="dialog"
      ref={ref as unknown as RefObject<ReactInputMask>}
      disabled={isDisabled}
      placeholder="yyyy/mm/dd"
      type="text"
      inputMode="numeric"
      value={textFieldInput ?? ''}
      onBlur={onBlur}
      onFocus={(e) => {
        // Close the calendar if textfield is focused if touchscreen and screen is extra small
        if (isTouchScreenDevice() && isExtraSmall) {
          setOpen(false);
        } else {
          setOpen(true);
        }
        if (typeof onFocus === 'function') {
          onFocus(e);
        }
      }}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
        setTextFieldInput(e.currentTarget.value);
        setValue(processDate(e.currentTarget.value));
        if (typeof onChange === 'function') {
          onChange(e);
        }
      }}
      onKeyUp={onKeyUp}
      onKeyDown={(e) => {
        // Focus on the Calendar's active date when arrow up or down is pressed
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
          e.preventDefault();
          (popoverRef.current?.querySelector('div[tabindex="0"][role="button"]') as HTMLDivElement)?.focus();
        }
        if (typeof onKeyDown === 'function') {
          onKeyDown(e);
        }
      }}
      beforeMaskedStateChange={({ nextState }) => handleFullWidthNumberInput(nextState)}
    />
  );
});

DatePickerTextField.displayName = 'DatePickerTextField';

export default React.memo(DatePickerTextField);

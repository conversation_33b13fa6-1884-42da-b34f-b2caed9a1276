/*
  This component is a low-level implementation of React-aria-component's <DatePicker> component. This component acts as the context
  provider of multiple child components & holder of the global state. We do not use some of the child components (Popover, Dialog),
  whilst creating custom versions (DateInput, Group) of the components, deleting unnecessary utility functions & hooks.
*/
import { AriaDatePickerProps, DateValue, useDatePicker, useFocusRing, useIsSSR } from 'react-aria';
import { DatePickerState, useDatePickerState } from 'react-stately';
import React, { ReactNode, forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import {
  CalendarContext,
  DateFieldContext,
  DateFieldProps,
  DatePickerContext,
  DatePickerProps,
  DatePickerStateContext,
  GroupContext,
  Provider,
  useContextProps,
} from 'react-aria-components';
import { today } from '@internationalized/date';

const removeDataAttributes = <T,>(props: T): T => {
  const filteredProps = {} as T;

  // eslint-disable-next-line no-restricted-syntax
  for (const prop of Object.keys(props as object) as (keyof T)[]) {
    if (!(prop as string).startsWith('data-')) {
      filteredProps[prop] = props[prop];
    }
  }

  return filteredProps;
};

// the useFocusRing hook doesn't work when used server side, so we'll create 2 divs to use conditionally
const DatePickerBaseWithFocus: React.FC<React.PropsWithChildren<React.ComponentPropsWithoutRef<'input'>>> = ({ children, ...rest }) => {
  const { isFocused, isFocusVisible } = useFocusRing({ within: true });
  return (
    <div data-focus-within={isFocused || undefined} data-focus-visible={isFocusVisible || undefined} {...rest}>
      {children}
    </div>
  );
};

const DatePickerBaseWithoutFocus: React.FC<React.PropsWithChildren<React.ComponentPropsWithoutRef<'input'>>> = ({ children, ...rest }) => {
  return <div {...rest}>{children}</div>;
};

type DatePickerBaseProps = {
  /** For react testing library tests */
  'data-testid'?: string;
} & Omit<DatePickerProps<DateValue>, 'onBlur' | 'onChange' | 'onFocus' | 'onFocusChange' | 'onKeyDown' | 'onKeyUp' | 'validationBehavior'> &
  Pick<React.ComponentPropsWithRef<'input'>, 'id' | 'name' | 'form' | 'onBlur' | 'onChange' | 'onFocus' | 'onKeyDown' | 'onKeyUp'>;

// DatePickerBase is the root component of the DatePicker, acting as the provider that holds some global state, and provides them
// to the child components
const DatePickerBase = forwardRef<HTMLInputElement, DatePickerBaseProps>(({ onChange, onBlur, onFocus, onKeyDown, onKeyUp, ...props }, ref) => {
  const isSSR = useIsSSR();
  // merge the local component props and the DatePickerContext props
  // The DatePickerContext type does not have the expected slot prop so typescript errors...
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [datePickerProps, inputRef] = useContextProps<typeof props, any, HTMLInputElement>(props, ref, DatePickerContext);
  const { children, isDisabled, slot, id } = datePickerProps;
  const state = useDatePickerState({
    ...props,
    validationBehavior: 'native',
  });
  // State for the TextField
  const [textFieldInput, setTextFieldInput] = useState(state.value?.toString());
  // State for the currently focused date. Used to navigate the Calendar component
  const [focusedDatePickerDate, setFocusedDatePickerDate] = useState(state.value ?? today('Asia/Tokyo'));

  // refs passed to the Group & Popover component respectively
  const groupRef = useRef<HTMLDivElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  // props to pass to the children

  // Create new object that doesn't include data attributes.
  const cleansedProps: AriaDatePickerProps<DateValue> = removeDataAttributes(datePickerProps);
  // Set these on the object to avoid spreading into a new object
  cleansedProps.label = false;
  cleansedProps.validationBehavior = 'native';

  const { groupProps, fieldProps, calendarProps } = useDatePicker(cleansedProps, state, groupRef);

  useEffect(() => {
    if (textFieldInput) {
      // update the value of the DatePickerTextField (again) after the setState, therefore triggering its native onChange handler.
      // It is placed in a useEffect instead of directly in the Calendar's onChange below because the TextField sometimes fails to update
      // due to the race condition of needing to wait for the initial re-render of the TextField first after setTextFieldInput. Placing it here
      // makes sure the code is run ONLY after the DatePickerTextField is re-rendered.
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value')!.set!;
      nativeInputValueSetter.call(inputRef.current, textFieldInput);
      inputRef.current?.dispatchEvent(new Event('input', { bubbles: true }));
    }
  }, [inputRef, textFieldInput]);

  const onCalendarChange = useCallback(
    (date: DateValue) => {
      state.setOpen(false);
      setTextFieldInput(date.toString());
      // We need to wait for textfield to finish re-rendering before
      // focusing on the calendar button when calendar closes
      setTimeout(() => {
        (groupRef.current?.childNodes[1] as HTMLButtonElement).focus();
      }, 0);
    },
    [state],
  );

  const onCalendarFocusChange = useCallback((date: DateValue) => {
    setFocusedDatePickerDate(date);
  }, []);
  return (
    // Provider to send props and state to multiple child components, helps to avoid prop drilling
    <Provider
      values={[
        [
          // The global DatePicker state.
          DatePickerStateContext,
          {
            ...state,
            textFieldInput,
            isDisabled,
            setTextFieldInput,
            setFocusedDatePickerDate,
            inputRef,
            popoverRef,
          } as DatePickerState,
        ],
        // Props provided to ../DatePicker/DatePickerGroup.tsx, the container that holds the TextField and Calendar Button
        [GroupContext, { ...groupProps, ref: groupRef, isInvalid: state.isInvalid, id: `${groupProps.id}group` }],
        // Props provided to ../DatePicker/DatePickerTextField.tsx, the TextField
        [
          DateFieldContext,
          {
            ...fieldProps,
            ref: inputRef,
            id,
            onChange,
            onBlur,
            onFocus,
            onKeyDown,
            onKeyUp,
            'data-testid': props['data-testid'],
          } as DateFieldProps<DateValue>,
        ],
        // Props provided to ../Calendar/Calendar.tsx, the Calendar
        [
          CalendarContext,
          {
            ...calendarProps,
            focusedValue: focusedDatePickerDate,
            value: state.value,
            // This onChange runs when a date is selected in the Calendar component. The onChange then updates the TextField with the selected
            // date by calling setTextFieldInput (which the Textfield uses the state of). However updating state of an input element in react
            // doesn't trigger the onChange of the input element itself, which only triggers when user does actions directly to the TextField,
            // such as typing. We want this onChange to be triggered otherwise the user won't be notified when the TextField changes due to Calendar date selection.
            // The useEffect in this file solves this.
            onChange: onCalendarChange,
            onFocusChange: onCalendarFocusChange,
          },
        ],
      ]}
    >
      {isSSR ? (
        <DatePickerBaseWithoutFocus
          style={{ width: '100%' }}
          slot={slot || undefined}
          data-invalid={state.isInvalid || undefined}
          data-disabled={isDisabled || undefined}
          data-open={state.isOpen || undefined}
        >
          {children as ReactNode}
        </DatePickerBaseWithoutFocus>
      ) : (
        <DatePickerBaseWithFocus
          style={{ width: '100%' }}
          slot={slot || undefined}
          data-invalid={state.isInvalid || undefined}
          data-disabled={isDisabled || undefined}
          data-open={state.isOpen || undefined}
        >
          {children as ReactNode}
        </DatePickerBaseWithFocus>
      )}
    </Provider>
  );
});

DatePickerBase.displayName = 'DatePickerBase';

export default React.memo(DatePickerBase);

import React from 'react';
import { createColumnHelper, RowData } from '@tanstack/react-table';
import { CustomTableState } from './types';
import { DataTableColumnDef } from '../DataTable/types';
import { DataListColumnDef } from '../DataList/types';
import Radio from '../Radio/Radio';
import Checkbox from '../Checkbox/Checkbox';

type FormColumnProps = Pick<CustomTableState, 'checkboxConfig' | 'radioConfig'>;

const getFormColumnConfig = ({
  checkboxConfig,
  radioConfig,
}: FormColumnProps): DataTableColumnDef<RowData> | DataListColumnDef<RowData> | undefined => {
  const columnHelper = createColumnHelper<RowData>();
  if (!checkboxConfig && !radioConfig) return undefined;
  if (checkboxConfig) {
    return columnHelper.display({
      id: checkboxConfig.id,
      header: checkboxConfig.headerConfig
        ? ({ table }) => {
            const config = checkboxConfig?.headerConfig ? checkboxConfig.headerConfig(table) : undefined;
            const isIntermediateState = table.getIsSomeRowsSelected() && !table.getIsAllRowsSelected();
            if (!config) return undefined;
            return (
              <Checkbox
                {...{ ...config }}
                aria-checked={isIntermediateState ? 'mixed' : table.getIsAllRowsSelected()}
                checked={table.getIsAllRowsSelected()}
                onChange={table.getToggleAllRowsSelectedHandler()}
              />
            );
          }
        : undefined,
      cell: ({ row }) => {
        const config = checkboxConfig.cellConfig(row);
        return (
          <Checkbox
            {...{ ...config }}
            aria-checked={row.getIsSelected()}
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        );
      },
    });
  }
  if (radioConfig) {
    return columnHelper.display({
      id: radioConfig.id,
      cell: ({ row }) => {
        const config = radioConfig.cellConfig(row);
        return <Radio {...{ ...config }} />;
      },
    });
  }
  return undefined;
};

export default getFormColumnConfig;

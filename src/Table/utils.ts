import { RowData, Table, TableFeature, Column, Header, ColumnPinningPosition } from '@tanstack/react-table';
import { CSSProperties } from 'react';
import { PinnedColumnsProps, CustomOptions, ColumnWidthOptionState } from './types';
import { SpacingKeysType } from '../spacing';

// Helper function to get props related to column pinning
export const getPinnedColumnsProps = <RowData>(column: Column<RowData>, header?: Header<RowData, RowData>): PinnedColumnsProps => {
  const isPinned = column.getIsPinned();
  /* We will check if a pinned column is the last column, if pinned left. One check to make sure that
   all headers and sub-headers are accounted for. The other is for the body cells. */
  const isLastLeftPinnedColumn =
    isPinned === 'left' && (header?.getLeafHeaders().some((leaf) => leaf.column.getIsLastColumn('left')) || column.getIsLastColumn('left'));
  /* To get a pinned columns left css position, first we check if column is pinned left, then we get either header or body cell's position */
  const left = isPinned === 'left' ? `${header?.getStart('left') || column?.getStart('left')}px` : undefined;
  return {
    isLastLeftPinnedColumn,
    left,
  };
};

type CellStylesParams = {
  colWidthOption?: ColumnWidthOptionState;
  colWidth?: SpacingKeysType;
  columnPinningPosition?: ColumnPinningPosition;
  left?: string;
  cellType?: 'table' | 'div';
  table: Table<RowData>;
};

// Helper function to get css styles
export const getCellStyles = ({ colWidthOption, colWidth, columnPinningPosition, left, cellType, table }: CellStylesParams) => {
  let cssProperties: CSSProperties = {};
  if (colWidthOption === 'fill') {
    if (cellType === 'div') {
      // For div-based tables, we use flex layout to achieve fill
      cssProperties = { ...cssProperties, flex: '1 1 auto' };
    } else {
      // For HTML tables, calculate the percentage width based on number of fill columns
      const allColumns = table.getAllColumns();
      const fillColumns = allColumns.filter((col) => col.getColumnWidthOption() === 'fill');
      const fillColumnCount = fillColumns.length;

      if (fillColumnCount > 1) {
        // If multiple fill columns, distribute width equally among them
        const percentageWidth = `${100 / fillColumnCount}%`;
        cssProperties = { ...cssProperties, width: percentageWidth };
      } else {
        // Single fill column gets 100% width
        cssProperties = { ...cssProperties, width: '100%' };
      }
    }
  }
  if (colWidthOption === 'fixed') {
    cssProperties = { ...cssProperties, width: colWidth, minWidth: colWidth, maxWidth: colWidth };
  }
  if (columnPinningPosition === 'left') {
    cssProperties = { ...cssProperties, left, width: colWidth, minWidth: colWidth, maxWidth: colWidth };
  }
  return cssProperties;
};

const defaultDensity = 'default';
const defaultHorizontalAlign = 'left';
const defaultVerticalAlign = 'center';
const defaultStickyHeader = false;
const defaultColumnWidthOption = 'fit';
const defaultColumnWidth = 150;

// Code for custom features that we add to Tanstack table
export const CustomFeatures: TableFeature = {
  // define the new feature's default options
  getDefaultOptions: (): CustomOptions => {
    return {
      density: defaultDensity,
      horizontalAlign: defaultHorizontalAlign,
      verticalAlign: defaultVerticalAlign,
      stickyHeader: defaultStickyHeader,
      columnWidthOption: defaultColumnWidthOption,
    };
  },
  // define the new feature's table instance methods
  createTable: <TData extends RowData>(table: Table<TData>): void => {
    table.getStickyHeader = () => {
      return table.options.stickyHeader || false;
    };
  },
  // Add column instance APIs.
  createColumn: <TData extends RowData>(column: Column<TData>, table: Table<TData>): void => {
    // Instance API to get specific information to style each cell. Priority is the prop set on the column level,
    // then table level, and a fallback.
    column.getDensity = () => table.options?.density || defaultDensity;
    column.getHorizontalAlign = () => column.columnDef.meta?.horizontalAlign || table.options?.horizontalAlign || defaultHorizontalAlign;
    column.getVerticalAlign = () => table.options?.verticalAlign || defaultVerticalAlign;
    column.getColumnWidthOption = () => column.columnDef.meta?.columnWidthOption || table.options?.columnWidthOption || defaultColumnWidthOption;
    column.getSize = () => (column.columnDef.meta?.width ? (column.columnDef.meta?.width as number) * 4 : defaultColumnWidth);
  },
};

/* eslint-disable @typescript-eslint/no-unused-vars */
import { RowData, AccessorFnColumnDef, AccessorKeyColumnDef, ColumnDef, Table, Row } from '@tanstack/react-table';
import { SpacingKeysType } from '../spacing';
import { CheckboxOptions } from '../Checkbox';
import { RadioOptions } from '../Radio';

export type CommonColumnDefProps<TData extends RowData, TValue = unknown> = Required<Pick<ColumnDef<TData, TValue>, 'id'>> &
  Partial<Pick<AccessorKeyColumnDef<TData, TValue>, 'accessorKey'>> &
  Partial<Pick<AccessorFnColumnDef<TData, TValue>, 'accessorFn'>>;

// Type for the return value of the column pinning helper function
export type PinnedColumnsProps = {
  isLastLeftPinnedColumn: boolean;
  left?: string;
};

// TS setup for custom features that we add to Tanstack table
export type DensityState = 'default' | 'compact';
export type HorizontalAlignState = 'left' | 'center' | 'right';
export type VerticalAlignState = 'top' | 'center' | 'bottom';
export type StickyHeaderState = boolean;
export type ColumnWidthOptionState = 'fit' | 'fixed' | 'fill';
export type CheckboxConfig = {
  id: string;
  headerConfig?: (table: Table<RowData>) => Omit<CheckboxOptions, 'defaultChecked'>;
  cellConfig: (row: Row<RowData>) => Omit<CheckboxOptions, 'defaultChecked'>;
};
export type RadioConfig = {
  id: string;
  cellConfig: (row: Row<RowData>) => Omit<RadioOptions, 'defaultChecked'>;
};
export type TableFieldProps = {
  /** Only applicable if radioConfig / checkboxConfig is passed. Legend title. */
  fieldTitle?: string;
  /** Only applicable if radioConfig / checkboxConfig is passed. */
  invalid?: boolean;
  /** Only applicable if radioConfig / checkboxConfig is passed and invalid is true. */
  errorText?: string;
  /** Only applicable if radioConfig / checkboxConfig is passed. Adds required badge. */
  required?: boolean;
  /** Only applicable if radioConfig / checkboxConfig is passed. Toggles the label's required indicator */
  showRequiredIndicator?: boolean;
};

export interface CustomTableState {
  /** Adjust the cell padding. */
  density?: DensityState;
  /** Adjust the horizontal alignment of cell contents. "left" by default */
  horizontalAlign?: HorizontalAlignState;
  /** Adjust the vertical alignment of cell contents. "center" by default */
  verticalAlign?: VerticalAlignState;
  /** Keep the header's position fixed during scrolling. */
  stickyHeader?: StickyHeaderState;
  /** Adjust the column width behavior. "fit" by default */
  columnWidthOption?: ColumnWidthOptionState;
  /** Width of the column, will only be used if columnWidthOption: 'fixed' meta option is passed. Uses our spacing scale. */
  width?: Exclude<SpacingKeysType, 0 | 0.5 | 1 | 1.5 | 2 | 3 | 3.5 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 14 | 'px' | 'auto'>;
  /** Configuration to turn the table into a form table, with checkboxes in the first column. */
  checkboxConfig?: CheckboxConfig;
  /** Configuration to turn the table into a form table, with radios in the first column. */
  radioConfig?: RadioConfig;
}

// define types for our new feature's table options
export interface CustomOptions extends CustomTableState {}

// define types for our new feature's column definition options
interface ColumnDefOptions extends Omit<CustomTableState, 'stickyHeader' | 'verticalAlign' | 'density'> {}

// Define types for our new feature's table APIs
interface CustomInstance {
  getStickyHeader: () => boolean;
}

// Define types for our new feature's column APIs
interface CustomColumn {
  getDensity: () => DensityState;
  getHorizontalAlign: () => HorizontalAlignState;
  getVerticalAlign: () => VerticalAlignState;
  getColumnWidthOption: () => ColumnWidthOptionState;
}

// Use declaration merging to add our new feature APIs and state types to TanStack Table's existing types.
declare module '@tanstack/react-table' {
  // merge our new feature's options with the existing table options
  interface TableOptionsResolved<TData extends RowData> extends CustomOptions {}
  // merge our new feature's instance APIs with the existing table instance APIs
  interface Table<TData extends RowData> extends CustomInstance {}
  // if you need to add column instance APIs...
  interface Column<TData extends RowData, TValue> extends CustomColumn {}
  // Note: declaration merging on `ColumnDef` is not possible because it is a type, not an interface.
  // interface ColumnDef.meta extends DensityTableState {}
  interface ColumnMeta<TData extends RowData, TValue> extends ColumnDefOptions {}
}

import styled from 'styled-components';
import { getColorVar } from '../colors';

import media from '../Breakpoints/Breakpoints';

export const HeaderTopNavBase = styled.div`
  background-color: ${getColorVar('utilityBackgroundLight')};
  border-bottom: 1px solid ${getColorVar('grey300')};

  ${media.mediumDown} {
    display: none;
  }
`;

export const HeaderTopNavInner = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: flex-end;
  margin: 0 auto;
  max-width: 1440px;
`;

import styled from 'styled-components';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';

const variants: VariantsType<'size'> = {
  size: {
    sm: {
      height: getSpacingVar(6),
      width: getSpacingVar(6),
    },
    md: {
      height: getSpacingVar(12),
      width: getSpacingVar(12),
    },
  },
};

const { useSx, getSxStyleRules } = sx('LoaderBase', ['margin'], variants);

export { useSx, variants };

export const LoaderBase = styled.svg`
  fill: none;
  stroke: ${getColorVar('interactiveActivePrimary')};
  stroke-width: 5px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  ${getSxStyleRules()}
`;

import React from 'react';
import { MarginSxPropType } from '../sx';
import { LoaderBase, useSx } from './LoaderBase';

export type LoaderProps = {
  size: 'md' | 'sm';
  sx?: MarginSxPropType;
};

const Loader: React.FC<LoaderProps> = ({ size, sx }) => {
  const sxOverrides = useSx(sx, { size });

  // For now there is only one loader, later on we can control depending on prop
  return (
    <LoaderBase focusable={false} viewBox="0 0 48 48" style={sxOverrides}>
      <path d="M41.5 24.0008C41.5 27.4619 40.4736 30.8454 38.5507 33.7232C36.6278 36.6011 33.8947 38.8441 30.697 40.1687C27.4993 41.4932 23.9806 41.8397 20.5859 41.1645C17.1913 40.4893 14.0731 38.8226 11.6256 36.3751C9.17821 33.9277 7.5115 30.8095 6.83626 27.4148C6.16102 24.0202 6.50757 20.5015 7.83211 17.3038C9.15664 14.1061 11.3997 11.373 14.2775 9.45004C17.1554 7.52712 20.5388 6.50076 24 6.50076" />
    </LoaderBase>
  );
};

export default Loader;

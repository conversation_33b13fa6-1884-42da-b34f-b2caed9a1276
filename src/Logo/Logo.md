### AXA & AXA Life logo:

```js
import Logo from '@axa-japan/design-system-react/Logo';
import Stack from '@axa-japan/design-system-react/Stack';

<Stack spacing={2} direction="column" sx={{ padding: '4' }}>
  <Logo brand="axa" />
  <Logo brand="axa-life" />
</Stack>;
```

### Variants:

```js
import Logo from '@axa-japan/design-system-react/Logo';
import Stack from '@axa-japan/design-system-react/Stack';

<Stack spacing={2} direction="column" sx={{ padding: '4', 'background-color': 'utilityBackgroundOcean' }}>
  <Logo brand="axa" variant="open-white" />
  <Logo brand="axa-life" variant="open-white" />
</Stack>;
```

### Clickable logo:

Wrap the logo in an &lt;a&gt; tag, or any other &lt;Link&gt; component to make a clickable logo.

```js
import Logo from '@axa-japan/design-system-react/Logo';
import Stack from '@axa-japan/design-system-react/Stack';

<a href="https://axa-japan-design-system.axa.co.jp/" target="_blank">
  <Logo brand="axa" />
</a>;
```

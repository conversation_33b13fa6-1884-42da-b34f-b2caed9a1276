import React from 'react';

export const AxaLifeOpenWhite = () => (
  <svg width="149" height="48" viewBox="0 0 149 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_20792_2397)">
      <path d="M0 0.000732422V48.0007H47.9981V0.000732422H0ZM46.8349 46.8375H1.16272V1.1644H46.8349V46.8375Z" fill="white" />
      <path d="M29.4679 23.7255L46.8349 1.16437H44.27L26.8438 23.7255H29.4679Z" fill="#FF1721" />
      <path
        d="M36.0656 33.787C36.861 36.0156 38.4992 41.8123 39.1507 42.2718H34.8441C34.8441 42.2718 34.8314 41.4139 34.64 40.7488C34.4537 40.0893 32.901 35.0777 32.901 35.0777H26.0602L24.9816 36.6007C24.9816 36.6007 26.2746 40.6476 26.3485 40.8447C26.4943 41.2003 27.1086 42.2713 27.1086 42.2713H22.9821C22.9821 42.2713 22.8801 41.6533 22.8377 41.3965C22.8006 41.1886 22.4459 40.0508 22.4459 40.0508C22.4459 40.0508 21.5076 41.0766 21.255 41.5568C20.9996 42.0234 20.8829 42.2718 20.8829 42.2718H17.6539C17.6539 42.2718 17.5457 41.6537 17.5072 41.3969C17.4752 41.189 17.0796 39.9609 17.0796 39.9609C17.0796 39.9609 16.177 41.062 15.9188 41.5333C15.6662 42.0074 15.5496 42.2718 15.5496 42.2718H12.3591C12.3591 42.2718 13.2646 41.4115 13.5778 41.0446C14.1088 40.4191 16.0938 37.8293 16.0938 37.8293L15.2974 35.0777H8.50406C8.50406 35.0777 4.63443 40.166 4.48203 40.3259C4.33011 40.4764 3.20078 42.0926 3.17068 42.2718H1.16272V40.9891C1.19988 40.9534 1.22669 40.9256 1.2408 40.9139C1.30336 40.8687 4.1763 37.3006 6.81266 33.787C9.18702 30.7222 11.4142 27.7368 11.6056 27.4536C12.0783 26.7679 12.757 25.2829 12.757 25.2829H16.2711C16.2711 25.2829 16.3807 26.647 16.4837 26.9781C16.5721 27.2716 18.7075 34.2903 18.7602 34.3665L19.9422 32.8552L17.9211 26.6286C17.9211 26.6286 17.4489 25.4621 17.2941 25.2829H21.39C21.39 25.2829 21.366 25.8878 21.5301 26.4C21.692 26.9123 22.5772 30.082 22.5772 30.082C22.5772 30.082 25.372 26.5774 25.531 26.33C25.8405 25.9076 25.8791 25.2825 25.8791 25.2825H29.292C29.292 25.2825 28.6711 25.742 27.5742 27.13C27.2069 27.5976 23.5974 32.1756 23.5974 32.1756C23.5974 32.1756 23.9106 33.2522 24.063 33.787C24.1082 33.9403 24.134 34.0424 24.134 34.0513C24.134 34.0612 24.2192 33.9639 24.3514 33.787C25.2827 32.6083 29.5178 27.0552 29.7732 26.5774C29.9792 26.1973 30.2859 25.7636 30.4613 25.2829H33.7971C33.7971 25.2829 33.8751 26.2825 33.9763 26.5534L36.0656 33.787ZM30.99 28.5326C30.5032 29.5858 27.6185 33.0899 27.6185 33.0899H32.1823C32.1823 33.0899 31.2985 30.3708 31.1452 29.756C31.0229 29.2683 31.0563 28.638 31.0563 28.5627C31.0563 28.5044 31.0431 28.4132 30.99 28.5326ZM13.4616 28.5326C12.972 29.5858 10.0948 33.0899 10.0948 33.0899H14.6578C14.6578 33.0899 13.7697 30.3708 13.6178 29.756C13.4974 29.2683 13.5294 28.638 13.5294 28.5627C13.5289 28.5044 13.5223 28.4132 13.4616 28.5326ZM20.3312 39.166L21.5856 37.4426C21.4695 37.3133 20.7625 35.1708 20.7625 35.1708L19.549 36.7456L20.3312 39.166Z"
        fill="white"
      />
      <path
        d="M140.089 32.8679L140.034 37.7606C140.034 38.6994 139.803 43.2285 139.803 43.2285L142.199 43.2304L142.186 38.5193C142.186 37.8561 142.281 35.541 142.309 34.8759H145.153V39.0193C145.153 39.4233 145.028 39.4934 144.652 39.4934L142.771 39.4788L143.044 41.6688C143.044 41.6688 144.192 41.5935 145.674 41.5935C146.138 41.5935 146.517 41.4519 146.796 41.1711C147.147 40.8165 147.324 40.2652 147.32 39.5286L147.358 32.8684L140.089 32.8679Z"
        fill="white"
      />
      <path
        d="M131.836 32.8679L131.863 37.1176C131.863 38.5616 131.639 41.7643 131.639 41.7643H138.819L138.847 36.9356C138.847 35.8279 139.016 32.8679 139.016 32.8679H131.836ZM134.051 34.9036H136.73L136.633 39.6768H133.98L134.051 34.9036Z"
        fill="white"
      />
      <path
        d="M144.069 27.9456C141.544 26.4391 141.375 25.4579 141.375 25.4579H138.404C138.404 25.4579 137.888 26.5421 135.258 28.0759C132.589 29.6323 130.759 29.994 130.759 29.994L130.763 32.2136C130.763 32.2136 132.695 31.668 134.495 30.9107L134.48 31.7983C134.48 31.7983 138.283 31.6548 139.722 31.7066C141.17 31.7579 144.833 31.7913 144.833 31.7913L144.894 30.8487C146.568 31.5523 148.668 32.223 148.668 32.223V29.9446C148.668 29.9446 146.696 29.5147 144.069 27.9456ZM142.589 29.6949L139.729 29.7259L136.927 29.6968C138.571 28.7156 139.506 27.8849 139.85 27.5543C140.162 27.9037 140.987 28.7175 142.589 29.6949Z"
        fill="white"
      />
      <path
        d="M124.567 40.5883C123.571 40.5883 122.581 40.6123 122.102 40.6231V36.7319C122.66 36.721 124.816 36.6529 125.435 36.6952C126.189 36.7483 127.992 36.8165 127.992 36.8165V34.4869C127.992 34.4869 126.763 34.5551 125.44 34.6082L122.102 34.6576V31.4055C122.677 31.4037 124.141 31.3999 124.742 31.4182L128.426 31.5579V29.2485C128.426 29.2485 125.55 29.32 124.802 29.32L122.147 29.3308C122.178 28.7485 122.215 27.0928 122.215 26.6958C122.215 26.2476 122.32 25.4612 122.32 25.4612H119.847C119.847 25.4612 119.871 26.1926 119.897 26.7067C119.919 27.129 119.904 28.772 119.9 29.3322H116.356C116.472 29.073 116.644 28.6836 116.757 28.3877C116.944 27.9155 117.299 26.6719 117.316 26.6182L117.385 26.3722H114.861L114.765 26.9089C114.765 26.9089 114.668 27.5905 113.733 29.8176C112.827 31.9653 111.859 32.3161 111.859 32.3161L111.857 35.0334C111.857 35.0334 113.279 34.5151 114.323 33.1778C114.705 32.6854 115.28 31.7117 115.46 31.4032L119.817 31.4051V34.6407C119.253 34.6223 117.096 34.5692 116.135 34.5692C114.973 34.5692 114.158 34.5085 114.158 34.5085L114.148 36.7921C114.148 36.7921 114.81 36.761 115.454 36.761C116.011 36.761 119.083 36.706 119.816 36.6858V40.5973C119.245 40.6029 117.207 40.6081 115.98 40.5879C114.508 40.5601 111.919 40.4778 111.919 40.4778V42.9472C111.919 42.9472 119.939 42.8296 120.579 42.8555C121.227 42.8813 128.766 42.9401 128.766 42.9401V40.4486C128.766 40.4486 125.802 40.5883 124.567 40.5883Z"
        fill="white"
      />
      <path
        d="M106.601 28.725L106.787 25.4692H104.239L104.267 28.7542L98.4017 28.7579L98.5616 25.473H96.072L96.0207 28.7528L91.9328 28.7048L91.9272 31.0843L96.0372 30.9794C96.0094 31.764 95.914 34.5052 95.914 34.8472C95.914 35.2277 95.7837 36.5447 95.7837 36.556L95.7616 36.7691H98.2822L98.2878 36.5833C98.2878 36.5739 98.3137 35.7823 98.2878 35.1026C98.2657 34.5222 98.3815 31.6539 98.413 30.8971H104.189V33.4088C104.189 35.5419 103.497 37.3222 102.183 38.5221C100.421 40.1335 97.1505 40.6665 97.1505 40.6665V43.0018C97.1505 43.0018 101.704 42.2266 104.254 39.7977C106.277 37.8721 106.486 34.8331 106.486 33.6186C106.486 33.2108 106.56 31.5387 106.582 30.9785C106.582 30.9785 109.123 30.9879 109.654 30.9879C109.845 30.9879 110.723 31.0373 110.723 31.0373V28.6342C110.723 28.6342 109.076 28.6672 109.65 28.6672C109.907 28.6662 106.601 28.725 106.601 28.725Z"
        fill="white"
      />
      <path
        d="M81.8653 27.6794L82.782 25.8074H80.0093L79.9246 26.2133C79.9227 26.2241 79.7336 27.5416 77.6885 30.0406C75.6509 32.5321 74.1886 32.9178 74.1886 32.9178L74.1792 35.319C74.1792 35.319 76.3235 34.6684 78.2854 32.8167C79.1048 32.0429 80.3761 30.3233 80.6593 29.9376H87.0369C86.8972 30.6798 86.2156 33.3402 83.3253 36.4215C81.3154 38.5691 78.0629 39.9397 77.2088 40.1787C76.3565 40.4195 75.7332 40.4708 75.7332 40.4708V42.9971C75.7332 42.9971 76.0954 42.8555 76.5728 42.7473C77.3922 42.5582 81.7082 41.3104 85.2782 37.8452C88.1573 35.0433 89.2857 30.9517 89.6384 29.32L89.6511 29.2612C89.7523 28.8073 90.0608 27.9532 90.0627 27.9456L90.1563 27.6883L81.8653 27.6794Z"
        fill="white"
      />
      <path
        d="M74.507 26.6136H65.383L64.1996 26.6173C61.9047 26.6192 57.7228 26.496 57.7228 26.496L57.7284 28.9729C57.7284 28.9729 59.5327 28.8534 60.33 28.8534L65.2264 28.8332H71.4549C71.3171 29.3717 70.8613 30.7936 69.6214 32.0137C68.3848 33.2282 66.946 33.5518 66.406 33.6341C66.43 33.3326 66.4648 32.7964 66.4648 32.359C66.4648 31.7452 66.5843 30.0458 66.5843 30.0458H64.1389L64.091 33.3806C64.091 33.9831 63.6921 36.6068 61.741 38.4534C59.8761 40.2154 57.989 40.522 57.989 40.522V42.982C57.989 42.982 61.4725 42.0135 63.5801 39.9595C65.3953 38.19 65.8807 36.5108 65.9888 36.0146C66.6887 35.9248 69.7423 35.4064 71.7324 33.1943C73.9115 30.7711 74.4534 26.9913 74.4774 26.8337L74.507 26.6136Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_20792_2397">
        <rect width="149" height="48" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AxaLifeSolidBlue = () => (
  <svg width="149" height="48" viewBox="0 0 149 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_25562_545)">
      <path d="M48 0.000732422H0V48.0007H48V0.000732422Z" fill="#00008F" />
      <path d="M29.7454 23.7051L48 0.000732422H45.3043L26.9885 23.7051H29.7454Z" fill="#FF1721" />
      <path
        d="M36.6805 34.2848C37.5121 36.6272 39.2356 42.7148 39.9218 43.2016H35.3936C35.3936 43.2016 35.3809 42.3051 35.1805 41.6033C34.9923 40.9086 33.3503 35.6427 33.3503 35.6427H26.1635L25.0346 37.2448C25.0346 37.2448 26.3907 41.4928 26.4749 41.704C26.6216 42.0732 27.2693 43.2016 27.2693 43.2016H22.9334C22.9334 43.2016 22.8229 42.5586 22.7763 42.2849C22.7467 42.0661 22.3662 40.8681 22.3662 40.8681C22.3662 40.8681 21.384 41.9467 21.1164 42.4462C20.8436 42.9443 20.7208 43.2012 20.7208 43.2012H17.3327C17.3327 43.2012 17.2222 42.5582 17.1766 42.2844C17.145 42.0657 16.7264 40.7722 16.7264 40.7722C16.7264 40.7722 15.7791 41.9297 15.5072 42.4241C15.2382 42.9236 15.1262 43.2012 15.1262 43.2012H11.7692C11.7692 43.2012 12.7174 42.3009 13.0509 41.9114C13.6093 41.2571 15.6925 38.5322 15.6925 38.5322L14.8553 35.6418H7.71974C7.71974 35.6418 3.65338 40.9866 3.49486 41.1522C3.32882 41.314 2.14301 43.0154 2.11667 43.2007H0V41.8578C0.0437445 41.8154 0.0710261 41.7882 0.0865484 41.7769C0.148167 41.7275 3.16983 37.9791 5.94455 34.2838C8.43611 31.0576 10.7734 27.9301 10.978 27.6304C11.4752 26.9047 12.1916 25.3501 12.1916 25.3501H15.8802C15.8802 25.3501 15.9907 26.7814 16.0971 27.1286C16.193 27.4352 18.4461 34.8144 18.4941 34.8897L19.7377 33.3003L17.6178 26.7645C17.6178 26.7645 17.1192 25.5316 16.9588 25.3496H21.2622C21.2622 25.3496 21.2279 25.9818 21.3995 26.518C21.5764 27.0599 22.5045 30.3911 22.5045 30.3911C22.5045 30.3911 25.4429 26.7052 25.6117 26.4409C25.9368 26.0015 25.9772 25.3496 25.9772 25.3496H29.5638C29.5638 25.3496 28.9086 25.8327 27.7557 27.2861C27.369 27.7748 23.5788 32.5943 23.5788 32.5943C23.5788 32.5943 23.909 33.7189 24.0699 34.2829C24.1131 34.4428 24.146 34.5548 24.146 34.5623C24.146 34.5731 24.2307 34.4668 24.3756 34.2829C25.3502 33.0463 29.7994 27.2127 30.0685 26.7076C30.2872 26.3068 30.6071 25.8515 30.7915 25.3515H34.2971C34.2971 25.3515 34.379 26.3971 34.4811 26.6892L36.6805 34.2848ZM31.3493 28.7617C30.8296 29.8694 27.8051 33.5538 27.8051 33.5538H32.6005C32.6005 33.5538 31.6706 30.693 31.5055 30.0482C31.3822 29.5336 31.4138 28.8741 31.4138 28.7932C31.4138 28.7358 31.4011 28.6422 31.3493 28.7617ZM12.9296 28.7617C12.4145 29.8694 9.38861 33.5534 9.38861 33.5534H14.1817C14.1817 33.5534 13.2537 30.6926 13.0914 30.0477C12.9606 29.5331 12.9973 28.8736 12.9973 28.7927C12.9973 28.7358 12.9888 28.6422 12.9296 28.7617ZM20.1455 39.9368L21.4682 38.1249C21.3408 37.9946 20.5985 35.7364 20.5985 35.7364L19.3252 37.3954L20.1455 39.9368Z"
        fill="white"
      />
      <path
        d="M140.093 32.8699L140.038 37.7627C140.038 38.7015 139.807 43.2307 139.807 43.2307L142.203 43.2326L142.19 38.5214C142.19 37.8582 142.286 35.543 142.313 34.8779H145.158V39.0214C145.158 39.4254 145.033 39.4955 144.656 39.4955L142.775 39.4809L143.049 41.671C143.049 41.671 144.197 41.5957 145.678 41.5957C146.143 41.5957 146.521 41.4542 146.801 41.1733C147.152 40.8187 147.328 40.2674 147.324 39.5308L147.363 32.8703L140.093 32.8699Z"
        fill="#00008F"
      />
      <path
        d="M131.84 32.8699L131.867 37.1197C131.867 38.5637 131.643 41.7665 131.643 41.7665H138.824L138.851 36.9376C138.851 35.8299 139.02 32.8699 139.02 32.8699H131.84ZM134.056 34.9056H136.734L136.637 39.679H133.984L134.056 34.9056Z"
        fill="#00008F"
      />
      <path
        d="M144.073 27.9474C141.549 26.4408 141.379 25.4596 141.379 25.4596H138.408C138.408 25.4596 137.892 26.5438 135.262 28.0777C132.593 29.6341 130.763 29.9959 130.763 29.9959L130.767 32.2155C130.767 32.2155 132.699 31.6699 134.499 30.9126L134.484 31.8002C134.484 31.8002 138.287 31.6567 139.726 31.7085C141.174 31.7598 144.838 31.7931 144.838 31.7931L144.898 30.8505C146.573 31.5542 148.672 32.2249 148.672 32.2249V29.9465C148.672 29.9465 146.701 29.5166 144.073 27.9474ZM142.594 29.6967L139.733 29.7277L136.931 29.6986C138.575 28.7174 139.51 27.8867 139.854 27.556C140.167 27.9055 140.992 28.7193 142.594 29.6967Z"
        fill="#00008F"
      />
      <path
        d="M124.571 40.5906C123.575 40.5906 122.584 40.6146 122.105 40.6254V36.7341C122.664 36.7232 124.819 36.655 125.438 36.6974C126.193 36.7505 127.996 36.8187 127.996 36.8187V34.489C127.996 34.489 126.767 34.5572 125.444 34.6103L122.105 34.6597V31.4076C122.68 31.4057 124.145 31.4019 124.745 31.4203L128.429 31.56V29.2504C128.429 29.2504 125.554 29.3219 124.806 29.3219L122.151 29.3328C122.182 28.7504 122.219 27.0947 122.219 26.6977C122.219 26.2495 122.324 25.463 122.324 25.463H119.851C119.851 25.463 119.875 26.1944 119.9 26.7086C119.922 27.131 119.908 28.774 119.904 29.3342H116.36C116.475 29.075 116.648 28.6855 116.76 28.3897C116.948 27.9174 117.302 26.6738 117.319 26.6201L117.389 26.3741H114.864L114.769 26.9108C114.769 26.9108 114.671 27.5924 113.736 29.8196C112.83 31.9673 111.862 32.3182 111.862 32.3182L111.86 35.0355C111.86 35.0355 113.282 34.5172 114.326 33.1799C114.708 32.6874 115.283 31.7138 115.464 31.4052L119.82 31.4071V34.6428C119.256 34.6244 117.099 34.5713 116.138 34.5713C114.977 34.5713 114.161 34.5106 114.161 34.5106L114.152 36.7943C114.152 36.7943 114.813 36.7632 115.458 36.7632C116.015 36.7632 119.087 36.7082 119.82 36.688V40.5996C119.248 40.6052 117.21 40.6104 115.983 40.5902C114.511 40.5624 111.923 40.4801 111.923 40.4801V42.9495C111.923 42.9495 119.943 42.832 120.582 42.8578C121.231 42.8837 128.769 42.9425 128.769 42.9425V40.4509C128.769 40.4509 125.806 40.5906 124.571 40.5906Z"
        fill="#00008F"
      />
      <path
        d="M106.605 28.7269L106.79 25.471H104.242L104.27 28.756L98.4046 28.7598L98.5645 25.4747H96.0749L96.0236 28.7546L91.9356 28.7067L91.9299 31.0863L96.04 30.9814C96.0123 31.766 95.9168 34.5073 95.9168 34.8492C95.9168 35.2298 95.7865 36.5468 95.7865 36.5581L95.7644 36.7712H98.2851L98.2908 36.5854C98.2908 36.576 98.3166 35.7843 98.2908 35.1046C98.2687 34.5242 98.3844 31.6559 98.4159 30.8991H104.193V33.4108C104.193 35.544 103.5 37.3243 102.186 38.5242C100.424 40.1357 97.1534 40.6687 97.1534 40.6687V43.0041C97.1534 43.0041 101.707 42.2289 104.257 39.7999C106.28 37.8742 106.489 34.8351 106.489 33.6206C106.489 33.2128 106.563 31.5406 106.585 30.9804C106.585 30.9804 109.126 30.9898 109.657 30.9898C109.848 30.9898 110.726 31.0392 110.726 31.0392V28.6361C110.726 28.6361 109.08 28.669 109.653 28.669C109.91 28.6681 106.605 28.7269 106.605 28.7269Z"
        fill="#00008F"
      />
      <path
        d="M81.8678 27.6811L82.7845 25.809H80.0117L79.927 26.2149C79.9251 26.2257 79.736 27.5432 77.6908 30.0423C75.6532 32.5339 74.1908 32.9196 74.1908 32.9196L74.1814 35.3208C74.1814 35.3208 76.3258 34.6703 78.2878 32.8185C79.1071 32.0447 80.3785 30.325 80.6617 29.9393H87.0395C86.8998 30.6816 86.2182 33.342 83.3278 36.4234C81.3179 38.5711 78.0653 39.9418 77.2111 40.1807C76.3588 40.4215 75.7355 40.4728 75.7355 40.4728V42.9992C75.7355 42.9992 76.0977 42.8576 76.5751 42.7494C77.3945 42.5603 81.7106 41.3124 85.2808 37.8472C88.1599 35.0452 89.2883 30.9534 89.6411 29.3217L89.6538 29.2629C89.7549 28.809 90.0635 27.9548 90.0654 27.9473L90.159 27.69L81.8678 27.6811Z"
        fill="#00008F"
      />
      <path
        d="M74.5092 26.6155H65.385L64.2015 26.6192C61.9066 26.6211 57.7245 26.4979 57.7245 26.4979L57.7301 28.9748C57.7301 28.9748 59.5345 28.8554 60.3318 28.8554L65.2283 28.8351H71.457C71.3192 29.3737 70.8634 30.7957 69.6235 32.0158C68.3869 33.2303 66.948 33.5539 66.408 33.6362C66.432 33.3347 66.4668 32.7985 66.4668 32.361C66.4668 31.7472 66.5863 30.0478 66.5863 30.0478H64.1408L64.0928 33.3827C64.0928 33.9852 63.694 36.609 61.7429 38.4557C59.8778 40.2177 57.9907 40.5243 57.9907 40.5243V42.9844C57.9907 42.9844 61.4743 42.0159 63.582 39.9618C65.3972 38.1922 65.8826 36.513 65.9908 36.0168C66.6907 35.9269 69.7444 35.4086 71.7345 33.1964C73.9137 30.7731 74.4556 26.9932 74.4796 26.8356L74.5092 26.6155Z"
        fill="#00008F"
      />
    </g>
    <defs>
      <clipPath id="clip0_25562_545">
        <rect width="149" height="48" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AxaOpenWhite = () => (
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_20792_2394)">
      <path d="M0 0.000732422V48.0007H47.9981V0.000732422H0ZM46.8349 46.8375H1.16272V1.1644H46.8349V46.8375Z" fill="white" />
      <path d="M29.4679 23.7255L46.8349 1.16437H44.2701L26.8438 23.7255H29.4679Z" fill="#FF1721" />
      <path
        d="M36.0656 33.787C36.861 36.0156 38.4992 41.8123 39.1507 42.2718H34.8441C34.8441 42.2718 34.8314 41.4139 34.64 40.7488C34.4537 40.0893 32.901 35.0777 32.901 35.0777H26.0602L24.9816 36.6007C24.9816 36.6007 26.2746 40.6476 26.3485 40.8447C26.4943 41.2003 27.1086 42.2713 27.1086 42.2713H22.9821C22.9821 42.2713 22.8801 41.6533 22.8377 41.3965C22.8006 41.1886 22.4459 40.0508 22.4459 40.0508C22.4459 40.0508 21.5076 41.0766 21.255 41.5568C20.9996 42.0234 20.8829 42.2718 20.8829 42.2718H17.6539C17.6539 42.2718 17.5457 41.6537 17.5072 41.3969C17.4752 41.189 17.0796 39.9609 17.0796 39.9609C17.0796 39.9609 16.177 41.062 15.9188 41.5333C15.6662 42.0075 15.5496 42.2718 15.5496 42.2718H12.3591C12.3591 42.2718 13.2646 41.4115 13.5778 41.0446C14.1088 40.4191 16.0938 37.8293 16.0938 37.8293L15.2974 35.0777H8.50406C8.50406 35.0777 4.63443 40.166 4.48203 40.3259C4.33011 40.4764 3.20078 42.0926 3.17068 42.2718H1.16272V40.9891C1.19988 40.9534 1.22669 40.9256 1.2408 40.9139C1.30336 40.8687 4.1763 37.3006 6.81266 33.787C9.18702 30.7222 11.4142 27.7368 11.6056 27.4536C12.0783 26.7679 12.757 25.2829 12.757 25.2829H16.2711C16.2711 25.2829 16.3807 26.647 16.4837 26.9781C16.5721 27.2716 18.7075 34.2903 18.7602 34.3665L19.9422 32.8552L17.9211 26.6286C17.9211 26.6286 17.4489 25.4621 17.2941 25.2829H21.39C21.39 25.2829 21.366 25.8878 21.5301 26.4C21.6919 26.9123 22.5772 30.082 22.5772 30.082C22.5772 30.082 25.372 26.5774 25.531 26.33C25.8405 25.9076 25.8791 25.2825 25.8791 25.2825H29.292C29.292 25.2825 28.6711 25.742 27.5742 27.13C27.2069 27.5976 23.5974 32.1756 23.5974 32.1756C23.5974 32.1756 23.9106 33.2522 24.063 33.787C24.1082 33.9403 24.134 34.0424 24.134 34.0513C24.134 34.0612 24.2192 33.9639 24.3514 33.787C25.2827 32.6083 29.5178 27.0552 29.7732 26.5774C29.9792 26.1973 30.2859 25.7636 30.4613 25.2829H33.7971C33.7971 25.2829 33.8751 26.2825 33.9763 26.5534L36.0656 33.787ZM30.99 28.5326C30.5032 29.5858 27.6185 33.0899 27.6185 33.0899H32.1823C32.1823 33.0899 31.2985 30.3708 31.1452 29.756C31.0229 29.2683 31.0563 28.638 31.0563 28.5627C31.0563 28.5044 31.0431 28.4132 30.99 28.5326ZM13.4616 28.5326C12.972 29.5858 10.0948 33.0899 10.0948 33.0899H14.6578C14.6578 33.0899 13.7697 30.3708 13.6178 29.756C13.4974 29.2683 13.5294 28.638 13.5294 28.5627C13.5289 28.5044 13.5223 28.4132 13.4616 28.5326ZM20.3312 39.166L21.5856 37.4426C21.4695 37.3133 20.7625 35.1708 20.7625 35.1708L19.549 36.7456L20.3312 39.166Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_20792_2394">
        <rect width="48" height="48" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AxaSolidBlue = () => (
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_25551_961)">
      <path d="M48 0.000732422H0V48.0007H48V0.000732422Z" fill="#00008F" />
      <path d="M29.7454 23.7051L48 0.000732422H45.3043L26.9885 23.7051H29.7454Z" fill="#FF1721" />
      <path
        d="M36.6805 34.2848C37.5121 36.6272 39.2356 42.7148 39.9218 43.2016H35.3936C35.3936 43.2016 35.3809 42.3051 35.1805 41.6033C34.9923 40.9086 33.3503 35.6427 33.3503 35.6427H26.1635L25.0346 37.2448C25.0346 37.2448 26.3907 41.4928 26.4749 41.704C26.6216 42.0732 27.2693 43.2016 27.2693 43.2016H22.9334C22.9334 43.2016 22.8229 42.5586 22.7763 42.2849C22.7467 42.0661 22.3662 40.8681 22.3662 40.8681C22.3662 40.8681 21.384 41.9467 21.1164 42.4462C20.8436 42.9443 20.7208 43.2012 20.7208 43.2012H17.3327C17.3327 43.2012 17.2222 42.5582 17.1766 42.2844C17.145 42.0657 16.7264 40.7722 16.7264 40.7722C16.7264 40.7722 15.7791 41.9297 15.5072 42.4241C15.2382 42.9236 15.1262 43.2012 15.1262 43.2012H11.7692C11.7692 43.2012 12.7174 42.3009 13.0509 41.9114C13.6093 41.2571 15.6925 38.5322 15.6925 38.5322L14.8553 35.6418H7.71974C7.71974 35.6418 3.65338 40.9866 3.49486 41.1522C3.32882 41.314 2.14301 43.0154 2.11667 43.2007H0V41.8578C0.0437445 41.8154 0.0710261 41.7882 0.0865484 41.7769C0.148167 41.7275 3.16983 37.9791 5.94455 34.2838C8.43611 31.0576 10.7734 27.9301 10.978 27.6304C11.4752 26.9047 12.1916 25.3501 12.1916 25.3501H15.8802C15.8802 25.3501 15.9907 26.7814 16.0971 27.1286C16.193 27.4352 18.4461 34.8144 18.4941 34.8897L19.7377 33.3003L17.6178 26.7645C17.6178 26.7645 17.1192 25.5316 16.9588 25.3496H21.2622C21.2622 25.3496 21.2279 25.9818 21.3995 26.518C21.5764 27.0599 22.5045 30.3911 22.5045 30.3911C22.5045 30.3911 25.4429 26.7052 25.6117 26.4409C25.9368 26.0015 25.9772 25.3496 25.9772 25.3496H29.5638C29.5638 25.3496 28.9086 25.8327 27.7557 27.2861C27.369 27.7748 23.5788 32.5943 23.5788 32.5943C23.5788 32.5943 23.909 33.7189 24.0699 34.2829C24.1131 34.4428 24.146 34.5548 24.146 34.5623C24.146 34.5731 24.2307 34.4668 24.3756 34.2829C25.3502 33.0463 29.7994 27.2127 30.0685 26.7076C30.2872 26.3068 30.6071 25.8515 30.7915 25.3515H34.2971C34.2971 25.3515 34.379 26.3971 34.4811 26.6892L36.6805 34.2848ZM31.3493 28.7617C30.8296 29.8694 27.8051 33.5538 27.8051 33.5538H32.6005C32.6005 33.5538 31.6706 30.693 31.5055 30.0482C31.3822 29.5336 31.4138 28.8741 31.4138 28.7932C31.4138 28.7358 31.4011 28.6422 31.3493 28.7617ZM12.9296 28.7617C12.4145 29.8694 9.38861 33.5534 9.38861 33.5534H14.1817C14.1817 33.5534 13.2537 30.6926 13.0914 30.0477C12.9606 29.5331 12.9973 28.8736 12.9973 28.7927C12.9973 28.7358 12.9888 28.6422 12.9296 28.7617ZM20.1455 39.9368L21.4682 38.1249C21.3408 37.9946 20.5985 35.7364 20.5985 35.7364L19.3252 37.3954L20.1455 39.9368Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_25551_961">
        <rect width="48" height="48" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

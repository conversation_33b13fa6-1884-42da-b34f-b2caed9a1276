import React from 'react';
import { render } from '../../utils/testUtils';
import Logo from '../Logo';

describe('Logo', () => {
  ['solid-blue', 'open-white'].forEach((variant) => {
    ['axa', 'axa-life'].forEach((brand) => {
      test(`renders with variant ${variant} and brand ${brand} without crashing`, () => {
        const { container } = render(<Logo variant={variant as 'solid-blue' | 'open-white'} brand={brand as 'axa' | 'axa-life'} />);
        expect(container).toMatchSnapshot();
      });
    });
  });
});

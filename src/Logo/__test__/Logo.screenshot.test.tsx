import React from 'react';
import Logo from '../Logo';
import { getColorVar } from '../../colors';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Logo', () => {
  test('axaLogo', async ({ task }) => {
    await takeScreenshot(<Logo brand="axa" />, task);
  });

  test('axaLifeLogo', async ({ task }) => {
    await takeScreenshot(<Logo brand="axa-life" />, task);
  });

  test('axaLogoOpenWhite', async ({ task }) => {
    await takeScreenshot(
      <div style={{ backgroundColor: getColorVar('utilityBackgroundOcean') }}>
        <Logo brand="axa" variant="open-white" />
      </div>,
      task,
    );
  });

  test('axaLifeLogoOpenWhite', async ({ task }) => {
    await takeScreenshot(
      <div style={{ backgroundColor: getColorVar('utilityBackgroundOcean') }}>
        <Logo brand="axa-life" variant="open-white" />
      </div>,
      task,
    );
  });
});

import React from 'react';
import { AxaLifeOpenWhite, AxaLifeSolidBlue, AxaOpenWhite, AxaSolidBlue } from './Svg';

export type LogoProps = {
  brand: 'axa' | 'axa-life';
  variant?: 'solid-blue' | 'open-white';
};

const Logo: React.FC<LogoProps> = ({ variant, brand }) => {
  if (brand === 'axa-life') {
    return variant === 'open-white' ? <AxaLifeOpenWhite /> : <AxaLifeSolidBlue />;
  }

  return variant === 'open-white' ? <AxaOpenWhite /> : <AxaSolidBlue />;
};

export default Logo;

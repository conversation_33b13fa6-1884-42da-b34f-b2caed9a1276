// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Stepper > renders with variant compact and currentStep 0 with Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c7 {
  font-size: 0.875rem;
  line-height: 1.1428571;
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
  cursor: default;
}

.c6 > p:first-child {
  font-weight: var(--ajds-font-weight-bold);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="loading"
    data-value="1"
    dir="ltr"
    id="progress-:r0:"
    style="--percent: 33;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="33 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="1"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="loading"
        dir="ltr"
        id="progress-:r0:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="loading"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 33; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          1/3
        </p>
      </span>
    </div>
    <div
      class="c6"
      data-orientation="horizontal"
      data-part="label"
      data-scope="progress"
      dir="ltr"
      id="progress-:r0:-label"
    >
      <p
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        ステップ１
      </p>
      <p
        class="c5 c7"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        次：ステップ２
      </p>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant compact and currentStep 0 without Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="loading"
    data-value="1"
    dir="ltr"
    id="progress-:r3:"
    style="--percent: 33;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="33 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="1"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="loading"
        dir="ltr"
        id="progress-:r3:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="loading"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 33; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          1/3
        </p>
      </span>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant compact and currentStep 1 with Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c7 {
  font-size: 0.875rem;
  line-height: 1.1428571;
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
  cursor: default;
}

.c6 > p:first-child {
  font-weight: var(--ajds-font-weight-bold);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="loading"
    data-value="2"
    dir="ltr"
    id="progress-:r1:"
    style="--percent: 67;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="67 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="2"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="loading"
        dir="ltr"
        id="progress-:r1:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="loading"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 67; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          2/3
        </p>
      </span>
    </div>
    <div
      class="c6"
      data-orientation="horizontal"
      data-part="label"
      data-scope="progress"
      dir="ltr"
      id="progress-:r1:-label"
    >
      <p
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        ステップ２
      </p>
      <p
        class="c5 c7"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        次：ステップ３
      </p>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant compact and currentStep 1 without Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="loading"
    data-value="2"
    dir="ltr"
    id="progress-:r4:"
    style="--percent: 67;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="67 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="2"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="loading"
        dir="ltr"
        id="progress-:r4:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="loading"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 67; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          2/3
        </p>
      </span>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant compact and currentStep 2 with Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
  cursor: default;
}

.c6 > p:first-child {
  font-weight: var(--ajds-font-weight-bold);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="complete"
    data-value="3"
    dir="ltr"
    id="progress-:r2:"
    style="--percent: 100;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="100 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="3"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="complete"
        dir="ltr"
        id="progress-:r2:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="complete"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 100; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          3/3
        </p>
      </span>
    </div>
    <div
      class="c6"
      data-orientation="horizontal"
      data-part="label"
      data-scope="progress"
      dir="ltr"
      id="progress-:r2:-label"
    >
      <p
        class="c5"
        style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
      >
        ステップ３
      </p>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant compact and currentStep 2 without Step object without crashing 1`] = `
.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-6);
  --size: var(--ajds-spacing-20);
  --thickness: var(--ajds-spacing-1-5);
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
  font-weight: var(--ajds-font-weight-bold);
  font-size: 1rem;
}

.c2 {
  stroke: var(--ajds-color-utility-stroke-light);
}

.c3 {
  stroke: var(--ajds-color-interactive-active-primary);
}

.c1 {
  display: inline-block;
  position: relative;
  line-height: 0;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  cursor: default;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
    data-max="3"
    data-orientation="horizontal"
    data-part="root"
    data-scope="progress"
    data-state="complete"
    data-value="3"
    dir="ltr"
    id="progress-:r5:"
    style="--percent: 100;"
  >
    <div
      class="c1"
    >
      <svg
        aria-label="100 percent"
        aria-valuemax="3"
        aria-valuemin="0"
        aria-valuenow="3"
        data-max="3"
        data-orientation="horizontal"
        data-part="circle"
        data-scope="progress"
        data-state="complete"
        dir="ltr"
        id="progress-:r5:-circle"
        role="progressbar"
        style="width: var(--size); height: var(--size);"
      >
        <circle
          class="c2"
          data-orientation="horizontal"
          data-part="circle-track"
          data-scope="progress"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness);"
        />
        <circle
          class="c3"
          data-part="circle-range"
          data-scope="progress"
          data-state="complete"
          dir="ltr"
          style="--radius: calc(var(--size) / 2 - var(--thickness) / 2); fill: transparent; stroke-width: var(--thickness); --percent: 100; --circumference: calc(2 * 3.14159 * var(--radius)); --offset: calc(var(--circumference) * (100 - var(--percent)) / 100); stroke-dashoffset: calc(var(--circumference) * ((100 - var(--percent)) / 100)); stroke-dasharray: var(--circumference); transform-origin: center; transform: rotate(-90deg);"
        />
      </svg>
      <span
        aria-live="polite"
        class="c4"
        data-part="value-text"
        data-scope="progress"
        dir="ltr"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          3/3
        </p>
      </span>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 0 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c4 {
  padding: 0 var(--ajds-spacing-4);
  text-align: center;
  cursor: default;
  word-break: break-all;
}

.c4 > p {
  font-weight: var(--ajds-font-weight-bold);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 0 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 1 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c4 {
  padding: 0 var(--ajds-spacing-4);
  text-align: center;
  cursor: default;
  word-break: break-all;
}

.c4 > p {
  font-weight: var(--ajds-font-weight-bold);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 1 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 2 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c4 {
  padding: 0 var(--ajds-spacing-4);
  text-align: center;
  cursor: default;
  word-break: break-all;
}

.c4 > p {
  font-weight: var(--ajds-font-weight-bold);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and currentStep 2 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant horizontal and showCompletedIcon true without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c5 {
  padding: 0 var(--ajds-spacing-4);
  text-align: center;
  cursor: default;
  word-break: break-all;
}

.c5 > p {
  font-weight: var(--ajds-font-weight-bold);
}

.c6 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
            />
          </svg>
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="done"
        data-right-path-status="todo"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="todo"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 0 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c6 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c6[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c6[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: default;
  word-break: break-all;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 0 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c4 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c4[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c4[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 1 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c6 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c6[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c6[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: default;
  word-break: break-all;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 1 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c4 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c4[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c4[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 2 with Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c6 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c6[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c6[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: default;
  word-break: break-all;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c6"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          3
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and currentStep 2 without Step object without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c4 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c4[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c4[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
    </div>
    <div
      class="c4"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          3
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders with variant vertical and showCompletedIcon true without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c6 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: grid;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-4);
}

.c2 {
  display: grid;
  grid-template-rows: 1fr var(--ajds-spacing-10) 1fr;
  height: 100%;
  justify-items: center;
}

.c2[data-prev-path-status='todo']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-prev-path-status='done']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-prev-path-status='end']::before {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c2[data-next-path-status='todo']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-next-path-status='done']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-next-path-status='end']::after {
  content: '';
  height: 100%;
  width: var(--ajds-spacing-1);
  pointer-events: none;
}

.c7 {
  width: var(--ajds-spacing-1);
  height: var(--ajds-spacing-6);
  margin-left: 1.11rem;
}

.c7[data-next-path-status='todo'] {
  background-color: var(--ajds-color-utility-stroke-light);
}

.c7[data-next-path-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: default;
  word-break: break-all;
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="done"
        data-prev-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
            />
          </svg>
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ１
        </p>
      </div>
    </div>
    <div
      class="c7"
      data-next-path-status="done"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="todo"
        data-prev-path-status="done"
      >
        <p
          class="c3"
          data-status="done"
        >
          2
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ２
        </p>
      </div>
    </div>
    <div
      class="c7"
      data-next-path-status="todo"
    />
    <div
      class="c1"
    >
      <div
        class="c2"
        data-next-path-status="end"
        data-prev-path-status="todo"
      >
        <p
          class="c3"
          data-status="todo"
        >
          3
        </p>
      </div>
      <div
        class="c5"
      >
        <p
          class="c6"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          ステップ３
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Stepper > renders without crashing 1`] = `
.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: var(--ajds-spacing-10);
  width: var(--ajds-spacing-10);
  border-radius: var(--ajds-radius-full);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: var(--ajds-color-character-primary-white);
  font-weight: var(--ajds-font-weight-bold);
}

.c3[data-status='todo'] {
  background-color: var(--ajds-color-interactive-disabled-dark);
}

.c3[data-status='done'] {
  background-color: var(--ajds-color-interactive-active-primary);
}

.c0 {
  display: grid;
  grid-auto-columns: minmax(0,1fr);
  grid-auto-flow: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: var(--ajds-spacing-2);
}

.c2 {
  display: grid;
  grid-template-columns: 1fr var(--ajds-spacing-10) 1fr;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c2[data-left-path-status='todo']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-left-path-status='done']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-left-path-status='end']::before {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c2[data-right-path-status='todo']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-utility-stroke-light);
}

.c2[data-right-path-status='done']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
  background-color: var(--ajds-color-interactive-active-primary);
}

.c2[data-right-path-status='end']::after {
  content: '';
  height: var(--ajds-spacing-1);
  width: 100%;
  pointer-events: none;
}

.c4 {
  padding: 0 var(--ajds-spacing-4);
  text-align: center;
  cursor: default;
  word-break: break-all;
}

.c4 > p {
  font-weight: var(--ajds-font-weight-bold);
}

.c5 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

<div>
  <div
    aria-hidden="true"
    class="c0"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
        data-left-path-status="end"
        data-right-path-status="end"
      >
        <p
          class="c3"
          data-status="done"
        >
          1
        </p>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-bold); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: center;"
        >
          ステップ１
        </p>
      </div>
    </div>
  </div>
</div>
`;

import React from 'react';
import { render } from '../../utils/testUtils';
import Stepper, { Step } from '../Stepper';

describe('Stepper', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(
      <Stepper
        variant="horizontal"
        steps={[
          {
            title: 'ステップ１',
          },
        ]}
        currentStep={0}
      />,
    );
    const testElement = getByText(/ステップ１/i);
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  (['horizontal', 'vertical', 'compact'] as const).forEach((variant) => {
    ([0, 1, 2] as const).forEach((currentStep) => {
      test(`renders with variant ${variant} and currentStep ${currentStep} with Step object without crashing`, () => {
        const steps: Step[] = [
          {
            title: 'ステップ１',
          },
          {
            title: 'ステップ２',
          },
          {
            title: 'ステップ３',
          },
        ];
        const { getByText, container } = render(<Stepper variant={variant} steps={steps} currentStep={currentStep} />);
        const testElement = getByText(steps[currentStep].title);
        expect(testElement).toBeInTheDocument();
        expect(container).toMatchSnapshot();
      });
    });
  });

  (['horizontal', 'vertical', 'compact'] as const).forEach((variant) => {
    ([0, 1, 2] as const).forEach((currentStep) => {
      test(`renders with variant ${variant} and currentStep ${currentStep} without Step object without crashing`, () => {
        const totalSteps = 3;
        const { getByText, container } = render(<Stepper variant={variant} steps={totalSteps} currentStep={currentStep} />);
        let testElement;

        if (variant === 'compact') {
          testElement = getByText(`${currentStep + 1}/${totalSteps}`);
        } else {
          testElement = getByText(currentStep + 1);
        }

        expect(testElement).toBeInTheDocument();
        expect(container).toMatchSnapshot();
      });
    });
  });

  (['horizontal', 'vertical'] as const).forEach((variant) => {
    test(`renders with variant ${variant} and showCompletedIcon true without crashing`, () => {
      const steps: Step[] = [
        {
          title: 'ステップ１',
        },
        {
          title: 'ステップ２',
        },
        {
          title: 'ステップ３',
        },
      ];
      const { getByText, queryByText, container } = render(<Stepper variant={variant} steps={steps} currentStep={1} showCompletedIcon={true} />);
      const testElement = getByText(steps[1].title);
      expect(testElement).toBeInTheDocument();
      expect(queryByText('1')).not.toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});

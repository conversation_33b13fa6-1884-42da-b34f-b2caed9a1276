import React from 'react';
import Stepper from '../Stepper';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

const steps = [
  {
    title: 'ステップ１',
  },
  {
    title:
      'ダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロードダウンロード',
  },
  {
    title: 'ステップ３',
  },
];

describe('Stepper', () => {
  test('Horizontal1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={steps} currentStep={0} />, task, {
      viewport: { height: 900 },
    });
  });

  test('Horizontal2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={steps} currentStep={1} />, task, {
      viewport: { height: 900 },
    });
  });

  test('Horizontal3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={steps} currentStep={2} />, task, {
      viewport: { height: 900 },
    });
  });

  test('HorizontalNumbers1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={3} currentStep={0} />, task);
  });

  test('HorizontalNumbers2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={3} currentStep={1} />, task);
  });

  test('HorizontalNumbers3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={3} currentStep={2} />, task);
  });

  test('HorizontalWithShowCompletedIcon', async ({ task }) => {
    await takeScreenshot(<Stepper variant="horizontal" steps={3} currentStep={1} showCompletedIcon />, task);
  });

  test('Vertical1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={steps} currentStep={0} />, task, {
      viewport: { height: 900 },
    });
  });

  test('Vertical2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={steps} currentStep={1} />, task, {
      viewport: { height: 900 },
    });
  });

  test('Vertical3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={steps} currentStep={2} />, task, {
      viewport: { height: 900 },
    });
  });

  test('VerticalNumbers1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={3} currentStep={0} />, task);
  });

  test('VerticalNumbers2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={3} currentStep={1} />, task);
  });

  test('VerticalNumbers3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={3} currentStep={2} />, task);
  });

  test('VerticalWithShowCompletedIcon', async ({ task }) => {
    await takeScreenshot(<Stepper variant="vertical" steps={3} currentStep={1} showCompletedIcon />, task);
  });

  test('Compact1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={steps} currentStep={0} />, task);
  });

  test('Compact2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={steps} currentStep={1} />, task, {
      viewport: { height: 900 },
    });
  });

  test('Compact3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={steps} currentStep={2} />, task);
  });

  test('CompactNumbers1', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={3} currentStep={0} />, task);
  });

  test('CompactNumbers2', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={3} currentStep={1} />, task);
  });

  test('CompactNumbers3', async ({ task }) => {
    await takeScreenshot(<Stepper variant="compact" steps={3} currentStep={2} />, task);
  });
});

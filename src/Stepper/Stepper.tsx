import React from 'react';
import HorizontalStepper from './HorizontalStepper';
import VerticalStepper from './VerticalStepper';
import CompactStepper from './CompactStepper';

export type Step = {
  title: string;
};

export type StepperProps = {
  /** The stepper variant. */
  variant: 'horizontal' | 'vertical' | 'compact';
  /** A list of steps or number of steps. */
  steps: Step[] | number;
  /** The current index starting from 0. Accepts values equal to num steps to show a fully completed stepper. */
  currentStep: number;
  /** If set to 'true' and variant is horizontal or vertical, then the step label will be a check icon. */
  showCompletedIcon?: boolean;
  'data-testid'?: string;
};

const Stepper: React.FC<StepperProps> = ({ variant, steps, currentStep, showCompletedIcon = false, 'data-testid': dataTestId }) => {
  const formattedSteps = typeof steps === 'number' ? Array(steps).fill(undefined) : steps.filter((step) => step !== undefined);
  const boundedStep = Math.max(0, currentStep);

  if (variant === 'vertical')
    return <VerticalStepper steps={formattedSteps} currentStep={boundedStep} showCompletedIcon={showCompletedIcon} data-testid={dataTestId} />;
  if (variant === 'compact')
    return <CompactStepper steps={formattedSteps} currentStep={boundedStep} showCompletedIcon={showCompletedIcon} data-testid={dataTestId} />;
  return <HorizontalStepper steps={formattedSteps} currentStep={boundedStep} showCompletedIcon={showCompletedIcon} data-testid={dataTestId} />;
};

export default Stepper;

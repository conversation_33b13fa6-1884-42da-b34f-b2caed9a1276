import React from 'react';
import { Progress } from '@ark-ui/react';
import Text from '../Text';
import {
  CircleAndValueContainer,
  SecondaryText,
  StyledProgressCircleRange,
  StyledProgressCircleTrack,
  StyledProgressRoot,
  StyledProgressValue,
  TitleContainer,
} from './CompactStepper.styles';
import { CheckIcon } from '../Icons';

type Step = {
  title: string;
};

export type CompactStepperProps = {
  steps: Step[] | null[];
  currentStep: number;
  showCompletedIcon: boolean;
  'data-testid'?: string;
};

const CompactStepper: React.FC<CompactStepperProps> = ({ steps, currentStep, showCompletedIcon, 'data-testid': dataTestId }) => {
  const boundedCurrentStep = Math.min(Math.max(currentStep, -1), steps.length - 1);

  const currentStepTitle = steps[boundedCurrentStep]?.title;
  const nextStepTitle = steps[boundedCurrentStep + 1]?.title || null;

  return (
    <StyledProgressRoot value={boundedCurrentStep + 1} max={steps.length} aria-hidden>
      <CircleAndValueContainer>
        <Progress.Circle>
          <StyledProgressCircleTrack />
          <StyledProgressCircleRange />
        </Progress.Circle>
        <StyledProgressValue data-testid={dataTestId !== undefined ? `${dataTestId}-icon` : undefined}>
          {currentStep >= steps.length && showCompletedIcon ? (
            <CheckIcon isStroke size="large" />
          ) : (
            <Text weight="bold">{`${boundedCurrentStep + 1}/${steps.length}`}</Text>
          )}
        </StyledProgressValue>
      </CircleAndValueContainer>
      {(currentStepTitle || nextStepTitle) && (
        <Progress.Label asChild>
          <TitleContainer>
            <Text weight="bold" data-testid={dataTestId !== undefined ? `${dataTestId}-title` : undefined}>
              {currentStepTitle}
            </Text>
            {nextStepTitle && <SecondaryText color="secondary">{`次：${nextStepTitle}`}</SecondaryText>}
          </TitleContainer>
        </Progress.Label>
      )}
    </StyledProgressRoot>
  );
};

export default CompactStepper;

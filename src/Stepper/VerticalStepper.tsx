import React from 'react';
import { StepperIcon } from './Stepper.styles';
import { IconContainer, Path, StepContainer, StepperContainer, TitleContainer } from './VerticalStepper.styles';
import Text from '../Text';
import { CheckIcon } from '../Icons';

type Step = {
  title: string;
};

export type VerticalStepperProps = {
  steps: Step[] | null[];
  currentStep: number;
  showCompletedIcon?: boolean;
  'data-testid'?: string;
};

const VerticalStepper: React.FC<VerticalStepperProps> = ({ steps, currentStep, showCompletedIcon, 'data-testid': dataTestId }) => {
  return (
    <StepperContainer aria-hidden>
      {steps.map((step, i) => {
        const title = step?.title;
        const currStatus = i <= currentStep ? 'done' : 'todo';
        const nextStatus = i + 1 <= currentStep ? 'done' : 'todo';
        const displayCheckIcon = showCompletedIcon && nextStatus === 'done';

        return (
          <React.Fragment key={`stepper-step-${i}`}>
            <StepContainer>
              <IconContainer
                data-prev-path-status={i === 0 ? 'end' : currStatus}
                data-next-path-status={i === steps.length - 1 ? 'end' : nextStatus}
                data-testid={dataTestId !== undefined ? `${dataTestId}-icon` : undefined}
              >
                <StepperIcon data-status={currStatus}>{displayCheckIcon ? <CheckIcon isStroke /> : i + 1}</StepperIcon>
              </IconContainer>
              {title && (
                <TitleContainer>
                  <Text
                    weight="bold"
                    color={currStatus === 'done' ? 'primary' : 'secondary'}
                    data-testid={dataTestId !== undefined ? `${dataTestId}-title` : undefined}
                  >
                    {title}
                  </Text>
                </TitleContainer>
              )}
            </StepContainer>
            {i < steps.length - 1 && <Path data-next-path-status={nextStatus} />}
          </React.Fragment>
        );
      })}
    </StepperContainer>
  );
};

export default VerticalStepper;

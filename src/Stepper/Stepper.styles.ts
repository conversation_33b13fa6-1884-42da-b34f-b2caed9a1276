import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';
import { getRadiusVar } from '../radius';

export const pathColorMap = {
  done: getColorVar('interactiveActivePrimary'),
  todo: getColorVar('utilityStrokeLight'),
} as const;

export const StepperIcon = styled.p`
  display: flex;
  align-items: center;
  justify-content: center;
  height: ${getSpacingVar(10)};
  width: ${getSpacingVar(10)};
  border-radius: ${getRadiusVar('full')};
  user-select: none;
  color: ${getColorVar('characterPrimaryWhite')};
  font-weight: ${getTypographyVar('boldFontWeight')};

  &[data-status='todo'] {
    background-color: ${getColorVar('interactiveDisabledDark')};
  }

  &[data-status='done'] {
    background-color: ${getColorVar('interactiveActivePrimary')};
  }
`;

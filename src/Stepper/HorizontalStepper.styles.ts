import styled, { css } from 'styled-components';
import { pathColorMap } from './Stepper.styles';
import { getSpacingVar } from '../spacing';
import { getTypographyVar } from '../typography';

export const StepperContainer = styled.div`
  display: grid;
  grid-auto-columns: minmax(0, 1fr);
  grid-auto-flow: column;
  align-items: start;
`;

export const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: ${getSpacingVar(2)};
`;

const PathStyles = css`
  content: '';
  height: ${getSpacingVar(1)};
  width: 100%;
  pointer-events: none;
`;

export const IconContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr ${getSpacingVar(10)} 1fr;
  width: 100%;
  align-items: center;
  justify-content: center;

  &[data-left-path-status='todo'] {
    &::before {
      ${PathStyles}
      background-color: ${pathColorMap.todo};
    }
  }

  &[data-left-path-status='done'] {
    &::before {
      ${PathStyles}
      background-color: ${pathColorMap.done};
    }
  }

  &[data-left-path-status='end'] {
    &::before {
      ${PathStyles}
    }
  }

  &[data-right-path-status='todo'] {
    &::after {
      ${PathStyles}
      background-color: ${pathColorMap.todo};
    }
  }

  &[data-right-path-status='done'] {
    &::after {
      ${PathStyles}
      background-color: ${pathColorMap.done};
    }
  }

  &[data-right-path-status='end'] {
    &::after {
      ${PathStyles}
    }
  }
`;

export const TitleContainer = styled.div`
  padding: 0 ${getSpacingVar(4)};
  text-align: center;
  cursor: default;
  word-break: break-all;

  & > p {
    /* We will temporarily override Text component is updated */
    font-weight: ${getTypographyVar('boldFontWeight')};
  }
`;

import React from 'react';
import { StepperIcon } from './Stepper.styles';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, StepContainer, IconContainer, TitleContainer } from './HorizontalStepper.styles';
import Text from '../Text';
import { CheckIcon } from '../Icons';

type Step = {
  title: string;
};

type HorizontalStepperProps = {
  steps: Step[] | undefined[];
  currentStep: number;
  showCompletedIcon?: boolean;
  'data-testid'?: string;
};

const HorizontalStepper: React.FC<HorizontalStepperProps> = ({ steps, currentStep, showCompletedIcon, 'data-testid': dataTestId }) => {
  return (
    <StepperContainer aria-hidden>
      {steps.map((step, i) => {
        const title = step?.title;
        const currStatus = i <= currentStep ? 'done' : 'todo';
        const nextStatus = i + 1 <= currentStep ? 'done' : 'todo';

        const leftPathStatus = i === 0 ? 'end' : currStatus;
        const rightPathStatus = i === steps.length - 1 ? 'end' : nextStatus;
        const displayCheckIcon = showCompletedIcon && nextStatus === 'done';

        return (
          <StepContainer key={`stepper-step-${i}`}>
            <IconContainer
              data-left-path-status={leftPathStatus}
              data-right-path-status={rightPathStatus}
              data-testid={dataTestId !== undefined ? `${dataTestId}-icon` : undefined}
            >
              <StepperIcon data-status={currStatus}>{displayCheckIcon ? <CheckIcon isStroke /> : i + 1}</StepperIcon>
            </IconContainer>
            {title && (
              <TitleContainer>
                <Text
                  weight="bold"
                  color={currStatus === 'done' ? 'primary' : 'secondary'}
                  align="center"
                  data-testid={dataTestId !== undefined ? `${dataTestId}-title` : undefined}
                >
                  {title}
                </Text>
              </TitleContainer>
            )}
          </StepContainer>
        );
      })}
    </StepperContainer>
  );
};

export default HorizontalStepper;

import { Progress } from '@ark-ui/react';
import styled from 'styled-components';
import { getTypographyVar } from '../typography';
import { pathColorMap } from './Stepper.styles';
import Text from '../Text';
import { getSpacingVar } from '../spacing';

export const StyledProgressRoot = styled(Progress.Root)`
  display: flex;
  align-items: center;
  gap: ${getSpacingVar(6)};

  --size: ${getSpacingVar(20)};
  --thickness: ${getSpacingVar(1.5)};
`;

export const StyledProgressValue = styled(Progress.ValueText)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  /* We will temporarily override Text component is updated */
  font-weight: ${getTypographyVar('boldFontWeight')};
  font-size: 1rem;
`;

export const StyledProgressCircleTrack = styled(Progress.CircleTrack)`
  stroke: ${pathColorMap.todo};
`;

export const StyledProgressCircleRange = styled(Progress.CircleRange)`
  stroke: ${pathColorMap.done};
`;

export const SecondaryText = styled(Text)`
  font-size: 0.875rem;
  line-height: 1.1428571;
`;

export const CircleAndValueContainer = styled.div`
  display: inline-block;
  position: relative;
  line-height: 0;
  height: fit-content;
  cursor: default;
`;

export const TitleContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: ${getSpacingVar(2)};
  cursor: default;

  & > p:first-child {
    /* We will temporarily override Text component is updated */
    font-weight: ${getTypographyVar('boldFontWeight')};
  }
`;

Stepper component has three variants: _horizontal_, _vertical_, and _compact_.

Accepts either `Step[]` or `number` to indicate `steps` in the user flow.

Note, **horizontal stepper looks best centered within the page content.**
Recommended to provide your own container for padding, margin, and limiting width.

### Horizontal Stepper:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

const steps = [
  {
    title: '情報入力',
  },
  {
    title: 'ダウンロード',
  },
  {
    title: '送信',
  },
];

<Stepper variant="horizontal" steps={steps} currentStep={1} />;
```

### Horizontal stepper without titles:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

<Stepper variant="horizontal" steps={3} currentStep={1} />;
```

### Horizontal Stepper with check icon:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

const steps = [
  {
    title: '情報入力',
  },
  {
    title: 'ダウンロード',
  },
  {
    title: '送信',
  },
];

<Stepper variant="horizontal" steps={steps} currentStep={2} showCompletedIcon />;
```

### Vertical Stepper:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

const steps = [
  {
    title: '情報入力',
  },
  {
    title: 'ダウンロード',
  },
  {
    title: '送信',
  },
];

<Stepper variant="vertical" steps={steps} currentStep={1} />;
```

### Vertical stepper without titles:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

<Stepper variant="vertical" steps={3} currentStep={1} />;
```

### Vertical Stepper with check icon:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

const steps = [
  {
    title: '情報入力',
  },
  {
    title: 'ダウンロード',
  },
  {
    title: '送信',
  },
];

<Stepper variant="vertical" steps={steps} currentStep={1} showCompletedIcon />;
```

### Compact Stepper:

```js
import Stepper from '@axa-japan/design-system-react/Stepper';

const steps = [
  {
    title: '情報入力',
  },
  {
    title: 'ダウンロード',
  },
  {
    title: '送信',
  },
];

<Stepper variant="compact" steps={steps} currentStep={1} />;
```

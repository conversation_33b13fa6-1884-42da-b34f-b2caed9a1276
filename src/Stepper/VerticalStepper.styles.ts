import styled, { css } from 'styled-components';
import { pathColorMap } from './Stepper.styles';
import { getSpacingVar } from '../spacing';

export const StepperContainer = styled.div`
  display: grid;
`;

export const StepContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${getSpacingVar(4)};
`;

const PathStyles = css`
  content: '';
  height: 100%;
  width: ${getSpacingVar(1)};
  pointer-events: none;
`;

export const IconContainer = styled.div`
  display: grid;
  grid-template-rows: 1fr ${getSpacingVar(10)} 1fr;
  height: 100%;
  justify-items: center;

  &[data-prev-path-status='todo'] {
    &::before {
      ${PathStyles}
      background-color: ${pathColorMap.todo};
    }
  }

  &[data-prev-path-status='done'] {
    &::before {
      ${PathStyles}
      background-color: ${pathColorMap.done};
    }
  }

  &[data-prev-path-status='end'] {
    &::before {
      ${PathStyles}
    }
  }

  &[data-next-path-status='todo'] {
    &::after {
      ${PathStyles}
      background-color: ${pathColorMap.todo};
    }
  }

  &[data-next-path-status='done'] {
    &::after {
      ${PathStyles}
      background-color: ${pathColorMap.done};
    }
  }

  &[data-next-path-status='end'] {
    &::after {
      ${PathStyles}
    }
  }
`;

export const Path = styled.div`
  width: ${getSpacingVar(1)};
  height: ${getSpacingVar(6)};
  margin-left: 1.11rem;
  &[data-next-path-status='todo'] {
    background-color: ${pathColorMap.todo};
  }
  &[data-next-path-status='done'] {
    background-color: ${pathColorMap.done};
  }
`;

export const TitleContainer = styled.div`
  display: flex;
  cursor: default;
  word-break: break-all;
`;

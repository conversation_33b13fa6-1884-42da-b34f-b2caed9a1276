// Contents of this file is recreation of react-aria-component's FileTrigger component. We will remove this file once the issue
// https://github.com/adobe/react-spectrum/pull/7927 is resolved.
import React, { createContext, CSSProperties, ReactNode, useMemo, ForwardedRef, InputHTMLAttributes, RefObject } from 'react';
import { createHideableComponent } from '@react-aria/collections';
import { HoverEvents, mergeProps, useHover, useButton } from 'react-aria';
import { ButtonContext, ContextValue, InputRenderProps, useContextProps, FileTriggerProps } from 'react-aria-components';
import { AriaLabelingProps, DOMProps as SharedDOMProps } from '@react-types/shared';
import { filterDOMProps, useObjectRef } from '@react-aria/utils';
import { PressResponder } from '@react-aria/interactions';
import type { ButtonProps } from 'react-aria-components';

interface StyleRenderProps<T> {
  /** The CSS [className](https://developer.mozilla.org/en-US/docs/Web/API/Element/className) for the element. A function may be provided to compute the class based on component state. */
  className?: string | ((values: T & { defaultClassName: string | undefined }) => string);
  /** The inline [style](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style) for the element. A function may be provided to compute the style based on component state. */
  style?: CSSProperties | ((values: T & { defaultStyle: CSSProperties }) => CSSProperties | undefined);
}

interface RenderProps<T> extends StyleRenderProps<T> {
  /** The children of the component. A function may be provided to alter the children based on component state. */
  children?: ReactNode | ((values: T & { defaultChildren: ReactNode | undefined }) => ReactNode);
}

interface RenderPropsHookOptions<T> extends RenderProps<T>, SharedDOMProps, AriaLabelingProps {
  values: T;
  defaultChildren?: ReactNode;
  defaultClassName?: string;
  defaultStyle?: CSSProperties;
}

interface RenderPropsHookRetVal {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  'data-rac': string;
}

function useRenderProps<T>({
  className,
  style,
  children,
  defaultClassName = undefined,
  defaultChildren = undefined,
  defaultStyle,
  values,
}: RenderPropsHookOptions<T>): RenderPropsHookRetVal {
  return useMemo(() => {
    let computedClassName: string | undefined;
    let computedStyle: React.CSSProperties | undefined;
    let computedChildren: React.ReactNode | undefined;

    if (typeof className === 'function') {
      computedClassName = className({ ...values, defaultClassName });
    } else {
      computedClassName = className;
    }

    if (typeof style === 'function') {
      computedStyle = style({ ...values, defaultStyle: defaultStyle || {} });
    } else {
      computedStyle = style;
    }

    if (typeof children === 'function') {
      computedChildren = children({ ...values, defaultChildren });
    } else if (children == null) {
      computedChildren = defaultChildren;
    } else {
      computedChildren = children;
    }

    return {
      className: computedClassName ?? defaultClassName,
      style: computedStyle || defaultStyle ? { ...defaultStyle, ...computedStyle } : undefined,
      children: computedChildren ?? defaultChildren,
      'data-rac': '',
    };
  }, [className, style, children, defaultClassName, defaultChildren, defaultStyle, values]);
}

interface StyleRenderProps<T> {
  /** The CSS [className](https://developer.mozilla.org/en-US/docs/Web/API/Element/className) for the element. A function may be provided to compute the class based on component state. */
  className?: string | ((values: T & { defaultClassName: string | undefined }) => string);
  /** The inline [style](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style) for the element. A function may be provided to compute the style based on component state. */
  style?: CSSProperties | ((values: T & { defaultStyle: CSSProperties }) => CSSProperties | undefined);
}

interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'style'>, HoverEvents, StyleRenderProps<InputRenderProps> {
  webkitdirectory: string | undefined;
}

const InputContext = createContext<ContextValue<InputProps, HTMLInputElement>>({});

const filterHoverProps = ({ onHoverStart, onHoverChange, onHoverEnd, ...otherProps }: InputProps) => {
  return otherProps;
};

const InputWithoutFocus: React.FC<{ racProps: InputProps; racRef?: RefObject<HTMLInputElement> }> = ({ racProps, racRef }) => {
  const { hoverProps, isHovered } = useHover(racProps);

  const isInvalid = !!racProps['aria-invalid'] && racProps['aria-invalid'] !== 'false';
  const renderProps = useRenderProps({
    ...racProps,
    values: {
      isHovered,
      isFocused: false,
      isFocusVisible: false,
      isDisabled: racProps.disabled || false,
      isInvalid,
    },
  });

  return (
    <input
      {...mergeProps(filterHoverProps(racProps), hoverProps)}
      {...renderProps}
      ref={racRef}
      data-disabled={racProps.disabled || undefined}
      data-hovered={isHovered || undefined}
      data-invalid={isInvalid || undefined}
    />
  );
};

export const RacInput = createHideableComponent((props: InputProps, ref: ForwardedRef<HTMLInputElement>) => {
  const [racProps, racRef] = useContextProps(props, ref, InputContext);

  return <InputWithoutFocus racProps={racProps} racRef={racRef} />;
});

export const RacFileTrigger = React.forwardRef(
  (
    { onSelect, acceptedFileTypes, allowsMultiple, defaultCamera, children, acceptDirectory, ...rest }: FileTriggerProps,
    ref: ForwardedRef<HTMLInputElement>,
  ) => {
    const inputRef = useObjectRef(ref);
    const domProps = filterDOMProps(rest);

    return (
      <>
        <PressResponder
          onPress={() => {
            if (inputRef.current?.value) {
              inputRef.current.value = '';
            }
            inputRef.current?.click();
          }}
        >
          {children}
        </PressResponder>
        <RacInput
          {...domProps}
          type="file"
          ref={inputRef}
          style={{ display: 'none' }}
          accept={acceptedFileTypes?.toString()}
          onChange={(e) => onSelect?.(e.target.files)}
          capture={defaultCamera}
          multiple={allowsMultiple}
          webkitdirectory={acceptDirectory ? '' : undefined}
        />
      </>
    );
  },
);

RacFileTrigger.displayName = 'RacFileTrigger';

export const RacButton = React.forwardRef((props: React.PropsWithChildren<ButtonProps>, ref: React.ForwardedRef<HTMLButtonElement>) => {
  // Merge the local props and ref with the ones provided via context.
  const [racProps, racRef] = useContextProps(props, ref, ButtonContext);
  const { style, className, slot, ...rest } = racProps;
  const { buttonProps } = useButton(racProps, racRef);
  return (
    <button {...buttonProps} {...rest} style={style as CSSProperties} className={className as string} slot={slot as string} ref={ref}>
      {props.children}
    </button>
  );
});

RacButton.displayName = 'RacButton';

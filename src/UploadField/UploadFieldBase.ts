import styled from 'styled-components';
import { getSpacingVar } from '../spacing';
import { getColorVar } from '../colors';

export const UploadFieldBase = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: ${getSpacingVar(2)};

  button:first-child {
    width: fit-content;
    color: ${getColorVar('interactiveActivePrimary')};
  }

  &[data-full-width] {
    button:first-child {
      width: 100%;
    }
  }
`;

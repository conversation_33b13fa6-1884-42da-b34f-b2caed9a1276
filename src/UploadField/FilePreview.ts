import styled from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

export const FilePreviewList = styled.ul`
  display: flex;
  flex-direction: column;
  list-style-type: none;
  gap: ${getSpacingVar(2)};
  margin: 0;
`;

export const FilePreviewItem = styled.li`
  background-color: ${getColorVar('utilityBackgroundWhite')};
  border: 2px solid ${getColorVar('utilityStrokeLight')};
  border-radius: ${getRadiusVar('sm')};
  color: ${getColorVar('characterPrimary')};
  padding-left: ${getSpacingVar(4)};

  &[aria-invalid='true'] {
    border: 2px solid ${getColorVar('statusDanger')};
  }
`;

export const FilePreviewItemWrapper = styled.div`
  display: flex;
  align-items: center;
`;

export const FilePreviewTextWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  min-width: 0;
  width: 100%;
`;

export const FilePreviewItemError = styled.div`
  padding-bottom: ${getSpacingVar(2)};
`;

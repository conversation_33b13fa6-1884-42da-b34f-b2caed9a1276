import React from 'react';
import UploadField from '../UploadField';
import { UploadIcon } from '../../Icons';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('UploadField', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<UploadField label="UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon /> }} />, task);
  });

  test('DefaultWithFullWidth', async ({ task }) => {
    await takeScreenshot(<UploadField label="UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon />, fullWidth: true }} />, task);
  });

  test('WithError', async ({ task }) => {
    const RenderComponent = () => {
      const [files, setFiles] = React.useState([new File([''], 'test file')]);
      return (
        <UploadField
          label="With error"
          files={files}
          setFiles={setFiles}
          button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
          previewFileItems={[{ errorMessage: 'This file is too large' }]}
        />
      );
    };

    await takeScreenshot(<RenderComponent />, task);
  });
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`UploadField > renders without crashing 1`] = `
.c4 {
  -webkit-text-decoration: none;
  text-decoration: none;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:hover,
.c4:focus {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c4:visited {
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:visited:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:visited:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4:visited[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:visited:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4:visited[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4[aria-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c5[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-2);
}

.c3 button:first-child {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  color: var(--ajds-color-interactive-active-primary);
}

.c3[data-full-width] button:first-child {
  width: 100%;
}

@media (hover:hover) {
  .c4:visited:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="upload-field-id-:r0:"
      id="field-label-:r1:"
    >
      UploadField
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
      >
        <button
          aria-label="ファイルを選択"
          class="c4"
          data-react-aria-pressable="true"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row-reverse; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
          tabindex="0"
          type="button"
        >
          <span
            class="c5"
            data-text-variant="false"
            style="--ajds-ButtonText-text-decoration: none;"
          >
            ファイルを選択
          </span>
          <span
            aria-hidden="true"
            class="c6"
          >
            <svg
              class="c7"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
              />
            </svg>
          </span>
        </button>
        <input
          data-rac=""
          multiple=""
          style="display: none;"
          type="file"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`UploadField > renders without crashing in an SSR environment 1`] = `
.c4 {
  -webkit-text-decoration: none;
  text-decoration: none;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:hover,
.c4:focus {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c4:visited {
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:visited:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:visited:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4:visited[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:visited:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4:visited[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4[aria-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c5[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-2);
}

.c3 button:first-child {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  color: var(--ajds-color-interactive-active-primary);
}

.c3[data-full-width] button:first-child {
  width: 100%;
}

@media (hover:hover) {
  .c4:visited:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="upload-field-id-:r2:"
      id="field-label-:r3:"
    >
      UploadField
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
      >
        <button
          aria-label="ファイルを選択"
          class="c4"
          data-react-aria-pressable="true"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row-reverse; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
          tabindex="0"
          type="button"
        >
          <span
            class="c5"
            data-text-variant="false"
            style="--ajds-ButtonText-text-decoration: none;"
          >
            ファイルを選択
          </span>
          <span
            aria-hidden="true"
            class="c6"
          >
            <svg
              class="c7"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
              />
            </svg>
          </span>
        </button>
        <input
          data-rac=""
          multiple=""
          style="display: none;"
          type="file"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`UploadField > renders without crashing with error 1`] = `
.c4 {
  -webkit-text-decoration: none;
  text-decoration: none;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:hover,
.c4:focus {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c4:visited {
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c4:visited:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:visited:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4:visited[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:visited:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4:visited[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4 svg {
  color: currentColor;
}

.c4:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c4:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c4[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c4:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c4[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c4[aria-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c5 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c5[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c6 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c12 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  list-style-type: none;
  gap: var(--ajds-spacing-2);
  margin: 0;
}

.c9 {
  background-color: var(--ajds-color-utility-background-white);
  border: 2px solid var(--ajds-color-utility-stroke-light);
  border-radius: var(--ajds-radius-sm);
  color: var(--ajds-color-character-primary);
  padding-left: var(--ajds-spacing-4);
}

.c9[aria-invalid='true'] {
  border: 2px solid var(--ajds-color-status-danger);
}

.c10 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  min-width: 0;
  width: 100%;
}

.c15 {
  padding-bottom: var(--ajds-spacing-2);
}

.c13 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c13 svg {
  color: currentColor;
}

.c13:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c13:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c14 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c14 svg {
  color: currentColor;
}

.c14[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c7 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  row-gap: var(--ajds-spacing-1);
  margin: var(--ajds-FieldWrapBase-margin-top,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-right,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-bottom,var(--ajds-FieldWrapBase-margin,0)) var(--ajds-FieldWrapBase-margin-left,var(--ajds-FieldWrapBase-margin,0));
}

.c1 {
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  color: var(--ajds-color-character-primary);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  position: relative;
  max-width: 100%;
  -webkit-column-gap: var(--ajds-spacing-2);
  column-gap: var(--ajds-spacing-2);
  -webkit-align-self: self-start;
  -ms-flex-item-align: self-start;
  align-self: self-start;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--ajds-spacing-2);
  width: 100%;
}

.c2 label {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.c2 input,
.c2 select,
.c2 button,
.c2 textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.5;
  margin: 0;
  color: var(--ajds-color-character-primary);
}

.c2 input {
  overflow: visible;
}

.c2 select {
  text-transform: none;
}

.c2 textarea {
  overflow: auto;
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  font-size: var(--ajds-font-size-default);
  line-height: 150%;
  width: 100%;
  -webkit-column-gap: var(--ajds-spacing-1);
  column-gap: var(--ajds-spacing-1);
  color: var(--ajds-color-status-danger);
  white-space: pre-line;
}

.c17 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--ajds-spacing-1) 0;
}

.c3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-2);
}

.c3 button:first-child {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  color: var(--ajds-color-interactive-active-primary);
}

.c3[data-full-width] button:first-child {
  width: 100%;
}

@media (hover:hover) {
  .c4:visited:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:visited:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c4:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c14:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c14:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
  >
    <label
      class="c1"
      for="upload-field-id-:r4:"
      id="field-label-:r5:"
    >
      With error
    </label>
    <div
      class="c2 field-input-wrap"
    >
      <div
        class="c3"
      >
        <button
          aria-label="ファイルを選択"
          class="c4"
          data-react-aria-pressable="true"
          style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row-reverse; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
          tabindex="0"
          type="button"
        >
          <span
            class="c5"
            data-text-variant="false"
            style="--ajds-ButtonText-text-decoration: none;"
          >
            ファイルを選択
          </span>
          <span
            aria-hidden="true"
            class="c6"
          >
            <svg
              class="c7"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11 16V7.85L8.4 10.45L7 9L12 4L17 9L15.6 10.45L13 7.85V16H11ZM6 20C5.45 20 4.97917 19.8042 4.5875 19.4125C4.19583 19.0208 4 18.55 4 18V15H6V18H18V15H20V18C20 18.55 19.8042 19.0208 19.4125 19.4125C19.0208 19.8042 18.55 20 18 20H6Z"
              />
            </svg>
          </span>
        </button>
        <input
          data-rac=""
          multiple=""
          style="display: none;"
          type="file"
        />
        <ul
          class="c8"
        >
          <li
            aria-describedby="test file-0-error-message"
            aria-errormessage="test file-0-error-message"
            aria-invalid="true"
            aria-relevant="additions text"
            class="c9"
          >
            <div
              class="c10"
            >
              <div
                class="c11"
              >
                <span
                  class="c12"
                  style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: hidden; --ajds-Text-white-space: nowrap; --ajds-Text-text-overflow: ellipsis; --ajds-Text-text-align: start;"
                >
                  test file
                </span>
                <span
                  class="c12"
                  style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start; text-wrap: nowrap;"
                >
                  0 B
                </span>
              </div>
              <button
                class="c13"
                style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
                type="button"
              >
                <div
                  class="c14"
                  style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-primary); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent);"
                >
                  <span
                    class="c6"
                  >
                    <svg
                      class="c7"
                      fill="none"
                      focusable="false"
                      style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.83333 21C6.31389 21 5.86921 20.815 5.49931 20.4451C5.1294 20.0752 4.94444 19.6306 4.94444 19.1111V6.83333H4V4.94444H8.72222V4H14.3889V4.94444H19.1111V6.83333H18.1667V19.1111C18.1667 19.6306 17.9817 20.0752 17.6118 20.4451C17.2419 20.815 16.7972 21 16.2778 21H6.83333ZM16.2778 6.83333H6.83333V19.1111H16.2778V6.83333ZM8.72222 17.2222H10.6111V8.72222H8.72222V17.2222ZM12.5 17.2222H14.3889V8.72222H12.5V17.2222Z"
                      />
                    </svg>
                  </span>
                </div>
              </button>
            </div>
            <div
              class="c15"
            >
              <div
                aria-live="polite"
                class="c16"
                id="test file-0-error-message"
                role="alert"
              >
                <div
                  class="c17"
                >
                  <svg
                    class="c7"
                    fill="none"
                    focusable="false"
                    style="--ajds-IconBase-height: var(--ajds-spacing-4); --ajds-IconBase-width: var(--ajds-spacing-4); --ajds-IconBase-transform: rotate(0deg);"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 3.00073C7.032 3.00073 3 7.03273 3 12.0007C3 16.9687 7.032 21.0007 12 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 12 3.00073ZM12.9 16.5007H11.1V14.7007H12.9V16.5007ZM12.9 12.9007H11.1V7.50073H12.9V12.9007Z"
                    />
                  </svg>
                </div>
                This file is too large
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

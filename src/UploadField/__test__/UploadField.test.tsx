import React from 'react';
import { render } from '../../utils/testUtils';
import UploadField from '../UploadField';
import { UploadIcon } from '../../Icons';

describe('UploadField', () => {
  test('renders without crashing', () => {
    const { getByRole, container } = render(<UploadField label="UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon /> }} />);
    const uploadButton = getByRole('button');
    expect(uploadButton).toBeInTheDocument();
    expect(uploadButton.textContent).toBe('ファイルを選択');
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing in an SSR environment', () => {
    vi.mock('react-aria', async () => {
      const lib = await vi.importActual('react-aria');
      return {
        ...lib,
        useIsSSR: () => true,
      };
    });
    const { getByRole, container } = render(<UploadField label="UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon /> }} />);
    const uploadButton = getByRole('button');
    expect(uploadButton).toBeInTheDocument();
    expect(uploadButton.textContent).toBe('ファイルを選択');
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with error', () => {
    const RenderComponent = () => {
      const [files, setFiles] = React.useState([new File([''], 'test file')]);
      return (
        <UploadField
          label="With error"
          files={files}
          setFiles={setFiles}
          button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
          previewFileItems={[{ errorMessage: 'This file is too large' }]}
        />
      );
    };
    const { getByText, container } = render(<RenderComponent />);
    expect(getByText('This file is too large')).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

import React, { forwardRef, useId, useState } from 'react';
import { <PERSON>Trigger, Button as AriaButton } from 'react-aria-components';
import { useIsSSR } from 'react-aria';
import Button from '../Button';
import Text from '../Text';
import { MarginSxPropType } from '../sx';
import { FilePreviewItem, FilePreviewItemError, FilePreviewItemWrapper, FilePreviewList, FilePreviewTextWrapper } from './FilePreview';
import IconButton from '../IconButton';
import { DeleteIcon } from '../Icons';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import checkHasError from '../utils/checkHasError';
import { UploadFieldBase } from './UploadFieldBase';
import { formatFileSize } from './utils';
import FieldError from '../FieldError';
import { Ra<PERSON><PERSON>ileTrigger, RacButton } from './RacFileTrigger';

export type UploadFieldProps = {
  files?: File[];
  /** Function to update the list of files */
  setFiles?: React.Dispatch<React.SetStateAction<File[]>>;
  onFilesChange?: (files: File[]) => void;
  button: {
    text: string;
    icon: React.ReactElement;
    /** Sets the button to be full width */
    fullWidth?: boolean;
  };
  /** Customize the preview file item, you can just use the index of the file you want to edit */
  previewFileItems?: {
    errorMessage?: string;
  }[];
  /** If set to false, it will only allow selection of a single file. Default to true. */
  allowsMultiple?: boolean;
  /** Specifies what <a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Guides/MIME_types/Common_types" target="_blank">mime type</a> of files are allowed. */
  acceptedFileTypes?: string[];
  sx?: MarginSxPropType;
} & CommonFieldBaseProps &
  Omit<React.ComponentPropsWithoutRef<'input'>, 'onSelect'>;

const UploadField = forwardRef<HTMLInputElement, UploadFieldProps>(
  (
    {
      files: controlledFiles,
      setFiles: setControlledFiles,
      button,
      previewFileItems,
      onFilesChange,
      allowsMultiple = true,
      acceptedFileTypes,
      sx,
      showRequiredIndicator,
      ...rest
    },
    ref,
  ) => {
    const isSSR = useIsSSR();
    const { id, label, errorMessage, showError, required } = rest;

    const fallbackId = `upload-field-id-${useId()}`;
    const hasError = checkHasError(showError, errorMessage);

    const [uncontrolledFiles, setUncontrolledFiles] = useState<File[]>([]);

    const isControlled = controlledFiles !== undefined && setControlledFiles !== undefined;
    const files = isControlled ? controlledFiles : uncontrolledFiles;
    const setFiles = isControlled ? setControlledFiles : setUncontrolledFiles;

    const onSelect = (selectedFiles: FileList | null) => {
      if (selectedFiles) {
        const newFiles = [...files, ...selectedFiles];
        setFiles(newFiles);
        if (onFilesChange) onFilesChange(newFiles);
      }
    };

    const removeFile = (index: number) => {
      const newFiles = files.filter((_, idx) => idx !== index);
      setFiles(newFiles);
      if (onFilesChange) onFilesChange(newFiles);
    };

    return (
      <FieldBase
        id={id || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <UploadFieldBase data-full-width={button.fullWidth}>
          {isSSR ? (
            // These components will be temporarily used until the issue in https://github.com/adobe/react-spectrum/pull/7927 is fixed and merged into react-aria-components
            <RacFileTrigger onSelect={onSelect} allowsMultiple={allowsMultiple} acceptedFileTypes={acceptedFileTypes} ref={ref} {...rest}>
              <Button variant="outlined" icon={button.icon} iconPosition="left" asChild>
                <RacButton>{button.text}</RacButton>
              </Button>
            </RacFileTrigger>
          ) : (
            <FileTrigger onSelect={onSelect} allowsMultiple={allowsMultiple} acceptedFileTypes={acceptedFileTypes} ref={ref} {...rest}>
              <Button variant="outlined" icon={button.icon} iconPosition="left" asChild>
                <AriaButton>{button.text}</AriaButton>
              </Button>
            </FileTrigger>
          )}
          {files.length > 0 && (
            <FilePreviewList>
              {files.map((file, index) => (
                <FilePreviewItem
                  key={`${file.name}-${index}`}
                  aria-invalid={!!previewFileItems && !!previewFileItems[index]?.errorMessage}
                  aria-errormessage={previewFileItems ? `${file.name}-${index}-error-message` : undefined}
                  aria-describedby={previewFileItems ? `${file.name}-${index}-error-message` : undefined}
                  aria-relevant="additions text"
                >
                  <FilePreviewItemWrapper>
                    <FilePreviewTextWrapper>
                      <Text truncate as="span">
                        {file.name}
                      </Text>
                      <Text wrap="nowrap" as="span">
                        {formatFileSize(file.size)}
                      </Text>
                    </FilePreviewTextWrapper>
                    <IconButton icon={<DeleteIcon />} onClick={() => removeFile(index)} />
                  </FilePreviewItemWrapper>
                  {previewFileItems && previewFileItems[index]?.errorMessage && (
                    <FilePreviewItemError>
                      <FieldError id={`${file.name}-${index}-error-message`} hasError errorMessage={previewFileItems[index]?.errorMessage} />
                    </FilePreviewItemError>
                  )}
                </FilePreviewItem>
              ))}
            </FilePreviewList>
          )}
        </UploadFieldBase>
      </FieldBase>
    );
  },
);

UploadField.displayName = 'UploadField';

export default UploadField;

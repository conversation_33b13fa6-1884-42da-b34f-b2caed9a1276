In addition to props above, _UploadField_ accepts any default props for _input_ HTML tag.

### Default UploadField (controlled):

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import { UploadIcon } from '@axa-japan/design-system-react/Icons';

const [files, setFiles] = React.useState([]);

<UploadField
  label="Controlled UploadField"
  required
  files={files}
  setFiles={setFiles}
  onFilesChange={(newFiles) => setFiles(newFiles)}
  button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
/>;
```

### Default UploadField (uncontrolled):

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import { UploadIcon } from '@axa-japan/design-system-react/Icons';

<UploadField label="Uncontrolled UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon /> }} />;
```

### Full Width:

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import UploadIcon from '@axa-japan/design-system-react/Icons/UploadIcon';

<UploadField label="Full width UploadField" button={{ text: 'ファイルを選択', icon: <UploadIcon />, fullWidth: true }} />;
```

### With preview file error:

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import { UploadIcon } from '@axa-japan/design-system-react/Icons';

const [files, setFiles] = React.useState([]);

const previewFileItems = files.map((file) => {
  if (file.size > 10) {
    return { errorMessage: 'This file is too large' };
  }
  return {};
});

<UploadField
  label="With custom error message"
  required
  files={files}
  onFilesChange={(newFiles) => setFiles(newFiles)}
  previewFileItems={previewFileItems}
  button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
/>;
```

### With allowsMultiple set to false:

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import { UploadIcon } from '@axa-japan/design-system-react/Icons';

const [files, setFiles] = React.useState([]);

<UploadField
  label="Only allow single file selection"
  files={files}
  setFiles={setFiles}
  onFilesChange={(newFiles) => setFiles(newFiles)}
  button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
  allowsMultiple={false}
/>;
```

### With acceptedFileTypes:

NOTE: Please note that using acceptedFileTypes may effect how devices such as iPhone and Android phones or tablets allow the user to input files, please test to ensure that all devices work as expected.

```js
import React from 'react';
import UploadField from '@axa-japan/design-system-react/UploadField';
import UploadIcon from '@axa-japan/design-system-react/Icons/UploadIcon';

const [files, setFiles] = React.useState([]);

<UploadField
  label="Only accepts pdf"
  files={files}
  setFiles={setFiles}
  onFilesChange={(newFiles) => setFiles(newFiles)}
  button={{ text: 'ファイルを選択', icon: <UploadIcon /> }}
  acceptedFileTypes={['application/pdf']}
/>;
```

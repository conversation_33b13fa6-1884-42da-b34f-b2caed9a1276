import { useState, useCallback } from 'react';

const useAccordionEffect = ({
  isOpenByDefault = false,
  open,
  onToggle,
}: {
  isOpenByDefault: boolean;
  open?: boolean;
  onToggle?: () => void;
}): {
  isOpen: boolean;
  toggleOpen: () => void;
} => {
  const [isOpen, setIsOpen] = useState<boolean>(isOpenByDefault);

  const isControlled = typeof open === 'boolean';

  const toggleOpen = useCallback(() => {
    // If controlled state
    if (isControlled && typeof onToggle === 'function') {
      onToggle();
    } else {
      setIsOpen((isCurrentlyOpen) => !isCurrentlyOpen);
    }
  }, [isControlled, onToggle]);

  const shouldOpenAccordion = isControlled ? open : isOpen;

  return {
    isOpen: shouldOpenAccordion,
    toggleOpen,
  };
};

export default useAccordionEffect;

import React from 'react';
import { fireEvent } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import useAccordionEffect from '../useAccordionEffect';

const TestComponent: React.FC<{ isOpenByDefault: boolean }> = ({ isOpenByDefault }) => {
  const { isOpen, toggleOpen } = useAccordionEffect({ isOpenByDefault });

  return (
    <>
      <button type="button" data-testid="handle-click" onClick={toggleOpen}>
        {isOpen ? 'open' : 'close'}
      </button>

      <button type="button" data-testid="set-is-closed" onClick={toggleOpen}>
        {isOpen ? 'open' : 'close'}
      </button>

      <div data-testid="accordion">
        <p data-testid="accordion-content">accordion content</p>
      </div>
    </>
  );
};

describe('useAccordionEffect', () => {
  test('can set open/closed state with setIsClosed', () => {
    const { getByTestId } = render(<TestComponent isOpenByDefault={true} />);

    expect(getByTestId('set-is-closed')).toHaveTextContent('open');
    fireEvent.click(getByTestId('set-is-closed'));
    expect(getByTestId('set-is-closed')).toHaveTextContent('close');
  });

  test('can set open/closed state with toggleOpen', () => {
    const { getByTestId } = render(<TestComponent isOpenByDefault={false} />);

    expect(getByTestId('handle-click')).toHaveTextContent('close');
    fireEvent.click(getByTestId('handle-click'));
    expect(getByTestId('handle-click')).toHaveTextContent('open');
  });
});

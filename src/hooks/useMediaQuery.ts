import { useEffect, useState } from 'react';

const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState<boolean>(false);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const handleMediaChange = (event: MediaQueryListEvent) => setMatches(event.matches);

    setMatches(mediaQueryList.matches);

    mediaQueryList.addEventListener('change', handleMediaChange);
    return () => {
      mediaQueryList.removeEventListener('change', handleMediaChange);
    };
  }, [query]);

  return matches;
};

export default useMediaQuery;

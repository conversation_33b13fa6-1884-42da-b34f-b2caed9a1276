import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const pagination = {
  trigger: css`
    all: unset;
    height: ${getSpacingVar(10)};
    width: ${getSpacingVar(10)};
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: ${getSpacingVar(1)};
    cursor: pointer;
    border-radius: ${getRadiusVar('sm')};

    &[data-disabled] {
      cursor: default;
      color: ${getColorVar('interactiveDisabledDark')};
    }

    :hover {
      background-color: ${getColorVar('interactiveHoverGreyTransparent')};
    }

    :focus-visible:not(:disabled) {
      box-shadow: inset 0 0 0 2px ${getColorVar('interactiveFocusPrimary')};
    }
  `,
};

export default pagination;

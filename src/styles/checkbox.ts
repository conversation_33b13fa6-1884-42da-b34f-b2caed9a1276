import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getRadiusVar } from '../radius';

const checkbox = {
  control: css`
    display: flex;
    width: 20px;
    height: 20px;
    align-items: flex-start;
    border-radius: ${getRadiusVar('xs')};
    transition: 0.2s all ease;
    color: ${getColorVar('utilityStrokeWhite')};

    &[data-state='unchecked'] {
      border: 2px solid ${getColorVar('utilityStrokeDark')};
      background-color: ${getColorVar('interactiveActiveWhite')};
    }

    &[data-state='checked'] {
      border: 2px solid ${getColorVar('interactiveActivePrimary')};
      background-color: ${getColorVar('interactiveActivePrimary')};
    }

    &[data-invalid] {
      border: 2px solid ${getColorVar('statusDanger')};

      &[data-state='unchecked'] {
        background-color: ${getColorVar('interactiveActiveWhite')};
      }

      &[data-state='checked'] {
        background-color: ${getColorVar('statusDanger')};
      }
    }

    &[data-disabled] {
      color: ${getColorVar('interactiveDisabledLight')};
      border: 2px solid ${getColorVar('interactiveDisabledDark')};

      &[data-state='unchecked'] {
        background-color: ${getColorVar('interactiveDisabledLight')};
      }

      &[data-state='checked'] {
        background-color: ${getColorVar('interactiveDisabledDark')};
      }
    }
  `,
};

export default checkbox;

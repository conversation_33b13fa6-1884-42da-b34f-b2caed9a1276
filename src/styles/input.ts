import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const input = {
  errorState: `border-color: ${getColorVar('statusDanger')};`,
  labelText: css`
    font-size: 16px;
    line-height: 24px;
    text-align: left;
    color: ${getColorVar('characterPrimary')};
  `,
  base: css`
    background-color: ${getColorVar('interactiveActiveWhite')};
    border-radius: ${getRadiusVar('sm')};
    border: none;
    outline: none;
    width: 100%;
    max-width: 100%;

    &::placeholder {
      color: ${getColorVar('interactivePlaceholder')};
    }

    &:hover:not(:disabled) {
      background-color: ${getColorVar('interactiveHoverGrey')};
    }

    /* Prevent hover from triggering on the field component when hovering on field label */
    .field-input-wrap:not(:hover) &:hover:not(:disabled) {
      background-color: ${getColorVar('utilityBackgroundWhite')};
    }

    &:disabled {
      background-color: ${getColorVar('interactiveDisabledLight')};
      color: ${getColorVar('interactiveDisabledDark')};
      cursor: not-allowed;
    }
  `,
  baseBorder: css`
    border-radius: ${getRadiusVar('sm')};
    border: 1px solid ${getColorVar('utilityStrokeDark')};

    &[aria-invalid='true'] {
      border: 2px solid ${getColorVar('statusDanger')};
    }
  `,
  disabledBorder: css`
    border: 1px solid ${getColorVar('interactiveDisabledDark')};
  `,
  focusBoxShadow: css`
    box-shadow:
      0 0 0 2px ${getColorVar('interactiveFocusWhite')},
      0 0 0 4px ${getColorVar('interactiveFocusPrimary')};
  `,
  itemLabel: () => css`
    ${input.labelText}

    display: flex;
    overflow-wrap: anywhere;

    &[data-invalid] {
      color: ${getColorVar('statusDanger')};
    }

    &[data-disabled] {
      color: ${getColorVar('interactiveDisabledDark')};
    }
  `,
  item: css`
    display: flex;
    align-items: center;
    box-sizing: border-box;
    border-radius: ${getRadiusVar('sm')};
    padding: ${getSpacingVar(3)} ${getSpacingVar(3.5)};
    column-gap: ${getSpacingVar(2)};
    min-height: ${getSpacingVar(12)};
    max-height: ${getSpacingVar(12)};

    &:hover {
      background-color: ${getColorVar('interactiveHoverGreyTransparent')};
    }

    &[data-disabled] {
      cursor: not-allowed;

      &:hover {
        background-color: initial;
      }
    }
  `,
  root: css`
    display: flex;
    flex-wrap: wrap;
    row-gap: ${getSpacingVar(2)};

    &[data-orientation='vertical'] {
      flex-direction: column;
    }

    &[data-orientation='horizontal'] {
      flex-direction: row;
      column-gap: ${getSpacingVar(2)};
    }
  `,
};

export default input;

import { css } from 'styled-components';

const table = {
  container: css`
    overflow: auto;
    width: fit-content;
    max-width: 100%;
  `,
  cell: css`
    text-align: left;
    vertical-align: middle;

    & > * {
      width: fit-content;
    }

    &[data-horizontal-align='center'] {
      text-align: center;
    }

    &[data-horizontal-align='right'] {
      text-align: right;
    }
  `,
};

export default table;

import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import input from './input';
import { getRadiusVar } from '../radius';

const tile = {
  content: css`
    display: flex;
    align-items: center;
    padding: ${getSpacingVar(3.5)} ${getSpacingVar(4)};
    height: 100%;
    column-gap: ${getSpacingVar(3.5)};
  `,
  item: css`
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: ${getColorVar('utilityBackgroundWhite')};
    border: 2px solid ${getColorVar('utilityStrokeLight')};
    border-radius: ${getRadiusVar('sm')};
    overflow: hidden;
    min-height: ${getSpacingVar(21)};
    transition: 0.2s border ease;

    /* Ark UI sets RadioGroup Item component's width to fit-content by default, it is overridden so that each tile takes the full width */
    width: 100% !important;

    &[data-state='checked'] {
      border: 2px solid ${getColorVar('interactiveActivePrimary')};
    }

    &:hover {
      background-color: ${getColorVar('interactiveHoverGreyTransparent')};

      &[data-state='checked'] {
        border: 2px solid ${getColorVar('interactiveHoverPrimary')};
      }
    }

    &:has(:focus-visible) {
      box-shadow:
        0 0 0 2px ${getColorVar('interactiveFocusWhite')},
        0 0 0 4px ${getColorVar('interactiveFocusPrimary')};
    }

    &[data-invalid] {
      &:has(:focus-visible) {
        box-shadow:
          0 0 0 2px ${getColorVar('interactiveFocusWhite')},
          0 0 0 4px ${getColorVar('interactiveFocusPrimary')};
      }

      &[data-state='checked'] {
        border: 2px solid ${getColorVar('statusImportant')};
      }
    }

    &[data-disabled] {
      cursor: not-allowed;
      border: 2px solid ${getColorVar('interactiveDisabledLight')};

      &:hover {
        background-color: initial;
      }

      &:has(:focus-visible) {
        box-shadow: initial;
      }

      &[data-state='checked'] {
        border: 2px solid ${getColorVar('interactiveDisabledDark')};
      }
    }
  `,
  label: css`
    ${input.labelText}

    overflow-wrap: anywhere;
    white-space: break-spaces;

    &[data-invalid] {
      color: ${getColorVar('statusDanger')};
    }

    &[data-disabled] {
      color: ${getColorVar('interactiveDisabledDark')};
    }
  `,
  root: css`
    display: grid;
    grid-gap: ${getSpacingVar(4)};
    width: 100%;
  `,
  textContent: css`
    display: flex;
    flex-direction: column;
    row-gap: ${getSpacingVar(1)};
  `,
};

export default tile;

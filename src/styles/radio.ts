import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const radio = {
  control: css`
    display: flex;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: ${getRadiusVar('full')};
    padding: ${getSpacingVar(0.5)};
    transition: 0.2s all ease;
    background-color: ${getColorVar('utilityBackgroundWhite')};

    &[data-state='unchecked'] {
      border: 2px solid ${getColorVar('utilityStrokeDark')};
      color: ${getColorVar('interactiveActiveWhite')};
    }

    &[data-state='checked'] {
      border: 2px solid ${getColorVar('interactiveActivePrimary')};
      color: ${getColorVar('interactiveActivePrimary')};
    }

    &[data-invalid] {
      border: 2px solid ${getColorVar('statusDanger')};

      &[data-state='unchecked'] {
        color: ${getColorVar('interactiveActiveWhite')};
      }

      &[data-state='checked'] {
        color: ${getColorVar('statusDanger')};
      }
    }

    &[data-disabled] {
      background-color: ${getColorVar('interactiveDisabledLight')};
      border: 2px solid ${getColorVar('interactiveDisabledDark')};

      &[data-state='unchecked'] {
        color: ${getColorVar('interactiveDisabledLight')};
      }

      &[data-state='checked'] {
        color: ${getColorVar('interactiveDisabledDark')};
      }
    }
  `,
};

export default radio;

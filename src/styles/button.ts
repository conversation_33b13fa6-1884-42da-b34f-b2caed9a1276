import { css } from 'styled-components';
import { getColorVar } from '../colors';
import { getSpacingVar } from '../spacing';
import { getRadiusVar } from '../radius';

const button = {
  base: css`
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    outline: none;
    outline-offset: 2px;
    cursor: pointer;
    border-radius: ${getRadiusVar('sm')};
    box-sizing: border-box;
    height: ${getSpacingVar(12)};

    /* Necessary to make iconPosition left buttons vertically aligned with other buttons */
    vertical-align: middle;

    svg {
      color: currentColor;
    }
  `,
  disabled: css`
    color: ${getColorVar('interactiveDisabledDark')};
    box-shadow: none;
    cursor: not-allowed;
  `,
};

export default button;

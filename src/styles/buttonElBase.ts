import { css } from 'styled-components';

export default css`
  /**
    * 1. Change the font styles in all browsers.
    * 2. Remove the margin in Firefox and Safari.
    * 3. Show the overflow in Edge.
    * 4. Remove the inheritance of text transform in Firefox.
    */
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
  outline: none;
  overflow: visible; /* 3 */
  text-transform: none; /* 4 */

  /**
    * Correct the inability to style clickable types in iOS and Safari.
    */
  &,
  [type='button'],
  [type='reset'],
  [type='submit'] {
    -webkit-appearance: button;
  }

  /**
    * Remove the inner border and padding in Firefox.
    */
  &::-moz-focus-inner,
  [type='button']::-moz-focus-inner,
  [type='reset']::-moz-focus-inner,
  [type='submit']::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }

  &:active,
  &:focus {
    outline: none;
  }

  /**
    * Restore the focus styles unset by the previous rule.
    */
  &:-moz-focusring,
  [type='button']:-moz-focusring,
  [type='reset']:-moz-focusring,
  [type='submit']:-moz-focusring {
    outline: none;
  }
`;

import React from 'react';
import FieldWrap from '../FieldWrap/FieldWrap';
import FieldLabel from '../FieldLabel/FieldLabel';
import FieldInputWrap from '../FieldInputWrap/FieldInputWrap';
import FieldError from '../FieldError';
import type { MarginSxPropType } from '../sx';
import { FieldsetBase, useSx } from './FieldsetBase';

export type UseFieldsetWrapperProp = {
  /** Flag to wrap the field in a fieldset */
  useFieldsetWrapper?: boolean;
};

export type CommonFieldBaseProps = {
  /** Label text */
  label?: string;
  /** Flag for showing error state */
  showError?: boolean;
  /** Message to show when field has an error */
  errorMessage?: string;
  /** Adds required asterisk to label */
  required?: boolean;
  /** Toggles the label's required indicator */
  showRequiredIndicator?: boolean;
  /** Style overrides */
  sx?: MarginSxPropType;
};

type FieldBaseProps = CommonFieldBaseProps &
  UseFieldsetWrapperProp &
  Omit<React.ComponentPropsWithoutRef<'div'> | React.ComponentPropsWithoutRef<'fieldset'>, 'disabled' | `on${string}`>;

const FieldBase: React.FC<FieldBaseProps> = ({
  id,
  label,
  children,
  showError = false,
  useFieldsetWrapper = false,
  showRequiredIndicator,
  errorMessage,
  required,
  sx,
  style,
  ...rest
}) => {
  const fieldSetSx = useSx(sx);
  if (useFieldsetWrapper) {
    return (
      <FieldsetBase style={{ ...fieldSetSx, ...style }} {...rest}>
        {label && <FieldLabel useLegend label={label} htmlFor={id} required={required} showRequiredIndicator={showRequiredIndicator} />}
        <FieldInputWrap>{children}</FieldInputWrap>
        <FieldError errorMessage={errorMessage} hasError={showError} />
      </FieldsetBase>
    );
  }
  return (
    <FieldWrap sx={sx} {...rest}>
      {label && <FieldLabel label={label} htmlFor={id} required={required} showRequiredIndicator={showRequiredIndicator} />}
      <FieldInputWrap>{children}</FieldInputWrap>
      <FieldError errorMessage={errorMessage} hasError={showError} />
    </FieldWrap>
  );
};

export default FieldBase;

import React from 'react';
import { render } from '../../utils/testUtils';
import FieldBase from '../FieldBase';

describe('FieldBase', () => {
  test('renders without crashing', () => {
    const { getByText, container } = render(<FieldBase label="FieldBase label">Field Base</FieldBase>);
    const testElement = getByText('Field Base');
    expect(testElement).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

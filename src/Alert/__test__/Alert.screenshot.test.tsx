import React from 'react';
import Alert from '../Alert';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('Alert', () => {
  test('Default', async ({ task }) => {
    await takeScreenshot(<Alert text="Alert" onClose={() => {}} />, task);
  });

  test('Info', async ({ task }) => {
    await takeScreenshot(<Alert variant="info" text="Alert" onClose={() => {}} />, task);
  });

  test('Positive', async ({ task }) => {
    await takeScreenshot(<Alert variant="positive" text="Alert" onClose={() => {}} />, task);
  });

  test('Negative', async ({ task }) => {
    await takeScreenshot(<Alert variant="negative" text="Alert" onClose={() => {}} />, task);
  });

  test('Warning', async ({ task }) => {
    await takeScreenshot(<Alert variant="warning" text="Alert" onClose={() => {}} />, task);
  });

  test('WithButton', async ({ task }) => {
    await takeScreenshot(
      <Alert
        variant="negative"
        buttons={[
          { buttonText: 'ボタン1', onClick: () => {} },
          { buttonText: 'ボタン2', onClick: () => {} },
        ]}
        text="Alert"
        onClose={() => {}}
      />,
      task,
    );
  });

  test('LongText', async ({ task }) => {
    await takeScreenshot(
      <Alert
        variant="negative"
        text="This is a very long alert message but should not be too long. Use period when there are more than one sentence. This is a very long toast message but should not be too long."
        onClose={() => {}}
      />,
      task,
    );
  });

  test('LongTextWithButton', async ({ task }) => {
    await takeScreenshot(
      <Alert
        variant="negative"
        buttons={[
          { buttonText: 'ボタン1', onClick: () => {} },
          { buttonText: 'ボタン2', onClick: () => {} },
        ]}
        text="This is a very long alert message but should not be too long. Use period when there are more than one sentence. This is a very long toast message but should not be too long."
        onClose={() => {}}
      />,
      task,
    );
  });

  test('WithoutCloseButton', async ({ task }) => {
    await takeScreenshot(
      <Alert
        variant="negative"
        text="This is a very long alert message but should not be too long. Use period when there are more than one sentence. This is a very long toast message but should not be too long."
        hasCloseButton={false}
      />,
      task,
    );
  });

  test('WithMaxWidth', async ({ task }) => {
    await takeScreenshot(
      <Alert
        variant="negative"
        buttons={[
          { buttonText: 'ボタン1', onClick: () => {} },
          { buttonText: 'ボタン2', onClick: () => {} },
        ]}
        contentMaxWidth={200}
        text="This is a very long alert message but should not be too long. Use period when there are more than one sentence. This is a very long toast message but should not be too long."
        onClose={() => {}}
      />,
      task,
    );
  });
});

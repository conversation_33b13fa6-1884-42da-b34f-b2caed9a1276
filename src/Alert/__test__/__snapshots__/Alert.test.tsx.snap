// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Alert > renders with button without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c11 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c9 svg {
  color: currentColor;
}

.c9:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c9:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c10 svg {
  color: currentColor;
}

.c10[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c10:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c10:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert
        </p>
        <div
          class="c6"
        >
          <button
            aria-label="ボタン1"
            class="c7"
            style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-grey-200); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
            type="button"
          >
            <span
              class="c8"
              data-text-variant="true"
              style="--ajds-ButtonText-text-decoration: underline;"
            >
              ボタン1
            </span>
          </button>
          <button
            aria-label="ボタン2"
            class="c7"
            style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-grey-200); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
            type="button"
          >
            <span
              class="c8"
              data-text-variant="true"
              style="--ajds-ButtonText-text-decoration: underline;"
            >
              ボタン2
            </span>
          </button>
        </div>
      </div>
      <button
        aria-label="閉じる"
        class="c9"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c10"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c11"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders with max width without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c7 svg {
  color: currentColor;
}

.c7:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c7:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c7[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c7:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c7[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c7:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c8 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c8[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c11 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: 500px;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c9 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c9 svg {
  color: currentColor;
}

.c9:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c9:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c10 svg {
  color: currentColor;
}

.c10[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c10:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c10:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert
        </p>
        <div
          class="c6"
        >
          <button
            aria-label="ボタン"
            class="c7"
            style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-grey-200); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
            type="button"
          >
            <span
              class="c8"
              data-text-variant="true"
              style="--ajds-ButtonText-text-decoration: underline;"
            >
              ボタン
            </span>
          </button>
        </div>
      </div>
      <button
        aria-label="閉じる"
        class="c9"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c10"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c11"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders with variant info without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert text
        </p>
      </div>
      <button
        aria-label="閉じる"
        class="c6"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c7"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c8"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders with variant negative without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-danger); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM12.8996 7.50073H11.0996V12.9007H12.8996V7.50073ZM12.8996 14.7007H11.0996V16.5007H12.8996V14.7007ZM4.7998 12.0007C4.7998 15.9787 8.0218 19.2007 11.9998 19.2007C15.9778 19.2007 19.1998 15.9787 19.1998 12.0007C19.1998 8.02273 15.9778 4.80073 11.9998 4.80073C8.0218 4.80073 4.7998 8.02273 4.7998 12.0007Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert text
        </p>
      </div>
      <button
        aria-label="閉じる"
        class="c6"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c7"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c8"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders with variant positive without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-success); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.12766 15.8843L4.83221 11.8097L3.40039 13.1679L9.12766 18.6007L21.4004 6.95892L19.9686 5.60071L9.12766 15.8843Z"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert text
        </p>
      </div>
      <button
        aria-label="閉じる"
        class="c6"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c7"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c8"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders with variant warning without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-warning); --ajds-AlertBase-color: var(--ajds-color-character-primary);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM12 7.3694L18.5205 18.3168H5.47955L12 7.3694ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert text
        </p>
      </div>
      <button
        aria-label="閉じる"
        class="c6"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary);"
        type="button"
      >
        <div
          class="c7"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-active-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-grey-transparent);"
        >
          <span
            class="c8"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

exports[`Alert > renders without close button without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert
        </p>
      </div>
    </div>
  </div>
</div>
`;

exports[`Alert > renders without crashing 1`] = `
.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c5 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c8 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c2 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c6 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c6 svg {
  color: currentColor;
}

.c6:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c6:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c7 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c7 svg {
  color: currentColor;
}

.c7[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

@media (hover:hover) {
  .c7:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c7:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

<div>
  <div
    class="c0"
    role="alert"
    style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <p
          class="c5"
        >
          alert text
        </p>
      </div>
      <button
        aria-label="閉じる"
        class="c6"
        style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
        type="button"
      >
        <div
          class="c7"
          style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
        >
          <span
            class="c8"
          >
            <svg
              class="c3"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
              />
            </svg>
          </span>
        </div>
      </button>
    </div>
  </div>
</div>
`;

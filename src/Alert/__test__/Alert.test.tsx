import React from 'react';
import { render } from '../../utils/testUtils';
import Alert from '../Alert';

const colors = ['info', 'positive', 'negative', 'warning'] as const;
describe('Alert', () => {
  test('renders without crashing', () => {
    const { getByRole, getByText, container } = render(<Alert text="alert text" onClose={() => {}} />);
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(getByText(/alert text/i)).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  colors.forEach((variant) => {
    test(`renders with variant ${variant} without crashing`, () => {
      const { getByRole, getByText, container } = render(<Alert variant={variant} text="alert text" onClose={() => {}} />);
      const testElement = getByRole('alert');
      expect(testElement).toBeInTheDocument();
      expect(getByText(/alert text/i)).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  test(`renders with button without crashing`, () => {
    const { getByRole, getByText, container } = render(
      <Alert
        variant="info"
        text="alert"
        buttons={[
          { buttonText: 'ボタン1', onClick: () => {} },
          { buttonText: 'ボタン2', onClick: () => {} },
        ]}
        onClose={() => {}}
      />,
    );
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(getByText(/ボタン1/i)).toBeInTheDocument();
    expect(getByText(/ボタン2/i)).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test(`renders without close button without crashing`, () => {
    const { getByRole, queryByLabelText, container } = render(<Alert variant="info" text="alert" hasCloseButton={false} />);
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(queryByLabelText(/閉じる/i)).not.toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  test(`renders with max width without crashing`, () => {
    const { getByRole, getByText, container } = render(
      <Alert variant="info" text="alert" buttons={[{ buttonText: 'ボタン', onClick: () => {} }]} contentMaxWidth={500} onClose={() => {}} />,
    );
    const testElement = getByRole('alert');
    expect(testElement).toBeInTheDocument();
    expect(getByText(/ボタン/i)).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});

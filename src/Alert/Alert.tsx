import React from 'react';
import AlertBase, { useSx } from './AlertBase';
import InformationIcon from '../Icons/InformationIcon';
import ErrorIcon from '../Icons/ErrorIcon';
import CheckIcon from '../Icons/CheckIcon';
import WarningIcon from '../Icons/WarningIcon';
import { AlertText } from './AlertText';
import Button from '../Button/Button';
import { AlertContentWrapper } from './AlertContentWrapper';
import { AlertWrapper } from './AlertWrapper';
import { AlertIconWrapper } from './AlertIconWrapper';
import { AlertButtonWrapper } from './AlertButtonWrapper';
import CloseButton from '../CloseButton';

type AlertVariantType = 'info' | 'negative' | 'positive' | 'warning';

// eslint-disable-next-line consistent-return
const iconRendering = (variant: AlertVariantType) => {
  switch (variant) {
    case 'negative':
      return <ErrorIcon size="medium" isOutline />;
    case 'positive':
      return <CheckIcon size="medium" isStroke />;
    case 'warning':
      return <WarningIcon size="medium" isOutline />;
    case 'info':
    default:
      return <InformationIcon size="medium" isOutline />;
  }
};

const getCloseButtonColor = (variant: AlertVariantType): 'white' | 'grey' => {
  switch (variant) {
    case 'warning':
      return 'grey';
    default:
      return 'white';
  }
};
type AlertButtonProps = {
  /** Button text to display */
  buttonText: string;
  /** Adds disabled styles on button */
  disabled?: boolean;
} & Omit<React.ComponentPropsWithoutRef<'button'>, 'color'>;

export type AlertProps = {
  /** Variant to define your alert type */
  variant?: AlertVariantType;
  /** Text to display */
  text: string;
  /** Define the max width for your content in px */
  contentMaxWidth?: number;
  /** To close the alert */
  onClose?(): void;
  /** Display the Close Button */
  hasCloseButton?: boolean;
  /** List of 0-2 alert button props */
  buttons?: [AlertButtonProps] | [AlertButtonProps, AlertButtonProps];
} & React.ComponentPropsWithoutRef<'div'>;

const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'info',
  contentMaxWidth,
  onClose,
  hasCloseButton = true,
  buttons = [],
  text,
  ...rest
}) => {
  const showButton = Boolean(buttons.length);
  return (
    <AlertBase {...rest} style={useSx({}, { variant })} role="alert">
      <AlertWrapper $maxWidth={contentMaxWidth}>
        <AlertIconWrapper>{iconRendering(variant)}</AlertIconWrapper>
        <AlertContentWrapper>
          <AlertText>{text}</AlertText>
          {showButton && (
            <AlertButtonWrapper>
              {buttons[0] && (
                <Button variant="text" color={variant === 'warning' ? 'blue' : 'white'} {...buttons[0]}>
                  {buttons[0].buttonText}
                </Button>
              )}
              {buttons[1] && (
                <Button variant="text" color={variant === 'warning' ? 'blue' : 'white'} {...buttons[1]}>
                  {buttons[1].buttonText}
                </Button>
              )}
            </AlertButtonWrapper>
          )}
        </AlertContentWrapper>
        {hasCloseButton && <CloseButton color={getCloseButtonColor(variant)} onClick={onClose} />}
      </AlertWrapper>
    </AlertBase>
  );
};

export default Alert;

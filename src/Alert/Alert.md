### Alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert text="これは非常に長い警告メッセージですが、あまり長くならないようにします。" onClose={() => {}} />;
```

### Long Alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert
  text="これは非常に長い警告メッセージですが、あまり長くならないようにします。複数の文章がある場合は、ピリオドを使用してください。これは非常に長いトーストメッセージですが、あまり長くなりすぎないようにしましょう。"
  onClose={() => {}}
/>;
```

### Alert with button example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert
  variant="info"
  text="アラートメッセージバナー"
  onClose={() => {}}
  buttons={[
    {
      buttonText: 'ボタン1',
      onClick: () => {
        alert('button 1');
      },
    },
    {
      buttonText: 'ボタン2',
      onClick: () => {
        alert('button 2');
      },
    },
  ]}
/>;
```

### Long Alert with button example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert
  text="これは非常に長い警告メッセージですが、あまり長くならないようにします。複数の文章がある場合は、ピリオドを使用してください。これは非常に長いトーストメッセージですが、あまり長くなりすぎないようにしましょう。"
  buttons={[
    {
      buttonText: 'ボタン',
      onClick: () => {
        alert('button 1');
      },
    },
    {
      buttonText: 'ボタン2',
      onClick: () => {
        alert('button 2');
      },
    },
  ]}
  onClose={() => {}}
/>;
```

### Alert without close button example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert text="これは非常に長い警告メッセージですが、あまり長くならないようにします。" hasCloseButton={false} />;
```

### Info alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert variant="info" text="アラートメッセージバナー" />;
```

### Positive alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert variant="positive" text="アラートメッセージバナー" />;
```

### Negative alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert variant="negative" text="アラートメッセージバナー" />;
```

### Warning alert example:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert variant="warning" text="アラートメッセージバナー" />;
```

### Alert with max width:

```js
import Alert from '@axa-japan/design-system-react/Alert';

<Alert
  text="これは非常に長い警告メッセージですが、あまり長くならないようにします。複数の文章がある場合は、ピリオドを使用してください。これは非常に長いトーストメッセージですが、あまり長くなりすぎないようにしましょう。"
  contentMaxWidth="500"
  buttons={[
    {
      buttonText: 'Button',
      onClick: () => {
        alert('button 1');
      },
    },
  ]}
  onClose={() => {}}
/>;
```

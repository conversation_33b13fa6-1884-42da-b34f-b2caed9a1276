import styled from 'styled-components';
import { getColorVar } from '../colors';
import sx from '../sx';
import type { VariantsType } from '../sx';
import { getSpacingVar } from '../spacing';

const variants: VariantsType<'variant'> = {
  variant: {
    info: {
      'background-color': getColorVar('statusInformation'),
      color: getColorVar('characterPrimaryWhite'),
    },
    positive: {
      'background-color': getColorVar('statusSuccess'),
      color: getColorVar('characterPrimaryWhite'),
    },
    negative: {
      'background-color': getColorVar('statusDanger'),
      color: getColorVar('characterPrimaryWhite'),
    },
    warning: {
      'background-color': getColorVar('statusWarning'),
      color: getColorVar('characterPrimary'),
    },
  },
};

const { useSx, getSxStyleRules } = sx('AlertBase', [], variants);

export { useSx };

const AlertBase = styled.div`
  display: flex;
  justify-content: center;
  padding: ${getSpacingVar(1)};
  ${getSxStyleRules()}
`;

export default AlertBase;

The component accepts an array of _options_, where you can specify _label_, _value_, _data-testid_ for each option.

### Example CheckboxTileGroupField - Default with required

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="Default with required"
  required={true}
  defaultValue={['2']}
  options={[
    { title: 'checkbox', value: '1' },
    { title: 'checkbox', value: '2' },
  ]}
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
/>;
```

### Example CheckboxTileGroupField - With description

An optional description can be added per option. Line-breaks can be inserted at any point of the text.

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="With description"
  defaultValue={['2']}
  options={[
    { title: 'checkbox', description: 'This is a description.', value: '1' },
    { title: 'checkbox', description: 'This is a description.\nThis is a description.', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example CheckboxTileGroupField - itemsPerRow

The _itemsPerRow_ prop can be used to set the maximum number of items per row, each item will take a fraction of the space available and adjust its width accordingly. Default is _1_ item per row.

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="Horizontal"
  itemsPerRow={4}
  defaultValue={['6']}
  options={[
    { title: 'checkbox', value: '1' },
    { title: 'checkbox', value: '2' },
    { title: 'checkbox', value: '3' },
    { title: 'checkbox', value: '4' },
    { title: 'checkbox', value: '5' },
    { title: 'checkbox', value: '6' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example CheckboxTileGroupField - Disabled

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="Disabled"
  disabled={true}
  defaultValue={['2']}
  options={[
    { title: 'checkbox', value: '1' },
    { title: 'checkbox', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example CheckboxTileGroupField - Partially Disabled

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="Partially Disabled"
  defaultValue={['2']}
  options={[
    { title: 'checkbox', value: '1', disabled: true },
    { title: 'checkbox', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example CheckboxTileGroupField - Error

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<CheckboxTileGroupField
  label="Error"
  errorMessage="error error error error"
  defaultValue={['2']}
  options={[
    { title: 'checkbox', value: '1' },
    { title: 'checkbox', value: '2' },
  ]}
  onChange={() => console.log('change')}
/>;
```

### Example CheckboxTileGroupField - Form Interaction

If you want to get the value of the option onChange through a &lt;Form&gt;, you can do so by wrapping &lt;CheckboxTileGroupField&gt; in a &lt;Form&gt;:

```js
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

<form
  id="form1"
  onChange={(e) => {
    const { name, id, value, checked } = e.target;
    console.log(`name: ${name}, id: ${id}, value: ${value} checked: ${checked}`);
  }}
>
  <CheckboxTileGroupField
    form="form1"
    label="Form integration"
    displayInline={true}
    defaultValue={['2', '3']}
    options={[
      { title: 'checkbox', value: '1' },
      { title: 'checkbox', value: '2' },
      { title: 'checkbox', value: '3' },
    ]}
    onChange={() => console.log('change')}
  />
</form>;
```

### Exposing Ref

A _ref_ prop can be passed which can be used to access the &lt;div&gt; container of the &lt;CheckboxTileGroupField&gt;.

```js
import React, { useRef } from 'react';
import CheckboxTileGroupField from '@axa-japan/design-system-react/CheckboxTileGroupField';

const Render = () => {
  const ref = useRef(null);
  return (
    <CheckboxTileGroupField
      ref={ref}
      label="Ref"
      defaultValue={['2']}
      options={[
        { title: 'checkbox', value: '1' },
        { title: 'checkbox', value: '2' },
      ]}
      onChange={() => console.log('change')}
    />
  );
};

<Render />;
```

import React from 'react';
import CheckboxT<PERSON><PERSON><PERSON><PERSON>ield from '../CheckboxTileGroupField';
import { takeScreenshot } from '../../utils/screenshotTestUtils';

describe('CheckboxTileGroupField', () => {
  test('DefaultWithRequired', async ({ task }) => {
    await takeScreenshot(
      <CheckboxTileGroupField
        label="Default with required"
        required={true}
        defaultValue={['2']}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('DefaultFocus', async ({ task }) => {
    await takeScreenshot(
      <div style={{ padding: '8px' }}>
        <CheckboxTileGroupField
          label="Focus"
          defaultValue={['2']}
          options={[
            { title: 'checkbox', value: '1' },
            { title: 'checkbox', value: '2' },
          ]}
        />
      </div>,
      task,
      {
        interactionSelector: 'label[data-part="root"]:nth-child(2)',
        interactionType: 'focus',
      },
    );
  });

  test('WithItemsPerRow', async ({ task }) => {
    await takeScreenshot(
      <div style={{ width: '500px' }}>
        <CheckboxTileGroupField
          label="Items per row"
          itemsPerRow={4}
          defaultValue={['2']}
          options={[
            { title: 'checkbox', value: '1' },
            { title: 'checkbox', value: '2' },
            { title: 'checkbox', value: '3' },
            { title: 'checkbox', value: '4' },
            { title: 'checkbox', value: '5' },
            { title: 'checkbox', value: '6' },
          ]}
        />
      </div>,
      task,
    );
  });

  test('WithDescription', async ({ task }) => {
    await takeScreenshot(
      <CheckboxTileGroupField
        label="With description"
        defaultValue={['2']}
        options={[
          { title: 'checkbox', description: 'This is a description.', value: '1' },
          { title: 'checkbox', description: 'This is a description.\nThis is a description.', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('Disabled', async ({ task }) => {
    await takeScreenshot(
      <CheckboxTileGroupField
        label="Disabled"
        disabled={true}
        defaultValue={['2']}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasError', async ({ task }) => {
    await takeScreenshot(
      <CheckboxTileGroupField
        label="Error"
        errorMessage="error error error error"
        defaultValue={['2']}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
      task,
    );
  });

  test('HasErrorFocus', async ({ task }) => {
    await takeScreenshot(
      <CheckboxTileGroupField
        label="Error focus"
        errorMessage="error error error error"
        defaultValue={['2']}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
      task,
      {
        interactionSelector: 'label[data-part="root"]:nth-child(2)',
        interactionType: 'focus',
      },
    );
  });
});

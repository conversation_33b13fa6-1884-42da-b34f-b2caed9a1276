import React from 'react';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import { render } from '../../utils/testUtils';
import CheckboxTileGroupField from '../CheckboxTileGroupField';

describe('CheckboxTileGroupField', () => {
  test('renders without crashing with required, description, and check user interaction', async () => {
    const { queryAllByText, container } = render(
      <CheckboxTileGroupField
        label="title"
        required={true}
        defaultValue={['2']}
        options={[
          { title: 'checkbox', description: 'description', value: '1' },
          { title: 'checkbox', description: 'description', value: '2' },
        ]}
      />,
    );
    // check that elements exist in the DOM
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const required = queryAllByText('必須');
    expect(required).toHaveLength(1);
    const title = queryAllByText('checkbox');
    expect(title).toHaveLength(2);
    const description = queryAllByText('description');
    expect(description).toHaveLength(2);
    // Check that interactions are working properly
    expect(title[0].getAttribute('data-state')).toBe('unchecked');
    expect(title[1].getAttribute('data-state')).toBe('checked');
    await userEvent.click(title[0]);
    await waitFor(() => {
      expect(title[0].getAttribute('data-state')).toBe('checked');
      expect(title[1].getAttribute('data-state')).toBe('checked');
    });
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with itemsPerRow prop', async () => {
    const { queryAllByText, container } = render(
      <CheckboxTileGroupField
        label="title"
        itemsPerRow={4}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
    );
    // check that elements exist in the DOM
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const title = queryAllByText('checkbox');
    expect(title).toHaveLength(2);
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with disabled', () => {
    const { queryAllByText, getAllByRole, container } = render(
      <CheckboxTileGroupField
        label="title"
        disabled={true}
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const testElement = queryAllByText('checkbox');
    expect(testElement).toHaveLength(2);
    const testItems = getAllByRole('checkbox');
    testItems.forEach((checkbox) => expect(checkbox).toBeDisabled());
    expect(container).toMatchSnapshot();
  });

  test('renders without crashing with error', () => {
    const { queryAllByText, container } = render(
      <CheckboxTileGroupField
        label="title"
        errorMessage="error error error error"
        options={[
          { title: 'checkbox', value: '1' },
          { title: 'checkbox', value: '2' },
        ]}
      />,
    );
    const label = queryAllByText('title');
    expect(label).toHaveLength(1);
    const errorText = queryAllByText('error error error error');
    expect(errorText).toHaveLength(1);
    const testItems = queryAllByText('checkbox');
    expect(testItems).toHaveLength(2);
    testItems.forEach((item) => expect(item.getAttribute('data-invalid')).not.toBeUndefined());
    expect(container).toMatchSnapshot();
  });
});

import React, { forwardRef, useId } from 'react';
import { CheckboxGroupProps, CheckboxRootBaseProps } from '@ark-ui/react';
import checkHasError from '../utils/checkHasError';
import FieldBase, { CommonFieldBaseProps } from '../FieldBase/FieldBase';
import CheckboxTileGroup from '../CheckboxTile/CheckboxTileGroup';
import CheckboxTile, { CheckboxTileOptions } from '../CheckboxTile/CheckboxTile';

export type CheckboxTileGroupFieldProps = {
  /** Set the maximum number of items per row, each item will take a fraction of the space available */
  itemsPerRow?: number;
  /** Props for Checkbox components, at least one must be provided */
  options: [CheckboxTileOptions, ...CheckboxTileOptions[]];
} & CommonFieldBaseProps &
  Pick<React.ComponentPropsWithoutRef<'input'>, 'onChange'> &
  Pick<CheckboxRootBaseProps, 'disabled' | 'form' | 'id' | 'name'> &
  Pick<CheckboxGroupProps, 'defaultValue' | 'value' | 'onValueChange'>;

const CheckboxTileGroupField = forwardRef<HTMLDivElement, CheckboxTileGroupFieldProps>(
  (
    {
      id,
      name,
      form,
      label,
      onChange,
      showError,
      errorMessage,
      required = false,
      showRequiredIndicator,
      disabled = false,
      itemsPerRow = 1,
      options,
      value,
      defaultValue,
      onValueChange,
      sx,
    },
    ref,
  ) => {
    const hasError = checkHasError(showError, errorMessage);
    const fallbackId = `id-${useId()}`;

    return (
      <FieldBase
        useFieldsetWrapper
        id={id || name || fallbackId}
        label={label}
        showError={hasError}
        errorMessage={errorMessage}
        required={required}
        showRequiredIndicator={showRequiredIndicator}
        sx={sx}
      >
        <CheckboxTileGroup
          style={{ gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)` }}
          disabled={disabled}
          invalid={hasError}
          name={name || fallbackId}
          id={id || name || fallbackId}
          value={value}
          defaultValue={defaultValue}
          onValueChange={onValueChange}
          data-orientation="horizontal"
          ref={ref}
        >
          {options.map((option, idx) => {
            return <CheckboxTile key={`${fallbackId}:${idx}`} form={form} onChange={onChange} {...option} />;
          })}
        </CheckboxTileGroup>
      </FieldBase>
    );
  },
);

CheckboxTileGroupField.displayName = 'CheckboxTileGroupField';

export default CheckboxTileGroupField;

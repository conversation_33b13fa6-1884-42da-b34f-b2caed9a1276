import React from 'react';
import { LinkProps } from 'react-router-dom';

export type LinkBaseProps<ExtraComponentProps> = (
  | React.ComponentPropsWithoutRef<'p'>
  | React.ComponentPropsWithoutRef<'button'>
  | React.ComponentPropsWithoutRef<'a'>
  | LinkProps
) & {
  /** URL for where you want the link to go to, or object if using router links (https://reactrouter.com/web/api/Link/to-object) */
  to?: string;
  /** Choose to use react router */
  useRouter?: boolean;
  linkComponent: React.FC<LinkProps & ExtraComponentProps>;
  anchorComponent: React.FC<React.ComponentPropsWithoutRef<'a'>>;
  otherComponent?: React.FC<Omit<ExtraComponentProps, 'to'>>;
} & ExtraComponentProps;

function LinkBase<ExtraComponentProps>(props: LinkBaseProps<ExtraComponentProps>): React.ReactElement<LinkBaseProps<ExtraComponentProps>> | null {
  const {
    to,
    useRouter = true,
    children,
    linkComponent: LinkComponent,
    anchorComponent: AnchorComponent,
    otherComponent: OtherComponent,
    ...rest
  } = props;

  if (OtherComponent && !to) {
    return <OtherComponent {...(rest as Omit<ExtraComponentProps, 'to'>)}>{children}</OtherComponent>;
  }

  if (to && useRouter) {
    return (
      <LinkComponent to={to} {...(rest as Omit<LinkProps, 'to'> & ExtraComponentProps)}>
        {children}
      </LinkComponent>
    );
  }

  if (typeof to === 'string') {
    return (
      <AnchorComponent href={to} {...(rest as ExtraComponentProps)}>
        {children}
      </AnchorComponent>
    );
  }

  return null;
}

export default LinkBase;

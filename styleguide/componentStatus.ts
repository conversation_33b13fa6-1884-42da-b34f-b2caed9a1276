export type ComponentStatusType = 'Legacy' | 'Beta' | 'Candidate' | 'Stable';

const componentStatus: Record<string, ComponentStatusType> = {
  Accordion: 'Candidate',
  Alert: 'Candidate',
  Badge: 'Candidate',
  Breadcrumb: 'Candidate',
  Button: 'Candidate',
  CheckboxGroupField: 'Candidate',
  CheckboxTileGroupField: 'Candidate',
  DataList: 'Candidate',
  DataTable: 'Candidate',
  DatePickerField: 'Candidate',
  Divider: 'Candidate',
  FlowList: 'Candidate',
  Footer: 'Candidate',
  Heading: 'Candidate',
  Icon: 'Candidate',
  IconButton: 'Candidate',
  Lightbox: 'Candidate',
  Link: 'Candidate',
  Loader: 'Candidate',
  Logo: 'Candidate',
  Message: 'Candidate',
  Modal: 'Candidate',
  Pagination: 'Candidate',
  Popover: 'Candidate',
  RadioGroupField: 'Candidate',
  RadioTileGroupField: 'Candidate',
  SelectField: 'Candidate',
  StatusModal: 'Candidate',
  Stepper: 'Candidate',
  Tabs: 'Candidate',
  Text: 'Candidate',
  TextAreaField: 'Candidate',
  TextField: 'Candidate',
  Toast: 'Candidate',
  UploadField: 'Candidate',
  PasswordField: 'Candidate',
};

export default componentStatus;

import styled from 'styled-components';
import { getSpacingVar } from '../../src/spacing';
import { getColorVar } from '../../src/colors';
import HeroCover from '../images/hero-cover.jpg';
import PeopleTalk from '../images/people-talk.png';
import media from '../../src/Breakpoints/Breakpoints';

export const Wrapper = styled.div`
  background-color: ${getColorVar('grey100')};
`;

export const HeroSection = styled.div`
  background-image: url(${HeroCover});
  background-size: cover;
  background-position-x: center;
  ${media.mediumUp} {
    padding: ${getSpacingVar(12)} ${getSpacingVar(8)};
    margin-bottom: ${getSpacingVar(20)};
  }
  ${media.smallOnly} {
    padding: ${getSpacingVar(10)} ${getSpacingVar(6)};
    margin-bottom: ${getSpacingVar(10)};
  }
`;

export const HeroContent = styled.div`
  ${media.mediumUp} {
    max-width: 40rem;
  }
  ${media.smallOnly} {
    max-width: 25rem;
  }
`;

export const ContentSection = styled.div`
  display: grid;
  ${media.mediumUp} {
    gap: ${getSpacingVar(16)};
    max-width: 60rem;
    margin: ${getSpacingVar(8)} auto;
  }
  ${media.mediumUp} {
    gap: ${getSpacingVar(16)};
    max-width: 50rem;
    margin: ${getSpacingVar(8)} auto;
  }
  ${media.smallOnly} {
    gap: ${getSpacingVar(10)};
    margin: ${getSpacingVar(10)} ${getSpacingVar(6)};
  }
`;

export const Customer = styled.div`
  ${media.mediumUp} {
    display: flex;
    flex-direction: row;
    gap: ${getSpacingVar(6)};
  }
`;

export const CustomerTitle = styled.div`
  ${media.mediumUp} {
    flex: 0 1 40%;
  }
`;

export const CustomerContent = styled.div`
  ${media.mediumUp} {
    flex: 1 0 60%;
  }
`;

export const Contact = styled.div`
  background-color: ${getColorVar('white')};
  padding: ${getSpacingVar(8)};
`;

export const Inquiry = styled.div`
  gap: ${getSpacingVar(6)};
  padding: ${getSpacingVar(10)};
  background-color: ${getColorVar('utilityBackgroundOcean')};
  ${media.mediumUp} {
    display: flex;
    flex-direction: row-reverse;
  }
  ${media.smallOnly} {
    margin: 0 ${getSpacingVar(4)};
  }
`;

export const InquiryImage = styled.div`
  background-image: url(${PeopleTalk});
  background-repeat: no-repeat;
  background-size: contain;
  ${media.mediumUp} {
    flex: 1 0 20%;
    height: 100%;
  }
  ${media.smallOnly} {
    height: 6.25rem;
  }
`;

export const InquiryContent = styled.div`
  ${media.mediumUp} {
    flex: 0 1 80%;
  }
`;

export const InquiryButtons = styled.div`
  display: flex;
  gap: ${getSpacingVar(6)};
  ${media.smallOnly} {
    flex-wrap: wrap;
  }
`;

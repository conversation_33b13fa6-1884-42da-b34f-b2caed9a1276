import React, { useState } from 'react';
import Accordion from '../../src/Accordion';
import Alert from '../../src/Alert';
import Breadcrumb from '../../src/Breadcrumb';
import Button from '../../src/Button';
import Heading from '../../src/Heading';
import Link from '../../src/Link';
import Modal from '../../src/Modal';
import Text from '../../src/Text';
import { useToast } from '../../src/Toast';
import ArrowIcon from '../../src/Icons/ArrowIcon';
import useScreenSize from '../../src/hooks/useScreenSize';
import {
  Wrapper,
  HeroSection,
  HeroContent,
  ContentSection,
  Customer,
  CustomerTitle,
  CustomerContent,
  Contact,
  Inquiry,
  InquiryImage,
  InquiryContent,
  InquiryButtons,
} from './ExamplePage.styles';

const ExamplePage: React.FC = () => {
  const [showAlert, setShowAlert] = useState(true);
  const [open, setOpen] = useState(false);

  const screenSize = useScreenSize();

  const triggerToast = useToast();

  const openModal = () => setOpen(true);
  const closeModal = () => setOpen(false);

  return (
    <Wrapper>
      {showAlert && (
        <Alert
          variant="info"
          text="システムの更新がありました。トーストを確認してください。"
          buttons={[
            {
              buttonText: 'トーストを確認する',
              onClick: () =>
                triggerToast({
                  description: 'こんにちは、トーストです。',
                }),
            },
          ]}
          onClose={() => setShowAlert(false)}
        />
      )}
      <HeroSection>
        <HeroContent>
          <Heading as="h5" color="primary-white" sx={{ 'margin-bottom': 4 }}>
            DESIGN SYSTEM
          </Heading>
          <Heading as="h1" color="primary-white" sx={{ 'margin-bottom': 4 }}>
            デザイン部品の
            <br />
            テストページ
          </Heading>
          <Text color="primary-white">
            このページは、どのようにコンポーネントが共存するかをテストしています。例えば下のボタンをクリッzクするとモーダルを確認できます。
          </Text>
        </HeroContent>
        <Button onClick={openModal} sx={{ 'margin-top': 6 }}>
          React Libraryを見る
        </Button>
      </HeroSection>
      <ContentSection>
        <Customer>
          <CustomerTitle>
            <Heading as="h2">保険商品一覧</Heading>
            <Text sx={{ 'margin-bottom': 6 }}>お客様向けイメージ例です</Text>
          </CustomerTitle>
          <CustomerContent>
            <Accordion headerText="死亡保険">
              死亡保険（生命保険）は、万が一の場合の葬儀費用・墓地等の費用をサポートする保険です。残されたご家族の将来の暮らしを守り、経済的な負担を減らすための保障をします。具体的な保険料につきましては、営業担当者もしくは
              <a href="/">コンタクトセンター</a>までお問い合わせください。
            </Accordion>
            <Accordion headerText="医療保険">
              医療保険は、病気やケガをした際の入院・手術・通院など医療費の負担を軽減する保険です。新しい医療技術や治療に対する保障も視野に入れて、医療技術の進歩に対応できるようにしておくと安心です。
            </Accordion>
            <Accordion headerText="​変額保険">
              変額保険とは、保障を確保しながら、将来のために資産形成ができる保険です。アクサ生命の変額保険は死亡保障を準備しながら積極的な資産形成ができます。
            </Accordion>
          </CustomerContent>
        </Customer>
        <Contact>
          <Heading as="h6" sx={{ 'margin-bottom': 2 }}>
            保険をご検討の方
          </Heading>
          <Heading as="h3" sx={{ 'margin-bottom': 2 }}>
            0120-977-990
          </Heading>
          <Text color="secondary">平日9:00〜18:00／土9:00〜17:00（日・祝日、年末年始の休業日を除く）</Text>
          <Link href="#" icon={<ArrowIcon />}>
            コールバックを予約する
          </Link>
        </Contact>
        <Inquiry>
          <InquiryImage />
          <InquiryContent>
            <Heading color="primary-white" as="h2" sx={{ 'margin-bottom': 4 }}>
              プロジェクトに導入する
            </Heading>
            <Text color="primary-white" sx={{ 'margin-bottom': 4 }}>
              私たちがプロジェクトで実際にデザインシステムを使用していくサポートをします
            </Text>
            <InquiryButtons>
              <Button color="white" fullWidth={screenSize === 'small'}>
                開発者に連絡する
              </Button>
              <Button color="white" variant="outlined" fullWidth={screenSize === 'small'}>
                UXギルドに連絡する
              </Button>
            </InquiryButtons>
          </InquiryContent>
        </Inquiry>
      </ContentSection>
      <Breadcrumb items={[{ text: 'ホーム', to: '/' }, { text: 'デザインシステム', to: '/' }, { text: 'テストページ' }]} sx={{ 'margin-top': 10 }} />
      <Modal
        id="modal"
        open={open}
        setOpen={closeModal}
        title="React Libraryを見る"
        content={
          <div>
            <Text>現在開発中のReact Libraryを見ることができます。</Text>
            <Text>本当にこのテストページを離れますか？</Text>
          </div>
        }
        buttons={[
          {
            text: 'React Libraryを見る',
            onClick: () => window.location.assign('/'),
          },
          {
            text: 'キャンセル',
            onClick: closeModal,
          },
        ]}
      />
    </Wrapper>
  );
};

export default ExamplePage;

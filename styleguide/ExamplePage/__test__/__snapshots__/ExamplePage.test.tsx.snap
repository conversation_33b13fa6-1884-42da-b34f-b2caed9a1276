// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Example Page > renders without crashing 1`] = `
.c25 {
  min-width: 12px;
}

.c25 path {
  fill: currentColor;
}

.c21 {
  border-bottom: 1px solid var(--ajds-color-utility-stroke-light);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-Accordion-margin-top,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-right,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-bottom,var(--ajds-Accordion-margin,0)) var(--ajds-Accordion-margin-left,var(--ajds-Accordion-margin,0));
}

.c22 {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  outline: none;
  overflow: visible;
  text-transform: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--ajds-color-utility-background-white);
  border: none;
  color: var(--ajds-color-interactive-active-primary);
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1;
  margin: var(--ajds-spacing-0) var(--ajds-spacing-auto);
  outline: none !important;
  width: 100%;
  padding: var(--ajds-AccordionHeader-padding);
  font-weight: var(--ajds-AccordionHeader-font-weight);
  font-size: var(--ajds-AccordionHeader-font-size);
}

.c22,
.c22 [type='button'],
.c22 [type='reset'],
.c22 [type='submit'] {
  -webkit-appearance: button;
}

.c22::-moz-focus-inner,
.c22 [type='button']::-moz-focus-inner,
.c22 [type='reset']::-moz-focus-inner,
.c22 [type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

.c22:active,
.c22:focus {
  outline: none;
}

.c22:-moz-focusring,
.c22 [type='button']:-moz-focusring,
.c22 [type='reset']:-moz-focusring,
.c22 [type='submit']:-moz-focusring {
  outline: none;
}

.c22:focus:not(:active) {
  box-shadow: inset 0 0 0 2px var(--ajds-color-interactive-focus-primary);
}

.c23 {
  text-align: left;
  width: 100%;
}

.c24 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: var(--ajds-spacing-2);
  margin-top: 0;
  -webkit-transform: var(--ajds-AccordionHeaderIcon-transform);
  -ms-transform: var(--ajds-AccordionHeaderIcon-transform);
  transform: var(--ajds-AccordionHeaderIcon-transform);
}

.c26 {
  display: grid;
  -webkit-transition: grid-template-rows 0.3s ease-in-out;
  transition: grid-template-rows 0.3s ease-in-out;
  grid-template-rows: var(--ajds-AccordionBody-grid-template-rows);
}

.c28 {
  background-color: var(--ajds-color-utility-background-white);
  padding: var(--ajds-AccordionBodyBg-padding);
}

.c28 > *:first-child {
  margin-top: 0;
}

.c28 > *:last-child {
  margin-bottom: 0;
}

.c27 {
  overflow-wrap: break-word;
  overflow: hidden;
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: var(--ajds-spacing-1);
  background-color: var(--ajds-AlertBase-background-color);
  color: var(--ajds-AlertBase-color);
}

.c4 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c6 {
  text-align: left;
  color: currentcolor;
  padding: var(--ajds-spacing-3) var(--ajds-spacing-2) var(--ajds-spacing-3) var(--ajds-spacing-0);
}

.c8 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c8 svg {
  color: currentColor;
}

.c8:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c8:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c8[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c8:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c8[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c8:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c9 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c9[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
}

.c12 svg {
  height: var(--ajds-spacing-6);
  width: var(--ajds-spacing-6);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  gap: var(--ajds-spacing-0) var(--ajds-spacing-6);
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-around;
  -webkit-justify-content: space-around;
  -ms-flex-pack: space-around;
  justify-content: space-around;
  width: 100%;
  max-width: auto;
}

.c3 {
  max-height: var(--ajds-spacing-12);
  padding: var(--ajds-spacing-3);
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: var(--ajds-spacing-4);
}

.c16 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: var(--ajds-spacing-12);
  background-color: transparent;
  border: none;
  padding: 0;
  outline-offset: -6px;
  border-radius: var(--ajds-radius-lg);
  margin: var(--ajds-IconButtonTargetBase-margin-top,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-right,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-bottom,var(--ajds-IconButtonTargetBase-margin,0)) var(--ajds-IconButtonTargetBase-margin-left,var(--ajds-IconButtonTargetBase-margin,0));
}

.c10 svg {
  color: currentColor;
}

.c10:focus-visible {
  outline: var(--ajds-IconButtonTargetBase-outline-focus-visible);
}

.c10:disabled {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c11 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
  background-color: transparent;
  border: none;
  margin: var(--ajds-IconButtonContainerBase-margin-top,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-right,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-bottom,var(--ajds-IconButtonContainerBase-margin,0)) var(--ajds-IconButtonContainerBase-margin-left,var(--ajds-IconButtonContainerBase-margin,0));
  color: var(--ajds-IconButtonContainerBase-color);
}

.c11 svg {
  color: currentColor;
}

.c11[data-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
  background-color: transparent;
}

.c36 {
  margin: var(--ajds-Breadcrumb-margin-top,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-right,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-bottom,var(--ajds-Breadcrumb-margin,0)) var(--ajds-Breadcrumb-margin-left,var(--ajds-Breadcrumb-margin,0));
}

.c37 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style-type: none;
  row-gap: var(--ajds-spacing-2);
}

.c38 {
  color: var(--ajds-color-character-primary);
}

.c38:not(:last-of-type)::after {
  content: '/';
  margin: 0 8px;
  font-weight: 800;
  color: var(--ajds-color-character-accent);
}

.c30 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c30 svg {
  color: currentColor;
}

.c30:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c31 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: var(--ajds-LinkIcon-margin-left);
  margin-right: var(--ajds-LinkIcon-margin-right);
}

.c31 svg {
  height: var(--ajds-spacing-4);
  width: var(--ajds-spacing-4);
}

.c15 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

.c0 {
  background-color: var(--ajds-color-grey-100);
}

.c13 {
  background-image: url(/styleguide/images/hero-cover.jpg);
  background-size: cover;
  background-position-x: center;
}

.c17 {
  display: grid;
}

.c29 {
  background-color: var(--ajds-color-white);
  padding: var(--ajds-spacing-8);
}

.c32 {
  gap: var(--ajds-spacing-6);
  padding: var(--ajds-spacing-10);
  background-color: var(--ajds-color-utility-background-ocean);
}

.c33 {
  background-image: url(/styleguide/images/people-talk.png);
  background-repeat: no-repeat;
  background-size: contain;
}

.c35 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--ajds-spacing-6);
}

@media (hover:hover) {
  .c22:hover {
    background-color: var(--ajds-color-interactive-hover-grey-transparent);
    color: var(--ajds-color-interactive-hover-primary);
  }
}

@media (hover:hover) {
  .c8:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c8:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c8:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c11:hover {
    color: var(--ajds-IconButtonContainerBase-color-hover);
  }
}

@media (hover:hover) {
  .c11:hover {
    background-color: var(--ajds-IconButtonContainerBase-background-color-hover);
  }
}

@media (hover:hover) {
  .c30:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

@media (min-width:900px) {
  .c13 {
    padding: var(--ajds-spacing-12) var(--ajds-spacing-8);
    margin-bottom: var(--ajds-spacing-20);
  }
}

@media (max-width:899px) {
  .c13 {
    padding: var(--ajds-spacing-10) var(--ajds-spacing-6);
    margin-bottom: var(--ajds-spacing-10);
  }
}

@media (min-width:900px) {
  .c14 {
    max-width: 40rem;
  }
}

@media (max-width:899px) {
  .c14 {
    max-width: 25rem;
  }
}

@media (min-width:900px) {
  .c17 {
    gap: var(--ajds-spacing-16);
    max-width: 60rem;
    margin: var(--ajds-spacing-8) auto;
  }
}

@media (min-width:900px) {
  .c17 {
    gap: var(--ajds-spacing-16);
    max-width: 50rem;
    margin: var(--ajds-spacing-8) auto;
  }
}

@media (max-width:899px) {
  .c17 {
    gap: var(--ajds-spacing-10);
    margin: var(--ajds-spacing-10) var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c18 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    gap: var(--ajds-spacing-6);
  }
}

@media (min-width:900px) {
  .c19 {
    -webkit-flex: 0 1 40%;
    -ms-flex: 0 1 40%;
    flex: 0 1 40%;
  }
}

@media (min-width:900px) {
  .c20 {
    -webkit-flex: 1 0 60%;
    -ms-flex: 1 0 60%;
    flex: 1 0 60%;
  }
}

@media (min-width:900px) {
  .c32 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
  }
}

@media (max-width:899px) {
  .c32 {
    margin: 0 var(--ajds-spacing-4);
  }
}

@media (min-width:900px) {
  .c33 {
    -webkit-flex: 1 0 20%;
    -ms-flex: 1 0 20%;
    flex: 1 0 20%;
    height: 100%;
  }
}

@media (max-width:899px) {
  .c33 {
    height: 6.25rem;
  }
}

@media (min-width:900px) {
  .c34 {
    -webkit-flex: 0 1 80%;
    -ms-flex: 0 1 80%;
    flex: 0 1 80%;
  }
}

@media (max-width:899px) {
  .c35 {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

<div>
  <div
    class="c0"
  >
    <div
      class="c1"
      role="alert"
      style="--ajds-AlertBase-background-color: var(--ajds-color-status-information); --ajds-AlertBase-color: var(--ajds-color-character-primary-white);"
    >
      <div
        class="c2"
      >
        <div
          class="c3"
        >
          <svg
            class="c4"
            fill="none"
            focusable="false"
            style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M11.991 3.00073C7.023 3.00073 3 7.03273 3 12.0007C3 16.9687 7.023 21.0007 11.991 21.0007C16.968 21.0007 21 16.9687 21 12.0007C21 7.03273 16.968 3.00073 11.991 3.00073ZM11.0996 9.52573H12.8996V7.72573H11.0996V9.52573ZM11.0996 16.5007H12.8996V11.1007H11.0996V16.5007ZM4.7998 12.0016C4.7998 15.9796 8.0218 19.2016 11.9998 19.2016C15.9778 19.2016 19.1998 15.9796 19.1998 12.0016C19.1998 8.02361 15.9778 4.80161 11.9998 4.80161C8.0218 4.80161 4.7998 8.02361 4.7998 12.0016Z"
              fill-rule="evenodd"
            />
          </svg>
        </div>
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            システムの更新がありました。トーストを確認してください。
          </p>
          <div
            class="c7"
          >
            <button
              aria-label="トーストを確認する"
              class="c8"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-grey-200); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
              type="button"
            >
              <span
                class="c9"
                data-text-variant="true"
                style="--ajds-ButtonText-text-decoration: underline;"
              >
                トーストを確認する
              </span>
            </button>
          </div>
        </div>
        <button
          aria-label="閉じる"
          class="c10"
          style="--ajds-IconButtonTargetBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white);"
          type="button"
        >
          <div
            class="c11"
            style="--ajds-IconButtonContainerBase-color: var(--ajds-color-interactive-active-white); --ajds-IconButtonContainerBase-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-IconButtonContainerBase-background-color-hover: var(--ajds-color-interactive-hover-white-transparent);"
          >
            <span
              class="c12"
            >
              <svg
                class="c4"
                fill="none"
                focusable="false"
                style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19 6.41073L17.59 5.00073L12 10.5907L6.41 5.00073L5 6.41073L10.59 12.0007L5 17.5907L6.41 19.0007L12 13.4107L17.59 19.0007L19 17.5907L13.41 12.0007L19 6.41073Z"
                />
              </svg>
            </span>
          </div>
        </button>
      </div>
    </div>
    <div
      class="c13"
    >
      <div
        class="c14"
      >
        <h5
          class="c15"
          style="--ajds-Heading-margin-bottom: var(--ajds-spacing-4); --ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          DESIGN SYSTEM
        </h5>
        <h1
          class="c15"
          style="--ajds-Heading-margin-bottom: var(--ajds-spacing-4); --ajds-Heading-font-size: var(--ajds-font-size-2xl-mobile); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          デザイン部品の
          <br />
          テストページ
        </h1>
        <p
          class="c16"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          このページは、どのようにコンポーネントが共存するかをテストしています。例えば下のボタンをクリッzクするとモーダルを確認できます。
        </p>
      </div>
      <button
        aria-label="React Libraryを見る"
        class="c8"
        style="--ajds-Button-margin-top: var(--ajds-spacing-6); --ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-active-white); --ajds-Button-background-color: var(--ajds-color-interactive-active-primary); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-primary); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-primary); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
        type="button"
      >
        <span
          class="c9"
          data-text-variant="false"
          style="--ajds-ButtonText-margin-top: var(--ajds-spacing-6); --ajds-ButtonText-text-decoration: none;"
        >
          React Libraryを見る
        </span>
      </button>
    </div>
    <div
      class="c17"
    >
      <div
        class="c18"
      >
        <div
          class="c19"
        >
          <h2
            class="c15"
            style="--ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
          >
            保険商品一覧
          </h2>
          <p
            class="c16"
            style="--ajds-Text-margin-bottom: var(--ajds-spacing-6); --ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
          >
            お客様向けイメージ例です
          </p>
        </div>
        <div
          class="c20"
        >
          <div
            class="c21"
          >
            <button
              aria-controls=":r1:"
              aria-expanded="false"
              class="c22"
              id=":r0:"
              style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
              type="button"
            >
              <span
                class="c23"
              >
                死亡保険
              </span>
              <span
                class="c24"
              >
                <svg
                  class="c25"
                  fill="none"
                  height="8"
                  viewBox="0 0 12 8"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="c26"
              style="--ajds-AccordionBody-grid-template-rows: 0fr;"
            >
              <div
                class="c27"
              >
                <div
                  aria-labelledby=":r0:"
                  class="c28"
                  id=":r1:"
                  role="region"
                  style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
                >
                  死亡保険（生命保険）は、万が一の場合の葬儀費用・墓地等の費用をサポートする保険です。残されたご家族の将来の暮らしを守り、経済的な負担を減らすための保障をします。具体的な保険料につきましては、営業担当者もしくは
                  <a
                    href="/"
                  >
                    コンタクトセンター
                  </a>
                  までお問い合わせください。
                </div>
              </div>
            </div>
          </div>
          <div
            class="c21"
          >
            <button
              aria-controls=":r3:"
              aria-expanded="false"
              class="c22"
              id=":r2:"
              style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
              type="button"
            >
              <span
                class="c23"
              >
                医療保険
              </span>
              <span
                class="c24"
              >
                <svg
                  class="c25"
                  fill="none"
                  height="8"
                  viewBox="0 0 12 8"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="c26"
              style="--ajds-AccordionBody-grid-template-rows: 0fr;"
            >
              <div
                class="c27"
              >
                <div
                  aria-labelledby=":r2:"
                  class="c28"
                  id=":r3:"
                  role="region"
                  style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
                >
                  医療保険は、病気やケガをした際の入院・手術・通院など医療費の負担を軽減する保険です。新しい医療技術や治療に対する保障も視野に入れて、医療技術の進歩に対応できるようにしておくと安心です。
                </div>
              </div>
            </div>
          </div>
          <div
            class="c21"
          >
            <button
              aria-controls=":r5:"
              aria-expanded="false"
              class="c22"
              id=":r4:"
              style="--ajds-AccordionHeader-padding: var(--ajds-spacing-5) var(--ajds-spacing-4); --ajds-AccordionHeader-font-weight: 600; --ajds-AccordionHeader-font-size: 20px;"
              type="button"
            >
              <span
                class="c23"
              >
                ​変額保険
              </span>
              <span
                class="c24"
              >
                <svg
                  class="c25"
                  fill="none"
                  height="8"
                  viewBox="0 0 12 8"
                  width="12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.59 0.589996L6 5.17L1.41 0.589996L0 2L6 8L12 2L10.59 0.589996Z"
                  />
                </svg>
              </span>
            </button>
            <div
              class="c26"
              style="--ajds-AccordionBody-grid-template-rows: 0fr;"
            >
              <div
                class="c27"
              >
                <div
                  aria-labelledby=":r4:"
                  class="c28"
                  id=":r5:"
                  role="region"
                  style="--ajds-AccordionBodyBg-padding: var(--ajds-spacing-2) var(--ajds-spacing-4) var(--ajds-spacing-6);"
                >
                  変額保険とは、保障を確保しながら、将来のために資産形成ができる保険です。アクサ生命の変額保険は死亡保障を準備しながら積極的な資産形成ができます。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="c29"
      >
        <h6
          class="c15"
          style="--ajds-Heading-margin-bottom: var(--ajds-spacing-2); --ajds-Heading-font-size: var(--ajds-font-size-default); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          保険をご検討の方
        </h6>
        <h3
          class="c15"
          style="--ajds-Heading-margin-bottom: var(--ajds-spacing-2); --ajds-Heading-font-size: var(--ajds-font-size-lg); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
        >
          0120-977-990
        </h3>
        <p
          class="c16"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          平日9:00〜18:00／土9:00〜17:00（日・祝日、年末年始の休業日を除く）
        </p>
        <a
          class="c30"
          href="#"
          style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
        >
          コールバックを予約する
          <span
            class="c31"
            style="--ajds-LinkIcon-margin-left: var(--ajds-spacing-1); --ajds-LinkIcon-margin-right: var(--ajds-spacing-0);"
          >
            <svg
              class="c4"
              fill="none"
              focusable="false"
              style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.7 18.6007L13.4175 17.3407L17.5575 13.2007H3V11.4007H17.5575L13.44 7.26073L14.7 6.00073L21 12.3007L14.7 18.6007Z"
              />
            </svg>
          </span>
        </a>
      </div>
      <div
        class="c32"
      >
        <div
          class="c33"
        />
        <div
          class="c34"
        >
          <h2
            class="c15"
            style="--ajds-Heading-margin-bottom: var(--ajds-spacing-4); --ajds-Heading-font-size: var(--ajds-font-size-xl); --ajds-Heading-color: var(--ajds-color-character-primary-white); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
          >
            プロジェクトに導入する
          </h2>
          <p
            class="c16"
            style="--ajds-Text-margin-bottom: var(--ajds-spacing-4); --ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary-white); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
          >
            私たちがプロジェクトで実際にデザインシステムを使用していくサポートをします
          </p>
          <div
            class="c35"
          >
            <button
              aria-label="開発者に連絡する"
              class="c8"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: var(--ajds-color-interactive-active-white); --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-white); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-grey); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: 100%; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c9"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                開発者に連絡する
              </span>
            </button>
            <button
              aria-label="UXギルドに連絡する"
              class="c8"
              style="--ajds-Button-color: var(--ajds-color-interactive-active-white); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-grey); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-white-transparent); --ajds-Button-border: 2px solid var(--ajds-color-interactive-active-white); --ajds-Button-border-hover: 2px solid var(--ajds-color-interactive-hover-grey); --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-white); --ajds-Button-background-color-disabled: var(--ajds-color-interactive-disabled-light); --ajds-Button-background-color-aria-disabled-true: var(--ajds-color-interactive-disabled-light); --ajds-Button-border-disabled: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-border-aria-disabled-true: 2px solid var(--ajds-color-interactive-disabled-light); --ajds-Button-flex-direction: row; --ajds-Button-width: 100%; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-5); --ajds-Button-outline-offset: var(--ajds-spacing-0-5);"
              type="button"
            >
              <span
                class="c9"
                data-text-variant="false"
                style="--ajds-ButtonText-text-decoration: none;"
              >
                UXギルドに連絡する
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <nav
      aria-label="パンくずリスト"
      class="c36"
      style="--ajds-Breadcrumb-margin-top: var(--ajds-spacing-10);"
    >
      <ul
        class="c37"
      >
        <li
          class="c38"
          role="listitem"
        >
          <a
            class="c30"
            href="/"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            ホーム
          </a>
        </li>
        <li
          class="c38"
          role="listitem"
        >
          <a
            class="c30"
            href="/"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            デザインシステム
          </a>
        </li>
        <li
          aria-current="page"
          class="c38"
          role="listitem"
        >
          <span>
            テストページ
          </span>
        </li>
      </ul>
    </nav>
  </div>
</div>
`;

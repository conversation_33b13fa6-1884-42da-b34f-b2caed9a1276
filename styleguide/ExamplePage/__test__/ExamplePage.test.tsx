/* eslint-disable class-methods-use-this */
import React from 'react';
import { render } from '../../../src/utils/testUtils';
import ExamplePage from '../ExamplePage';

beforeAll(() => {
  global.ResizeObserver = class ResizeObserver {
    observe() {
      // do nothing
    }

    unobserve() {
      // do nothing
    }

    disconnect() {
      // do nothing
    }
  };
});

describe('Example Page', () => {
  test('renders without crashing', () => {
    const { container } = render(<ExamplePage />);

    expect(container).toMatchSnapshot();
  });
});

/** tell typescript that when we import image extension files,
 * the result should be a string (img URL) */
declare module '*.jpg' {
  const content: string;
  export = content;
}
declare module '*.jpeg' {
  const content: string;
  export = content;
}
declare module '*.png' {
  const content: string;
  export = content;
}
declare module '*.gif' {
  const content: string;
  export = content;
}
declare module '*.svg' {
  const content: string;
  export = content;
}

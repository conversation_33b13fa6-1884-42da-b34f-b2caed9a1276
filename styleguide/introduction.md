- [Base](#base)
- [Font family](#font-family)
- [Component Status](#component-status)

## Base

NOTE: We have a set of global style overrides, please take note that these may affect your application's current styles:

- font-size: 16px
- font-family: Noto Sans JP
- line-height: 1.6
- tab-size: 4
- color: characterPrimary (#343c3d)
- Other CSS resets designed to smooth over cross-browser inconsistencies.

For a complete reference of all the styles applied by the BaseStyles, [see the styles](https://github.axa.com/aljdevops/design-system-react/blob/0960b7b3b8e205f207291d6862de33d86380fbc1/src/BaseStyles/BaseStyles.styles.ts).

To use the axa japan design system in your project, you need to wrap your application with the BaseStyles component like this:

```js static
import BaseStyles from '@axa-japan/design-system-react/BaseStyles';

<BaseStyles>
  <App />
</BaseStyles>;
```

## Font family

We use the Noto Sans JP font family. Please include it manually in your configuration. Then it will be automatically set by the BaseStyles.

For example in the head of your html:

```js static
<style>@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap')</style>
```

Or you can download it here: https://fonts.google.com/noto/specimen/Noto+Sans+JP

Or even use this NPM package: https://www.npmjs.com/package/@fontsource/noto-sans-jp

## Component Status

Each component has a status label attached to it among these:

```js noeditor
import Badge from '@axa-japan/design-system-react/Badge';
<>
  <Badge text="Legacy" variant="danger" />
  &nbsp;
  <Badge text="Beta" variant="warning" />
  &nbsp;
  <Badge text="Candidate" variant="info" />
  &nbsp;
  <Badge text="Stable" variant="success" />
</>;
```

- Legacy: Old design components. Should be migrated to new design.
- Beta: New design but can still get breaking changes. Recommended to wait before using.
- Candidate: New design and fixed. Waiting for early adopters.
- Stable: New design, fixed and already used by some applications.

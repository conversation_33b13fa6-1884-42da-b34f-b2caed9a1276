The following colors are available as js variables.

To use the color variables in js, simply import the colors module:

```js static
import colors from '@axa-japan/design-system-react/colors';
```

## Colors

We export colors with semantic naming. Therefore, they should be used in accordance with their naming/purpose.

### Characters

```js noeditor
import ColorSwatch, { ColorSwatchContainer } from './components/ColorSwatch';

<ColorSwatchContainer>
  <ColorSwatch name="characterPrimary" />
  <ColorSwatch name="characterSecondary" />
  <ColorSwatch name="characterPrimaryWhite" />
  <ColorSwatch name="characterSecondaryWhite" />
  <ColorSwatch name="characterAccent" />
</ColorSwatchContainer>;
```

### Status

```js noeditor
import ColorSwatch, { ColorSwatchContainer } from './components/ColorSwatch';

<ColorSwatchContainer>
  <ColorSwatch name="statusSuccess" />
  <ColorSwatch name="statusSuccessLight" />
  <ColorSwatch name="statusDanger" />
  <ColorSwatch name="statusDangerLight" />
  <ColorSwatch name="statusWarning" />
  <ColorSwatch name="statusWarningLight" />
  <ColorSwatch name="statusWarningDark" />
  <ColorSwatch name="statusImportant" />
  <ColorSwatch name="statusImportantLight" />
  <ColorSwatch name="statusInformation" />
  <ColorSwatch name="statusInformationLight" />
  <ColorSwatch name="statusNeutral" />
  <ColorSwatch name="statusNeutralLight" />
</ColorSwatchContainer>;
```

### Utility

```js noeditor
import ColorSwatch, { ColorSwatchContainer } from './components/ColorSwatch';
<>
  <ColorSwatchContainer>
    <ColorSwatch name="utilityBackgroundWhite" />
    <ColorSwatch name="utilityBackgroundLight" />
    <ColorSwatch name="utilityBackgroundOcean" />
    <ColorSwatch name="utilityBackgroundGrey" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="utilityStrokeLight" />
    <ColorSwatch name="utilityStrokeDark" />
    <ColorSwatch name="utilityStrokeWhite" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="utilityOverlay" alias="grey-800 (72%)" />
  </ColorSwatchContainer>
</>;
```

### Interactive

```js noeditor
import ColorSwatch, { ColorSwatchContainer } from './components/ColorSwatch';

<>
  <ColorSwatchContainer>
    <ColorSwatch name="interactiveActivePrimary" />
    <ColorSwatch name="interactiveActiveWhite" />
    <ColorSwatch name="interactiveActiveGrey" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="interactiveHoverPrimary" />
    <ColorSwatch name="interactiveHoverPrimaryTransparent" alias="axa-blue-500 (10%)" />
    <ColorSwatch name="interactiveHoverWhiteTransparent" alias="white (15%)" />
    <ColorSwatch name="interactiveHoverGreyTransparent" alias="grey-800 (5%)" />
    <ColorSwatch name="interactiveHoverGrey" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="interactiveFocusPrimary" />
    <ColorSwatch name="interactiveFocusWhite" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="interactiveVisitedOcean" />
    <ColorSwatch name="interactiveVisitedGrey" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="interactiveDisabledDark" />
    <ColorSwatch name="interactiveDisabledLight" />
  </ColorSwatchContainer>
  <ColorSwatchContainer>
    <ColorSwatch name="interactivePlaceholder" />
  </ColorSwatchContainer>
</>;
```

## Primitive colors

Those colors are only used internally to define the semantic colors.

```js noeditor
import ColorSwatch, { ColorSwatchContainer } from './components/ColorSwatch';
<>
  <ColorSwatchContainer>
    <ColorSwatch name="white" />
    <ColorSwatch name="grey100" />
    <ColorSwatch name="grey200" />
    <ColorSwatch name="grey300" />
    <ColorSwatch name="grey400" />
    <ColorSwatch name="grey500" />
    <ColorSwatch name="grey600" />
    <ColorSwatch name="grey700" />
    <ColorSwatch name="grey800" />
    <ColorSwatch name="grey900" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="axaBlue100" />
    <ColorSwatch name="axaBlue200" />
    <ColorSwatch name="axaBlue300" />
    <ColorSwatch name="axaBlue400" />
    <ColorSwatch name="axaBlue500" />
    <ColorSwatch name="axaBlue600" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="red50" />
    <ColorSwatch name="red100" />
    <ColorSwatch name="red200" />
    <ColorSwatch name="red300" />
    <ColorSwatch name="red400" />
    <ColorSwatch name="red500" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="green50" />
    <ColorSwatch name="green100" />
    <ColorSwatch name="green200" />
    <ColorSwatch name="green300" />
    <ColorSwatch name="green400" />
    <ColorSwatch name="green500" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="yellow50" />
    <ColorSwatch name="yellow100" />
    <ColorSwatch name="yellow200" />
    <ColorSwatch name="yellow300" />
    <ColorSwatch name="yellow400" />
    <ColorSwatch name="yellow500" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="sienna50" />
    <ColorSwatch name="sienna100" />
    <ColorSwatch name="sienna200" />
    <ColorSwatch name="sienna300" />
    <ColorSwatch name="sienna400" />
    <ColorSwatch name="sienna500" />
    <ColorSwatch name="sienna600" />
  </ColorSwatchContainer>

  <ColorSwatchContainer>
    <ColorSwatch name="ocean50" />
    <ColorSwatch name="ocean100" />
    <ColorSwatch name="ocean200" />
    <ColorSwatch name="ocean300" />
    <ColorSwatch name="ocean400" />
    <ColorSwatch name="ocean500" />
  </ColorSwatchContainer>
</>;
```

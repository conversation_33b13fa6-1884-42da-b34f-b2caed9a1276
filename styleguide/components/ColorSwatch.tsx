import React from 'react';
import styled from 'styled-components';
import semanticColors, { primitiveColors } from '../../src/colors';

const allColors = { ...semanticColors, ...primitiveColors } as const;

type ColorSwatchProps = {
  name: keyof typeof allColors;
  alias?: string;
};

export const ColorSwatchContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
`;

const SwatchWrapper = styled.div`
  display: flex;
  margin-bottom: 24px;
  flex-direction: column;
`;

const SwatchName = styled.p`
  font-weight: bold;
  margin: 0;
`;

const Swatch = styled.div`
  border: 1px solid ${primitiveColors.grey400};
  display: inline-block;
  height: 75px;
  width: 180px;
`;

const ColorSwatch: React.FC<React.PropsWithChildren<ColorSwatchProps>> = ({ name, alias }) => {
  return (
    <SwatchWrapper>
      <Swatch
        style={{
          backgroundColor: `${allColors[name]}`,
        }}
      />
      <div>
        <SwatchName>{name}</SwatchName>
        {alias && alias}
        {/** Display the corresponding primitive color if its a semantic color */}
        {!alias &&
          Object.keys(semanticColors).includes(name) &&
          Object.keys(primitiveColors).find((color) => primitiveColors[color as keyof typeof primitiveColors] === allColors[name])}
        <p style={{ margin: 0 }}>{allColors[name]}</p>
      </div>
    </SwatchWrapper>
  );
};

export default ColorSwatch;

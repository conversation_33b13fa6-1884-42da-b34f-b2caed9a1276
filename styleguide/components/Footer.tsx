import React from 'react';
import styled from 'styled-components';
import Footer from '../../src/Footer';
import media from '../../src/Breakpoints/Breakpoints';
import { getZIndexVar } from '../../src/zIndex';

const FooterText = styled.p`
  color: #fff;
  ${media.smallOnly} {
    padding-top: 8px;
  }
`;

const StyleguideFooter: React.FC = () => {
  return (
    <Footer style={{ zIndex: getZIndexVar('sticky'), position: 'fixed', left: 0, bottom: 0, right: 0 }}>
      <FooterText>AXA Japan | Design System React</FooterText>
    </Footer>
  );
};

export default StyleguideFooter;

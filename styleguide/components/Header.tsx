import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { getColorVar } from '../../src/colors';
import Header from '../../src/Header';
import HeaderTopNav from '../../src/HeaderTopNav';
import HeaderMainNav from '../../src/HeaderMainNav';
import HeaderMobileNav from '../../src/HeaderMobileNav';
import AxaLogo from '../../src/HeaderLogo/AxaLogo';
import HeaderTopNavLink from '../../src/HeaderTopNavLink';
import Button from '../../src/Button';
import media from '../../src/Breakpoints/Breakpoints';
import { getZIndexVar } from '../../src/zIndex';

const StickyHeader = styled(Header)`
  position: fixed;
  flex-shrink: 0;
  z-index: ${getZIndexVar('header')};
  top: 0;
  left: 0;
  right: 0;
`;

const HeadingText = styled.span`
  color: ${getColorVar('axaBlue400')};
  font-weight: 600;

  ${media.smallOnly} {
    font-size: 18px;
    margin-left: 8px;
  }

  ${media.mediumOnly} {
    font-size: 20px;
    margin-left: 12px;
  }

  ${media.largeUp} {
    font-size: 24px;
    margin-left: 16px;
  }
`;

const HeaderMainNavStyled = styled(HeaderMainNav)`
  margin-top: 0px !important;
  padding: 12px 0 18px 0;
  height: 90px;
`;

const HeaderLogoImage: React.FC = () => {
  return (
    <>
      <AxaLogo /> <HeadingText>AXA Japan | Design System React</HeadingText>
    </>
  );
};

const GitHubIcon = styled.svg`
  fill: currentColor;
  height: 14px;
  margin-right: 8px;
  vertical-align: sub;
  width: 14px;
`;

const Toc = styled.div`
  li {
    display: block;
    margin: 8px 0;
  }
`;

const StyleguideHeader: React.FC<React.PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const onToggleExamplePageClick = () => {
    const togglePath = location.pathname === '/example-page' ? '/' : '/example-page';
    if (togglePath === '/') {
      // If the first loading is on example page, all the links in the design system will be loaded with base /example-page
      // We avoid that with a workaround of reloading the page when going back to the base path
      window.location.assign(togglePath);
    }
    navigate(togglePath);
  };

  return (
    <StickyHeader>
      <HeaderTopNav>
        <Button variant="text" onClick={onToggleExamplePageClick}>
          {location.pathname === '/example-page' ? 'Design System' : 'Example Page'}
        </Button>
        <HeaderTopNavLink to="https://github.axa.com/aljdevops/design-system-react" useRouter={false} target="_blank">
          <GitHubIcon xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
          </GitHubIcon>
          GitHub
        </HeaderTopNavLink>
      </HeaderTopNav>
      <HeaderMainNavStyled
        headerLogoUseRouter={false}
        headerLogoTo="https://pages.github.axa.com/aljdevops/design-system-react/"
        headerLogoImage={<HeaderLogoImage />}
      />
      <HeaderMobileNav
        headerLogoUseRouter={false}
        headerLogoTo="https://pages.github.axa.com/aljdevops/design-system-react/"
        headerLogoImage={<HeaderLogoImage />}
      >
        <Toc>{children}</Toc>
      </HeaderMobileNav>
    </StickyHeader>
  );
};

export default StyleguideHeader;

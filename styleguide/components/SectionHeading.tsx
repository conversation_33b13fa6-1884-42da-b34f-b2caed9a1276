import React from 'react';
import Slot from 'react-styleguidist/lib/client/rsg-components/Slot';
import SectionHeadingRenderer from 'react-styleguidist/lib/client/rsg-components/SectionHeading/SectionHeadingRenderer';
import Badge from '../../src/Badge';
import componentStatus, { ComponentStatusType } from '../componentStatus';

type SectionHeadingProps = {
  children?: React.ReactNode;
  id: string;
  slotName: string;
  slotProps: Record<string, unknown> & { name: string };
  depth: number;
  href?: string;
  deprecated?: boolean;
  pagePerSection?: boolean;
};

const statusMap: Record<ComponentStatusType, 'danger' | 'warning' | 'info' | 'success'> = {
  Legacy: 'danger',
  Beta: 'warning',
  Candidate: 'info',
  Stable: 'success',
};

const SectionHeading: React.FC<SectionHeadingProps> = ({ slotName, slotProps, children, id, href, ...rest }) => {
  const defaultSectionHeading = (
    <SectionHeadingRenderer toolbar={<Slot name={slotName} props={slotProps} />} id={id} href={href} {...rest}>
      {children}
    </SectionHeadingRenderer>
  );

  const isComponentHeading = slotProps.name && Object.keys(componentStatus).includes(slotProps.name);
  if (!isComponentHeading) {
    return defaultSectionHeading;
  }

  const status = componentStatus[slotProps.name];

  return (
    <div>
      {defaultSectionHeading}
      <a href="#component-status">
        <Badge text={status} variant={statusMap[status]} />
      </a>
    </div>
  );
};

export default SectionHeading;

import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import media from '../../src/Breakpoints/Breakpoints';
import Header from './Header';
import Footer from './Footer';
import { primitiveColors } from '../../src/colors';
import BaseStyles from '../../src/BaseStyles';
import ExamplePage from '../ExamplePage/ExamplePage';
import { AccordionHeaderIcon } from '../../src/Accordion';
import AccordionIcon from '../../src/Accordion/AccordionIcon';

const Content = styled.div`
  display: flex;
  position: relative;

  h1 {
    ${media.smallOnly} {
      scroll-margin-top: 75px;
    }
    ${media.mediumOnly} {
      scroll-margin-top: 111px;
    }
    ${media.largeUp} {
      scroll-margin-top: 148px;
    }
  }
`;

const Main = styled.main`
  border-left: 1px solid ${primitiveColors.grey400};
  flex: 1;
  overflow-x: scroll;
  margin: 135px 0 20px 215px;

  ::-webkit-scrollbar {
    display: none;
  }

  ${media.smallOnly} {
    padding: 14px;
  }

  ${media.mediumOnly} {
    padding: 40px;
  }

  ${media.mediumDown} {
    margin-left: 0;
    margin-top: 70px;
  }

  ${media.largeUp} {
    padding: 40px;
  }
`;

const Toc = styled.div`
  margin-bottom: 71px;
  margin-top: 143px;
  overflow: auto;
  // Height of the ToC is 100% - height of the contents above it
  height: calc(100% - 206px);
  width: 215px;
  position: fixed;

  ${media.mediumDown} {
    display: none;
  }
  a,
  div {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    color: #000;
    text-decoration: none;
    transition: background-color 0.2s;
    justify-content: space-between;
    &:hover {
      background-color: #f0f0f0;
    }
  }
`;

const SectionDropdownIcon = styled.span`
  margin-right: 8px;
`;

const SectionComponentList = styled.ul`
  padding-left: 12px;
  margin-top: 4px;
  list-style-type: none;
`;

const BackToTopButton = styled.button`
  position: fixed;
  bottom: 40px;
  right: 20px;
  background-color: ${primitiveColors.grey300};
  border: none;
  cursor: pointer;
  height: 30px;
  width: 30px;
  border-radius: 50%;

  span {
    margin: 0;
  }

  svg {
    color: ${primitiveColors.grey500};
  }

  ${media.smallOnly} {
    bottom: 60px;
  }

  ${media.mediumOnly} {
    bottom: 50px;
  }
`;

type Component = {
  href: string;
  visibleName: string;
};

type Section = {
  name: string;
  content?: string;
  components?: Component[];
  sectionDepth?: number;
  disable?: boolean;
  href?: string;
};

type StyleGuideRendererProps = {
  hasSidebar: boolean;
  toc: React.ReactElement;
};

const StyleGuideRenderer: React.FC<React.PropsWithChildren<StyleGuideRendererProps>> = (props) => {
  const { children, toc, hasSidebar } = props;

  const [openSections, setOpenSections] = useState<string[]>([]);

  const scrollableContentRef = useRef<HTMLDivElement | null>(null); // Provide initial value

  const handleBackToTop = () => {
    scrollableContentRef.current?.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const toggleSection = (sectionName: string) => {
    setOpenSections((prevOpenSections) => {
      if (prevOpenSections.includes(sectionName)) {
        return prevOpenSections.filter((section) => section !== sectionName);
      }
      return [...prevOpenSections, sectionName];
    });
  };

  const isSectionOpen = (sectionName: string) => {
    return openSections.includes(sectionName);
  };

  const renderSection = (section: Section) => {
    const hasComponents = section.components && section.components.length > 0;
    const isClickable = hasComponents; // Check if the section has components or content

    if (isClickable) {
      return (
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div onClick={() => toggleSection(section.name)}>
          {section.name}
          <SectionDropdownIcon>
            <AccordionHeaderIcon data-is-open={isSectionOpen(section.name)}>
              <AccordionIcon />
            </AccordionHeaderIcon>
          </SectionDropdownIcon>
        </div>
      );
    }
    return <a href={section.href}>{section.name}</a>;
  };

  return (
    <BrowserRouter>
      <BaseStyles>
        <Header>{toc}</Header>
        <Routes>
          <Route
            path="/"
            element={
              <Content>
                {hasSidebar && (
                  <Toc>
                    {toc.props.sections.map((section: Section) => (
                      <React.Fragment key={section.name}>
                        {renderSection(section)}
                        {isSectionOpen(section.name) && section.components && (
                          <SectionComponentList>
                            {section.components.map((component) => {
                              return (
                                <li key={JSON.stringify(component)}>
                                  <a href={component.href}>{component.visibleName}</a>
                                </li>
                              );
                            })}
                          </SectionComponentList>
                        )}
                      </React.Fragment>
                    ))}
                  </Toc>
                )}
                <Main ref={scrollableContentRef}>{children}</Main>
                <BackToTopButton onClick={handleBackToTop}>
                  <AccordionHeaderIcon>
                    <AccordionIcon />
                  </AccordionHeaderIcon>
                </BackToTopButton>
              </Content>
            }
          />
          <Route path="/example-page" element={<ExamplePage />} />
        </Routes>
        <Footer />
      </BaseStyles>
    </BrowserRouter>
  );
};

export default StyleGuideRenderer;

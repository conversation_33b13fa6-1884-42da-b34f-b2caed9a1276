// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Component Request Page > renders without crashing 1`] = `
.c10 {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: left;
  cursor: pointer;
  overflow-wrap: anywhere;
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-default);
  line-height: var(--ajds-line-height-sm);
  -webkit-text-decoration-style: solid;
  text-decoration-style: solid;
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
  margin: var(--ajds-LinkBase-margin-top,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-right,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-bottom,var(--ajds-LinkBase-margin,0)) var(--ajds-LinkBase-margin-left,var(--ajds-LinkBase-margin,0));
  color: var(--ajds-LinkBase-color);
  -webkit-text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  text-decoration-line: var(--ajds-LinkBase-text-decoration-line);
  -webkit-flex-direction: var(--ajds-LinkBase-flex-direction);
  -ms-flex-direction: var(--ajds-LinkBase-flex-direction);
  flex-direction: var(--ajds-LinkBase-flex-direction);
}

.c10 svg {
  color: currentColor;
}

.c10:focus-visible {
  outline: var(--ajds-LinkBase-outline-focus-visible);
}

.c0 {
  border: 1px solid;
  margin-bottom: var(--ajds-spacing-2);
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-4);
  display: none;
  border-radius: var(--ajds-radius-sm);
  margin: var(--ajds-MessageBase-margin-top,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-right,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-bottom,var(--ajds-MessageBase-margin,0)) var(--ajds-MessageBase-margin-left,var(--ajds-MessageBase-margin,0));
  background-color: var(--ajds-MessageBase-background-color);
  border-color: var(--ajds-MessageBase-border-color);
  color: var(--ajds-MessageBase-color);
}

.c0[data-show='true'] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c3 {
  fill: currentColor;
  height: var(--ajds-IconBase-height);
  width: var(--ajds-IconBase-width);
  -webkit-transform: var(--ajds-IconBase-transform);
  -ms-transform: var(--ajds-IconBase-transform);
  transform: var(--ajds-IconBase-transform);
}

.c7 {
  color: var(--ajds-color-character-primary);
  line-height: 1.6;
  margin-top: var(--ajds-spacing-0-5);
}

.c2 {
  max-height: 24px;
  margin: var(--ajds-spacing-1) 0;
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  gap: var(--ajds-spacing-2);
}

.c20 {
  line-height: var(--ajds-line-height);
  margin: var(--ajds-Text-margin-top,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-right,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-bottom,var(--ajds-Text-margin,0)) var(--ajds-Text-margin-left,var(--ajds-Text-margin,0));
  font-size: var(--ajds-Text-font-size);
  color: var(--ajds-Text-color);
  font-weight: var(--ajds-Text-font-weight);
  overflow: var(--ajds-Text-overflow);
  white-space: var(--ajds-Text-white-space);
  text-overflow: var(--ajds-Text-text-overflow);
  text-align: var(--ajds-Text-text-align);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--ajds-spacing-1);
  padding-left: var(--ajds-spacing-1);
}

.c1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: var(--ajds-spacing-2) var(--ajds-spacing-2) var(--ajds-spacing-2) 0;
  gap: var(--ajds-spacing-0-5);
}

.c6 {
  color: var(--ajds-color-character-primary);
  font-weight: var(--ajds-font-weight-bold);
  font-size: var(--ajds-font-size-lg);
  line-height: 1.6;
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: var(--ajds-spacing-0);
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-1);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c14::before {
  color: var(--ajds-color-interactive-active-white);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  content: counter(stepCounter);
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  background-color: var(--ajds-color-interactive-active-primary);
  border-radius: var(--ajds-radius-full);
  width: var(--ajds-spacing-10);
  height: var(--ajds-spacing-10);
}

.c14::after {
  content: '';
  border-left: var(--ajds-spacing-0-5) solid var(--ajds-color-interactive-active-primary);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: var(--ajds-spacing-1);
}

.c16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: var(--ajds-spacing-2);
  margin-bottom: var(--ajds-spacing-14);
}

.c12 {
  counter-increment: stepCounter 1;
  list-style: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: var(--ajds-spacing-3-5);
  column-gap: var(--ajds-spacing-3-5);
  white-space: break-spaces;
}

.c12:last-child > .c13::after {
  height: 0;
  min-height: 0;
  margin: 0;
  border: none;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c12:last-child > .c15 {
  margin-bottom: var(--ajds-spacing-0);
}

.c17 {
  font-size: var(--ajds-font-size-lg);
  font-weight: var(--ajds-font-weight-bold);
  margin-top: var(--ajds-spacing-1);
  line-height: 1.625;
}

.c19 {
  line-height: var(--ajds-line-height-sm);
  font-weight: var(--ajds-font-weight-bold);
  margin: var(--ajds-Heading-margin-top,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-right,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-bottom,var(--ajds-Heading-margin,0)) var(--ajds-Heading-margin-left,var(--ajds-Heading-margin,0));
  font-size: var(--ajds-Heading-font-size);
  color: var(--ajds-Heading-color);
  text-align: var(--ajds-Heading-text-align);
  text-wrap: var(--ajds-Heading-text-wrap);
}

.c21 {
  -webkit-text-decoration: none;
  text-decoration: none;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  outline: none;
  outline-offset: 2px;
  cursor: pointer;
  border-radius: var(--ajds-radius-sm);
  box-sizing: border-box;
  height: var(--ajds-spacing-12);
  vertical-align: middle;
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c21:hover,
.c21:focus {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c21:visited {
  margin: var(--ajds-Button-margin-top,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-right,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-bottom,var(--ajds-Button-margin,0)) var(--ajds-Button-margin-left,var(--ajds-Button-margin,0));
  color: var(--ajds-Button-color);
  background-color: var(--ajds-Button-background-color);
  border: var(--ajds-Button-border);
  -webkit-flex-direction: var(--ajds-Button-flex-direction);
  -ms-flex-direction: var(--ajds-Button-flex-direction);
  flex-direction: var(--ajds-Button-flex-direction);
  width: var(--ajds-Button-width);
  padding: var(--ajds-Button-padding);
  outline-offset: var(--ajds-Button-outline-offset);
}

.c21:visited:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c21:visited:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c21:visited[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c21:visited:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c21:visited[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c21 svg {
  color: currentColor;
}

.c21:focus-visible {
  outline: var(--ajds-Button-outline-focus-visible);
}

.c21:disabled {
  background-color: var(--ajds-Button-background-color-disabled);
}

.c21[aria-disabled="true"] {
  background-color: var(--ajds-Button-background-color-aria-disabled-true);
}

.c21:disabled {
  border: var(--ajds-Button-border-disabled);
}

.c21[aria-disabled="true"] {
  border: var(--ajds-Button-border-aria-disabled-true);
}

.c21[aria-disabled='true'] {
  color: var(--ajds-color-interactive-disabled-dark);
  box-shadow: none;
  cursor: not-allowed;
}

.c22 {
  font-size: var(--ajds-font-size-default);
  font-weight: var(--ajds-font-weight-bold);
  line-height: var(--ajds-line-height-sm);
  text-align: center;
  margin: 0 var(--ajds-spacing-1);
  -webkit-text-decoration: var(--ajds-ButtonText-text-decoration);
  text-decoration: var(--ajds-ButtonText-text-decoration);
}

.c22[data-text-variant='true'] {
  text-underline-offset: var(--ajds-spacing-1);
  -webkit-text-decoration-thickness: var(--ajds-spacing-px);
  text-decoration-thickness: var(--ajds-spacing-px);
}

.c18 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  gap: var(--ajds-spacing-2);
  border-radius: var(--ajds-radius-sm);
  padding: var(--ajds-spacing-6);
  margin: var(--ajds-spacing-7) 0;
  box-shadow: var(--ajds-shadow-small);
}

.c8 {
  list-style-type: decimal;
  padding-left: 20px;
  font-weight: bold;
}

.c8 li:nth-child(4) {
  list-style-type: none;
}

.c9 {
  font-weight: normal;
}

.c9 li {
  list-style-type: disc;
  margin-left: 30px;
}

@media (hover:hover) {
  .c10:hover {
    color: var(--ajds-LinkBase-color-hover);
  }
}

@media (max-width:599px) {
  .c14::before {
    width: var(--ajds-spacing-8);
    height: var(--ajds-spacing-8);
    font-size: var(--ajds-font-size-default);
  }
}

@media (max-width:599px) {
  .c14::after {
    min-height: 70px;
  }
}

@media (max-width:599px) {
  .c16 {
    margin-bottom: var(--ajds-spacing-12);
  }
}

@media (max-width:599px) {
  .c12 {
    -webkit-column-gap: var(--ajds-spacing-3);
    column-gap: var(--ajds-spacing-3);
  }
}

@media (max-width:599px) {
  .c17 {
    margin-top: var(--ajds-spacing-0-5);
    font-size: var(--ajds-font-size-default);
  }
}

@media (hover:hover) {
  .c21:visited:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c21:visited:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c21:visited:hover {
    border: var(--ajds-Button-border-hover);
  }
}

@media (hover:hover) {
  .c21:hover {
    color: var(--ajds-Button-color-hover);
  }
}

@media (hover:hover) {
  .c21:hover {
    background-color: var(--ajds-Button-background-color-hover);
  }
}

@media (hover:hover) {
  .c21:hover {
    border: var(--ajds-Button-border-hover);
  }
}

<div>
  <div
    class="c0"
    data-show="true"
    role="alert"
    style="--ajds-MessageBase-margin-top: var(--ajds-spacing-20); --ajds-MessageBase-margin-bottom: var(--ajds-spacing-20); --ajds-MessageBase-background-color: var(--ajds-color-status-warning-light); --ajds-MessageBase-border-color: var(--ajds-color-status-warning); --ajds-MessageBase-color: var(--ajds-color-status-warning-dark);"
  >
    <div
      class="c1"
    >
      <div
        class="c2"
      >
        <svg
          class="c3"
          fill="none"
          focusable="false"
          style="--ajds-IconBase-height: var(--ajds-spacing-6); --ajds-IconBase-width: var(--ajds-spacing-6); --ajds-IconBase-transform: rotate(0deg);"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M12 4.00098L2.5 20.001H21.5L12 4.00098ZM11.1367 14.1063H12.864V10.7378H11.1367V14.1063ZM11.1367 17.4747H12.864V15.7904H11.1367V17.4747Z"
            fill-rule="evenodd"
          />
        </svg>
      </div>
      <div
        class="c4"
      >
        <div
          class="c5"
        >
          <p
            class="c6"
          >
            Check points before requesting
          </p>
          <div
            class="c7"
          >
            Before jumping straight into requesting new components, we ask that you have checked over these alternatives.
            <ul
              class="c8"
            >
              <li>
                Does it already exist in the AXA Japan Design System?
              </li>
              <li>
                Are there alternative solutions?
              </li>
              <li>
                Is the component WIP or planned to be made by the Design System team?
              </li>
              <li>
                <ul
                  class="c9"
                >
                  View the
                  <a
                    class="c10"
                    href="https://confluence.axa.com/confluence/pages/viewpage.action?pageId=446590342"
                    style="--ajds-LinkBase-margin-left: var(--ajds-spacing-1); --ajds-LinkBase-margin-right: var(--ajds-spacing-1); --ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
                  >
                    Component Status
                  </a>
                  to see the current timeline.
                  <li>
                    If the current timeline does not work for your team, please reach out and discuss with the Design System team.
                  </li>
                  <li>
                    If the timeline works, please wait for the release.
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ul
    class="c11"
  >
    <li
      class="c12"
    >
      <div
        class="c13 c14"
      />
      <div
        class="c15 c16"
      >
        <h3
          class="c17"
        >
          Request component to the Design System team
        </h3>
        <div
          class="c18"
        >
          <h3
            class="c19"
            style="--ajds-Heading-font-size: var(--ajds-font-size-lg); --ajds-Heading-color: var(--ajds-color-character-primary); --ajds-Heading-text-align: start; --ajds-Heading-text-wrap: wrap;"
          >
            Component Request Form
          </h3>
          <p
            class="c20"
            style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
          >
            This is specifically used for requesting components. For design related inquiries please reach out to the UX guild.
          </p>
          <a
            aria-label="SUBMIT FORM"
            class="c21"
            href="https://forms.office.com/Pages/ResponsePage.aspx?id=zDhrOWWqK0m7Dj2U7SWpe2l1GPPifB1Esw2LQRbBQqBURDk2Sk44WUNUTkZPSFcySU4wNUVNUFI4Ti4u"
            style="--ajds-Button-color: var(--ajds-color-interactive-active-primary); --ajds-Button-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-Button-background-color: transparent; --ajds-Button-background-color-hover: var(--ajds-color-interactive-hover-primary-transparent); --ajds-Button-background-color-disabled: transparent; --ajds-Button-background-color-aria-disabled-true: transparent; --ajds-Button-border: none; --ajds-Button-border-hover: none; --ajds-Button-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-Button-flex-direction: row; --ajds-Button-width: auto; --ajds-Button-padding: var(--ajds-spacing-3) var(--ajds-spacing-1); --ajds-Button-outline-offset: var(--ajds-spacing-0);"
          >
            <span
              class="c22"
              data-text-variant="true"
              style="--ajds-ButtonText-text-decoration: underline;"
            >
              SUBMIT FORM
            </span>
          </a>
        </div>
        <p
          class="c20"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-secondary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          For urgent requests, please reach out to us through email after submitting the request form.
          <br />
          Email: 
          <a
            class="c10"
            href="mailto:<EMAIL>"
            style="--ajds-LinkBase-color: var(--ajds-color-interactive-active-primary); --ajds-LinkBase-color-hover: var(--ajds-color-interactive-hover-primary); --ajds-LinkBase-outline-focus-visible: 2px solid var(--ajds-color-interactive-focus-primary); --ajds-LinkBase-text-decoration-line: underline; --ajds-LinkBase-flex-direction: row;"
          >
            <EMAIL>
          </a>
          <br />
          Subject: Design System Component Request
        </p>
      </div>
    </li>
    <li
      class="c12"
    >
      <div
        class="c13 c14"
      />
      <div
        class="c15 c16"
      >
        <h3
          class="c17"
        >
          Design System team will reach out to you
        </h3>
        <p
          class="c20"
          style="--ajds-Text-font-size: var(--ajds-font-size-default); --ajds-Text-color: var(--ajds-color-character-primary); --ajds-Text-font-weight: var(--ajds-font-weight-default); --ajds-Text-overflow: visible; --ajds-Text-white-space: normal; --ajds-Text-text-overflow: clip; --ajds-Text-text-align: start;"
        >
          Once the request has been submitted, the Design System team will reach out to you about next steps or additional discussions.
        </p>
        <ul
          class="c9"
        >
          For any other questions, please feel free to reach out to us:
          <li>
            Antoine Boue
          </li>
          <li>
            Bradley Wong
          </li>
          <li>
            Emiri Vithoontien
          </li>
          <li>
            Jamie Kostaschuk
          </li>
        </ul>
      </div>
    </li>
  </ul>
</div>
`;

import React from 'react';
import Link from '../../src/Link';
import Message from '../../src/Message';
import FlowList from '../../src/FlowList';
import StepOne from './Step/StepOne';
import StepTwo from './Step/StepTwo';
import { CustomListTop, CustomListInside } from './ComponentRequestPage.styles';

const ComponentRequestPage: React.FC = () => {
  return (
    <>
      <Message title="Check points before requesting" status="warning" sx={{ 'margin-top': 20, 'margin-bottom': 20 }} hasCloseButton={false}>
        Before jumping straight into requesting new components, we ask that you have checked over these alternatives.
        <CustomListTop>
          <li>Does it already exist in the AXA Japan Design System?</li>
          <li>Are there alternative solutions?</li>
          <li>Is the component WIP or planned to be made by the Design System team?</li>
          <li>
            <CustomListInside>
              View the
              <Link href="https://confluence.axa.com/confluence/pages/viewpage.action?pageId=446590342" sx={{ 'margin-left': 1, 'margin-right': 1 }}>
                Component Status
              </Link>
              to see the current timeline.
              <li>If the current timeline does not work for your team, please reach out and discuss with the Design System team.</li>
              <li>If the timeline works, please wait for the release.</li>
            </CustomListInside>
          </li>
        </CustomListTop>
      </Message>
      <FlowList
        titleAs="h3"
        steps={[
          {
            title: 'Request component to the Design System team',
            content: <StepOne />,
          },
          {
            title: 'Design System team will reach out to you',
            content: <StepTwo />,
          },
        ]}
      />
    </>
  );
};

export default ComponentRequestPage;

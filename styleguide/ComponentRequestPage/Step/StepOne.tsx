import React from 'react';
import Text from '../../../src/Text';
import Heading from '../../../src/Heading';
import Button from '../../../src/Button';
import { Card } from './StepOne.styles';
import Link from '../../../src/Link';

const formUrl =
  'https://forms.office.com/Pages/ResponsePage.aspx?id=zDhrOWWqK0m7Dj2U7SWpe2l1GPPifB1Esw2LQRbBQqBURDk2Sk44WUNUTkZPSFcySU4wNUVNUFI4Ti4u';

const StepOne: React.FC = () => {
  return (
    <>
      <Card>
        <Heading as="h3">Component Request Form</Heading>
        <Text>This is specifically used for requesting components. For design related inquiries please reach out to the UX guild.</Text>
        <Button variant="text" asChild>
          <a href={formUrl}>SUBMIT FORM</a>
        </Button>
      </Card>
      <Text color="secondary">
        For urgent requests, please reach out to us through email after submitting the request form.
        <br />
        Email: <Link href="mailto:<EMAIL>"><EMAIL></Link>
        <br />
        Subject: Design System Component Request
      </Text>
    </>
  );
};

export default StepOne;

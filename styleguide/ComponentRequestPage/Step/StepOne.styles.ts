import styled from 'styled-components';
import { getSpacingVar } from '../../../src/spacing';
import { getShadowVar } from '../../../src/shadows';
import { getRadiusVar } from '../../../src/radius';

export const Card = styled.div`
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: ${getSpacingVar(2)};
  border-radius: ${getRadiusVar('sm')};
  padding: ${getSpacingVar(6)};
  margin: ${getSpacingVar(7)} 0;
  box-shadow: ${getShadowVar('smallShadow')};
`;

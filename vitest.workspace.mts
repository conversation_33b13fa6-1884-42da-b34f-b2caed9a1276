import { defineWorkspace, configDefaults } from 'vitest/config';

// Extract the base test configuration to reuse
const baseTestConfig = {
  environment: 'jsdom',
  // Increase global test timeout as github actions is slow
  testTimeout: 30000, // Increased from 10000 to 30000
  globals: true,
  setupFiles: ['./vitest.setup.ts'],
  include: ['**/*/*.test.tsx', '**/*/*.test.ts'],
  exclude: [...configDefaults.exclude, '**/*/*.screenshot.build.test.tsx', '**/*/*.screenshot.test.tsx', '**/*/*.build.test.tsx'],
  // Add reporter configuration to help identify hanging tests
  reporter: process.env.CI ? ['verbose', 'json'] : ['default'],
  // Add pool configuration for better CI performance
  pool: 'forks',
  poolOptions: {
    forks: {
      singleFork: process.env.CI === 'true', // Use single fork in CI to avoid resource issues
    },
  },
  coverage: {
    include: ['src'],
    exclude: [
      '**/*/*.screenshot.test.tsx',
      '**/*/*.styles.ts',
      '**/*/index.ts',
      '**/*/*.d.ts',
      '**/*/*.test.tsx',
      '**/*/*.test.ts',
      '**/*/*.build.test.tsx',
      'src/styles',
    ],
    reporter: ['text', 'json', 'html', 'lcov', 'json-summary'],
    all: true,
  },
};

export default defineWorkspace([
  {
    test: {
      ...baseTestConfig,
      name: 'unit',
    },
    define: {
      VITEST_ENV: true,
    },
  },
  {
    test: {
      ...baseTestConfig,
      name: 'build',
      include: ['**/*/*.build.test.tsx', '**/*/*.build.test.ts'],
      exclude: [...configDefaults.exclude, '**/*/*.screenshot.build.test.tsx'],
    },
    define: {
      VITEST_ENV: true,
    },
  },
  {
    test: {
      name: 'mobile',
      globals: true,
      include: ['**/*/*.screenshot.test.tsx'],
      browser: {
        enabled: true,
        headless: true,
        viewport: { width: 375, height: 667 },
        provider: 'webdriverio',
        instances: [
          {
            browser: 'chrome',
          },
        ],
      },
    },
    define: {
      __VIEWPORT__: JSON.stringify('mobile'),
    },
  },
  {
    test: {
      name: 'desktop',
      globals: true,
      include: ['src/{Accordion,Alert,FlowList,Footer,Heading,Lightbox,Modal,StatusModal,Tabs}/**/*.screenshot.test.tsx'],
      browser: {
        enabled: true,
        headless: true,
        viewport: { width: 1280, height: 720 },
        provider: 'webdriverio',
        instances: [
          {
            browser: 'chrome',
          },
        ],
      },
    },
    define: {
      __VIEWPORT__: JSON.stringify('desktop'),
    },
  },
  {
    test: {
      name: 'tablet',
      globals: true,
      include: ['src/{Modal,StatusModal}/**/*.screenshot.test.tsx'],
      browser: {
        enabled: true,
        headless: true,
        viewport: { width: 1024, height: 768 },
        provider: 'webdriverio',
        instances: [
          {
            browser: 'chrome',
          },
        ],
      },
    },
    define: {
      __VIEWPORT__: JSON.stringify('tablet'),
    },
  },
]);

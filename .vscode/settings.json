{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "cSpell.words": ["ajds", "commitlint", "deepmerge", "Lightbox", "tanstack", "testid", "viewports", "yarl"], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "sonarlint.connectedMode.project": {"connectionId": "https-sonarqube-axa-li-jp-dev-int-scarlet-ap-southeast-1-aws-openpaas-axa-cloud-com", "projectKey": "design-system-react"}}
module.exports = {
  extends: ['@axa-japan/eslint-config', '@axa-japan/eslint-config/react', '@axa-japan/eslint-config/prettier'],
  rules: {
    'import/no-extraneous-dependencies': ['error', { devDependencies: true, optionalDependencies: false, peerDependencies: false }],
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      parserOptions: {
        project: ['./tsconfig.json', './tsconfig.jest.json'],
        tsconfigRootDir: './',
      },
      rules: {
        'react/jsx-props-no-spreading': 'off',
        'import/prefer-default-export': 'off',
        'react/prop-types': 'off',
        'react/jsx-boolean-value': 'off',
        '@typescript-eslint/ban-ts-comment': ['error', { 'ts-ignore': 'allow-with-description' }],
        'no-restricted-exports': ['error', { restrictDefaultExports: { defaultFrom: false } }],
      },
    },
    {
      files: ['*.test.*'],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-unsafe-assignment': 'off',
        '@typescript-eslint/ban-ts-comment': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
      },
    },
  ],
};

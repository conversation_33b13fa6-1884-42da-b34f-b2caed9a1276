service: design-system-docs

frameworkVersion: '3'

plugins:
  - serverless-lift

params:
  default:
    domain: axa-japan-design-system-${sls:stage}.preprodaxa.jp
    certificate: arn:aws:acm:us-east-1:031682695384:certificate/0ea41578-3f7c-4900-8813-b7b3c3454305
  # Actual "prod" but in non prod environment
  test:
    domain: axa-japan-design-system.axa.co.jp
    certificate: arn:aws:acm:us-east-1:031682695384:certificate/9e806082-4d46-41b7-b21a-cff031239269
  dev:
    domain: axa-japan-design-system.preprodaxa.jp
    certificate: arn:aws:acm:us-east-1:031682695384:certificate/0ea41578-3f7c-4900-8813-b7b3c3454305

provider:
  name: aws
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'ap-southeast-1'}
  stackTags:
    local.app: ${self:service}
    global.env: ${sls:stage}
    repo.name: design-system-react
    pr.number: ${sls:stage}

constructs:
  landing:
    type: single-page-app
    path: docs
    domain: ${param:domain}
    certificate: ${param:certificate}

import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/**/index.ts', 'src/*.ts'],
  format: ['cjs', 'esm'],
  splitting: false,
  sourcemap: true,
  clean: true,
  minify: true,
  outDir: 'build',
  external: [
    '@ark-ui/react',
    '@chakra-ui/toast',
    '@internationalized/date',
    '@mona-health/react-input-mask',
    '@radix-ui/react-dialog',
    '@radix-ui/react-popover',
    '@radix-ui/react-select',
    '@react-aria/collections',
    '@react-aria/interactions',
    '@react-aria/utils',
    '@react-types/shared',
    'deepmerge',
    'focus-trap',
    'react-aria-components',
    'react-aria',
    'react-router-dom',
    'react-stately',
    'react',
    'styled-components',
    '@tanstack/react-table',
    'yet-another-react-lightbox',
  ],
});

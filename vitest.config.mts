import { defineConfig, configDefaults } from 'vitest/config';

// Extract the base test configuration to reuse
const baseTestConfig = {
  environment: 'jsdom',
  // Increase global test timeout as github actions is slow
  testTimeout: 30000,
  globals: true,
  setupFiles: ['./vitest.setup.ts'],
  include: ['**/*/*.test.tsx', '**/*/*.test.ts'],
  exclude: [...configDefaults.exclude, '**/*/*.screenshot.build.test.tsx', '**/*/*.screenshot.test.tsx', '**/*/*.build.test.tsx'],
  // Use forks instead of threads to prevent CI timeout issues
  pool: 'forks',
  poolOptions: {
    forks: {
      singleFork: true, // Use single fork for stability in CI
    },
  },
  coverage: {
    include: ['src'],
    exclude: [
      '**/*/*.screenshot.test.tsx',
      '**/*/*.styles.ts',
      '**/*/index.ts',
      '**/*/*.d.ts',
      '**/*/*.test.tsx',
      '**/*/*.test.ts',
      '**/*/*.build.test.tsx',
      'src/styles',
    ],
    reporter: ['text', 'json', 'html', 'lcov', 'json-summary'],
    all: true,
  },
};

export default defineConfig({
  test: {
    projects: [
      {
        test: {
          ...baseTestConfig,
          name: 'unit',
        },
        define: {
          VITEST_ENV: true,
        },
      },
      {
        test: {
          ...baseTestConfig,
          name: 'build',
          include: ['**/*/*.build.test.tsx', '**/*/*.build.test.ts'],
          exclude: [...configDefaults.exclude, '**/*/*.screenshot.build.test.tsx'],
        },
        define: {
          VITEST_ENV: true,
        },
      },
      {
        test: {
          name: 'mobile',
          globals: true,
          include: ['**/*/*.screenshot.test.tsx'],
          browser: {
            enabled: true,
            headless: true,
            viewport: { width: 375, height: 667 },
            provider: 'playwright',
            instances: [
              {
                browser: 'chromium',
              },
            ],
          },
        },
        define: {
          __VIEWPORT__: JSON.stringify('mobile'),
        },
      },
      {
        test: {
          name: 'desktop',
          globals: true,
          include: ['src/{Accordion,Alert,FlowList,Footer,Heading,Lightbox,Modal,StatusModal,Tabs}/**/*.screenshot.test.tsx'],
          browser: {
            enabled: true,
            headless: true,
            viewport: { width: 1280, height: 720 },
            provider: 'playwright',
            instances: [
              {
                browser: 'chromium',
              },
            ],
          },
        },
        define: {
          __VIEWPORT__: JSON.stringify('desktop'),
        },
      },
      {
        test: {
          name: 'tablet',
          globals: true,
          include: ['src/{Modal,StatusModal}/**/*.screenshot.test.tsx'],
          browser: {
            enabled: true,
            headless: true,
            viewport: { width: 1024, height: 768 },
            provider: 'playwright',
            instances: [
              {
                browser: 'chromium',
              },
            ],
          },
        },
        define: {
          __VIEWPORT__: JSON.stringify('tablet'),
        },
      },
    ],
  },
});

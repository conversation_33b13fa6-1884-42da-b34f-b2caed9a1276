# must be unique in a given SonarQube instance
sonar.projectKey=design-system-react
# this is the name displayed in the SonarQube UI
sonar.projectName=design-system-react

sonar.exclusions=node_modules/**/*, .github/**/*, .vscode/**/*, coverage/**/*, build/**/*, junit.xml, **/*/*.test.*, **/*/*.screenshot.*

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# Since SonarQube 4.2, this property is optional if sonar.modules is set.
# If not set, SonarQube starts looking for source code from the directory containing
# the sonar-project.properties file.
sonar.sources=./src

# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8

# sonar.coverage.exclusions=src/styles/**/*,jest.*,styleguide.config.js
sonar.dynamicAnalysis=reuseReports
sonar.javascript.lcov.reportPaths=coverage/lcov.info

sonar.eslint.reportPaths=eslint-error-report.json

# using sonar.issue.ignore.multicriteria in properties file is considered a hack, and is unsupported
# the following rules have been disabled in the UI
# typescript:S4624 - Template literals should not be nested
# typescript:S4622 - Union types should not have too many elements
# typescript:S4144 - Functions should not have identical implementations (false positive on styled components)
# typescript:S121 - Control structures should use curly braces
# typescript:S103 - Lines should not be too long

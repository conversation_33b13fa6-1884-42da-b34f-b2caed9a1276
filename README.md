# One AXA Design System React

A React UI component library based on the One AXA Design System.

---

- [Getting started](#getting-started)
- [Documentation](#documentation)
- [Releases](#releases)
- [Contributing](#contributing)

## Getting started

Create .npmrc file, with the necessary credentials to install packages from Artifactory. Then install dependencies:

React 18 is required to use this library:

```
npm install react@18
```

Install the library:

```
npm install @axa-japan/design-system-react
```

If you are using the following dependencies in your project, the following versions should be installed to avoid conflicts. Optional peer dependencies:

```
npm install styled-components@5
npm install @internationalized/date@3
```

If you don't have access to Artifactory, raise a ticket on <PERSON>ra using the following ticket template: _https://jira.axa.com/jira/browse/AOCS-604_

## Documentation

Check out the documentation learn how to setup this library with your project, and see all the components: _https://axa-japan-design-system.axa.co.jp/_

## Releases

For changelog, visit _https://github.axa.com/aljdevops/design-system-react/releases_.

## Contributing

Please follow our [contributing guidelines](/.github/CONTRIBUTING.md)
